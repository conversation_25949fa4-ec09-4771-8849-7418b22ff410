"""
基本使用示例
"""
import os
import sys
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.api.extraction_api import IntelligentExtractionAPI, extract_archive_elements

# 配置日志
logging.basicConfig(level=logging.INFO)

def example_1_basic_usage():
    """示例1: 基本使用"""
    print("=== 示例1: 基本使用 ===")
    
    # 创建API实例
    api = IntelligentExtractionAPI()
    
    try:
        # 提取档案要素
        document_path = "test_document.jpg"  # 替换为实际文档路径
        key_list = ["题名", "责任者", "文号", "发文日期"]
        
        result = api.extract(document_path, key_list)
        
        print("提取结果:")
        for key, value in result['results'].items():
            print(f"  {key}: {value}")
            
    except Exception as e:
        print(f"提取失败: {e}")
    
    finally:
        api.cleanup()

def example_2_with_config():
    """示例2: 使用配置文件"""
    print("\n=== 示例2: 使用配置文件 ===")
    
    # 使用单GPU配置
    config_path = "config/single_gpu_config.yaml"
    
    with IntelligentExtractionAPI(config_path) as api:
        # 检查系统状态
        status = api.get_system_status()
        print(f"GPU模式: {status['device_info']['mode']}")
        print(f"GPU数量: {status['device_info']['gpu_count']}")
        
        # 提取档案要素
        document_path = "test_document.jpg"
        result = api.extract_archive_elements(document_path)
        
        print("档案要素提取结果:")
        for key, value in result['results'].items():
            print(f"  {key}: {value}")

def example_3_async_processing():
    """示例3: 异步处理"""
    print("\n=== 示例3: 异步处理 ===")
    
    with IntelligentExtractionAPI() as api:
        # 提交异步任务
        document_path = "test_document.jpg"
        key_list = ["题名", "责任者"]
        
        task_id = api.extract(document_path, key_list, sync=False)
        print(f"任务已提交: {task_id}")
        
        # 检查任务状态
        while True:
            status = api.get_task_status(task_id)
            print(f"任务状态: {status['status']}")
            
            if status['status'] in ['completed', 'failed']:
                break
            
            import time
            time.sleep(1)
        
        # 获取结果
        if status['status'] == 'completed':
            result = api.wait_for_result(task_id)
            print("异步提取结果:")
            for key, value in result['results'].items():
                print(f"  {key}: {value}")

def example_4_batch_processing():
    """示例4: 批量处理"""
    print("\n=== 示例4: 批量处理 ===")
    
    documents = [
        "document1.jpg",
        "document2.jpg", 
        "document3.jpg"
    ]
    
    with IntelligentExtractionAPI() as api:
        task_ids = []
        
        # 提交批量任务
        for doc_path in documents:
            task_id = api.extract_archive_elements(doc_path, sync=False)
            task_ids.append(task_id)
            print(f"提交任务: {doc_path} -> {task_id}")
        
        # 等待所有任务完成
        results = {}
        for i, task_id in enumerate(task_ids):
            try:
                result = api.wait_for_result(task_id, timeout=60)
                results[documents[i]] = result['results']
                print(f"任务完成: {documents[i]}")
            except Exception as e:
                print(f"任务失败: {documents[i]}, 错误: {e}")
        
        # 输出结果
        print("\n批量处理结果:")
        for doc_path, doc_result in results.items():
            print(f"\n{doc_path}:")
            for key, value in doc_result.items():
                print(f"  {key}: {value}")

def example_5_custom_options():
    """示例5: 自定义选项"""
    print("\n=== 示例5: 自定义选项 ===")
    
    with IntelligentExtractionAPI() as api:
        document_path = "test_document.jpg"
        key_list = ["题名", "责任者", "文号", "发文日期"]
        
        # 自定义选项
        options = {
            'confidence_threshold': 0.8,  # 提高置信度阈值
            'max_retries': 3,             # 增加重试次数
            'enable_multi_strategy': True, # 启用多策略验证
            'domain': 'archive',          # 指定档案领域
            'use_archive_rules': True,    # 使用档案规则
            'enable_date_parsing': True,  # 启用日期解析
        }
        
        result = api.extract(document_path, key_list, options=options)
        
        print("自定义选项提取结果:")
        for key, value in result['results'].items():
            print(f"  {key}: {value}")

def example_6_performance_monitoring():
    """示例6: 性能监控"""
    print("\n=== 示例6: 性能监控 ===")
    
    with IntelligentExtractionAPI() as api:
        # 获取初始状态
        initial_status = api.get_system_status()
        print("初始系统状态:")
        print(f"  GPU模式: {initial_status['device_info']['mode']}")
        print(f"  已加载模型: {len([m for m in initial_status['model_status'].values() if m['loaded']])}")
        
        # 执行一些提取任务
        for i in range(3):
            try:
                result = api.extract_archive_elements("test_document.jpg")
                print(f"任务 {i+1} 完成")
            except Exception as e:
                print(f"任务 {i+1} 失败: {e}")
        
        # 获取性能统计
        final_status = api.get_system_status()
        perf_stats = final_status['performance_stats']
        
        print("\n性能统计:")
        print(f"  总任务数: {perf_stats['total_tasks']}")
        print(f"  成功任务: {perf_stats['successful_tasks']}")
        print(f"  失败任务: {perf_stats['failed_tasks']}")
        print(f"  成功率: {perf_stats['success_rate']:.1f}%")
        print(f"  平均处理时间: {perf_stats['average_processing_time']:.2f}s")
        
        # 显存使用情况
        memory_usage = final_status['device_info']['memory_usage']
        for gpu_id, usage in memory_usage.items():
            print(f"  {gpu_id}: {usage['usage_percent']:.1f}% ({usage['allocated_mb']}MB/{usage['total_mb']}MB)")

def example_7_convenience_function():
    """示例7: 便捷函数"""
    print("\n=== 示例7: 便捷函数 ===")
    
    try:
        # 使用便捷函数快速提取档案要素
        result = extract_archive_elements("test_document.jpg")
        
        print("便捷函数提取结果:")
        for key, value in result['results'].items():
            print(f"  {key}: {value}")
            
    except Exception as e:
        print(f"便捷函数提取失败: {e}")

def main():
    """主函数"""
    print("智能信息提取模块使用示例")
    print("=" * 50)
    
    # 检查测试文档
    test_doc = "test_document.jpg"
    if not os.path.exists(test_doc):
        print(f"警告: 测试文档 {test_doc} 不存在")
        print("请准备一个测试文档，或修改示例中的文档路径")
        return
    
    try:
        # 运行示例
        example_1_basic_usage()
        example_2_with_config()
        # example_3_async_processing()  # 需要实际文档
        # example_4_batch_processing()   # 需要多个文档
        example_5_custom_options()
        example_6_performance_monitoring()
        example_7_convenience_function()
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
