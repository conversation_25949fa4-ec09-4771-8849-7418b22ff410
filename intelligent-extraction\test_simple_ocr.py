#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最简单的PaddleOCR配置
"""
import sys
import time
from pathlib import Path

def test_simple_paddleocr(image_path: str):
    """测试最简单的PaddleOCR配置"""
    print("🧪 测试最简单的PaddleOCR配置")
    print("=" * 50)
    
    image_file = Path(image_path)
    if not image_file.exists():
        print(f"❌ 图像文件不存在: {image_path}")
        return False
    
    # 显示图像信息
    try:
        from PIL import Image
        with Image.open(image_file) as img:
            width, height = img.size
            file_size = image_file.stat().st_size
            print(f"📊 图像信息: {width}×{height}, {file_size/1024:.1f}KB, {img.format}")
    except Exception as e:
        print(f"⚠️ 无法获取图像信息: {e}")
    
    try:
        print("\n📋 步骤1: 导入PaddleOCR")
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR导入成功")
        
        print("\n📋 步骤2: 创建最简单的OCR实例")
        # 使用最简单的配置
        start_time = time.time()
        ocr = PaddleOCR(
            use_angle_cls=True,
            lang='ch'
            # 注意：PaddleOCR 3.0.2使用device参数而不是use_gpu
            # device='cpu' 是默认值，可以不指定
        )
        init_time = time.time() - start_time
        print(f"✅ OCR实例创建成功，耗时: {init_time:.2f}秒")
        
        print("\n📋 步骤3: 执行OCR识别")
        start_time = time.time()
        # PaddleOCR 3.0.2推荐使用predict方法
        try:
            result = ocr.predict(str(image_file))
        except TypeError:
            # 如果predict方法不支持，尝试使用ocr方法
            result = ocr.ocr(str(image_file))
        ocr_time = time.time() - start_time
        print(f"⏱️ OCR识别耗时: {ocr_time:.2f}秒")
        
        if result:
            print(f"✅ OCR识别成功!")
            print(f"📊 结果类型: {type(result)}")
            print(f"📊 结果长度: {len(result) if hasattr(result, '__len__') else 'N/A'}")

            # 调试：打印结果结构
            if hasattr(result, '__len__') and len(result) > 0:
                print(f"📊 第一个元素类型: {type(result[0])}")
                if hasattr(result[0], '__len__'):
                    print(f"📊 第一个元素长度: {len(result[0])}")

            # 尝试解析结果 - 适配PaddleOCR 3.0.2的新格式
            try:
                # 检查是否是新的结果格式
                if isinstance(result, list) and len(result) > 0:
                    if isinstance(result[0], list):
                        # 旧格式: [[bbox, (text, confidence)], ...]
                        text_blocks = result[0]
                        print(f"📝 检测到文本块: {len(text_blocks)}个 (旧格式)")

                        for i, line in enumerate(text_blocks[:5], 1):
                            if len(line) >= 2:
                                bbox = line[0]
                                text_info = line[1]
                                text = text_info[0] if isinstance(text_info, (list, tuple)) else str(text_info)
                                confidence = text_info[1] if isinstance(text_info, (list, tuple)) and len(text_info) > 1 else 0.0
                                print(f"   {i}. '{text}' (置信度: {confidence:.3f})")

                        if len(text_blocks) > 5:
                            print(f"   ... 还有 {len(text_blocks) - 5} 个文本块")

                    elif hasattr(result[0], 'get') or hasattr(result[0], '__dict__'):
                        # 新格式: 可能是对象或字典
                        print(f"📝 检测到文本块: {len(result)}个 (新格式)")

                        for i, item in enumerate(result[:5], 1):
                            # 尝试提取文本和置信度
                            if hasattr(item, 'get'):
                                text = item.get('text', str(item))
                                confidence = item.get('confidence', 0.0)
                            elif hasattr(item, '__dict__'):
                                text = getattr(item, 'text', str(item))
                                confidence = getattr(item, 'confidence', 0.0)
                            else:
                                text = str(item)
                                confidence = 0.0

                            print(f"   {i}. '{text}' (置信度: {confidence:.3f})")

                        if len(result) > 5:
                            print(f"   ... 还有 {len(result) - 5} 个文本块")

                    else:
                        print(f"⚠️ 未知的结果格式，直接显示前几个元素:")
                        for i, item in enumerate(result[:3], 1):
                            print(f"   {i}. {item}")

                return True

            except Exception as parse_error:
                print(f"❌ 结果解析失败: {parse_error}")
                print(f"📊 原始结果: {result}")
                return False
        else:
            print("❌ OCR识别失败: 没有检测到文本")
            return False
            
    except Exception as e:
        print(f"❌ OCR测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python test_simple_ocr.py <图像路径>")
        print("示例: python test_simple_ocr.py G:\\tmp\\Image_00002.jpg")
        return
    
    image_path = sys.argv[1]
    success = test_simple_paddleocr(image_path)
    
    print(f"\n🎯 测试结果:")
    if success:
        print("✅ 简单PaddleOCR配置测试成功!")
        print("💡 这说明PaddleOCR本身是可以工作的")
    else:
        print("❌ 简单PaddleOCR配置测试失败")
        print("💡 可能是PaddleOCR环境或配置问题")


if __name__ == "__main__":
    main()
