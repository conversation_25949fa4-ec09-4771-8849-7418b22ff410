package test.backend.archive;

import com.deeppaas.ollama.OllamaEmbeddingService;
import com.deeppaas.vector.MemoryOptimizedEmbeddingService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;

/**
 * Ollama BGE-M3集成测试
 */
@SpringBootTest
public class OllamaBGEM3Test {
    
    @Autowired
    private OllamaEmbeddingService ollamaService;
    
    @Autowired
    private MemoryOptimizedEmbeddingService optimizedService;
    
    @Test
    public void testOllamaConnection() {
        System.out.println("=== Ollama连接测试 ===\n");
        
        try {
            Map<String, Object> status = ollamaService.getServiceStatus();
            
            System.out.println("Ollama服务状态:");
            for (Map.Entry<String, Object> entry : status.entrySet()) {
                System.out.printf("  %s: %s\n", entry.getKey(), entry.getValue());
            }
            
            String ollamaStatus = (String) status.get("ollamaStatus");
            assert "ONLINE".equals(ollamaStatus) : "Ollama服务应该在线";
            
            System.out.println("\n✓ Ollama连接测试通过");
            
        } catch (Exception e) {
            System.err.printf("✗ Ollama连接测试失败: %s\n", e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testBGEM3Embedding() {
        System.out.println("=== BGE-M3 Embedding测试 ===\n");
        
        // 测试用的档案文本
        List<String> testTexts = Arrays.asList(
            "国务院关于加强档案管理工作的通知",
            "教育部办公厅关于做好2023年档案工作的意见",
            "财政部关于印发《会计档案管理办法》的通知",
            "人力资源社会保障部关于进一步加强人事档案管理的通知",
            "中共中央办公厅 国务院办公厅印发《关于加强新时代档案工作的意见》的通知"
        );
        
        for (String text : testTexts) {
            try {
                System.out.printf("测试文本: %s\n", text);
                
                long startTime = System.currentTimeMillis();
                float[] embedding = ollamaService.getTextEmbedding(text);
                long endTime = System.currentTimeMillis();
                
                System.out.printf("  向量维度: %d\n", embedding.length);
                System.out.printf("  处理时间: %d ms\n", endTime - startTime);
                System.out.printf("  向量范围: [%.4f, %.4f]\n", 
                    Arrays.stream(embedding).min().orElse(0f),
                    Arrays.stream(embedding).max().orElse(0f));
                
                // 验证向量质量
                assert embedding.length == 1024 : "BGE-M3应该返回1024维向量";
                assert !isZeroVector(embedding) : "不应该返回零向量";
                
                System.out.println("  ✓ 测试通过\n");
                
            } catch (Exception e) {
                System.err.printf("  ✗ 测试失败: %s\n", e.getMessage());
            }
        }
    }
    
    @Test
    public void testBatchEmbedding() {
        System.out.println("=== BGE-M3批量Embedding测试 ===\n");
        
        List<String> batchTexts = Arrays.asList(
            "档案管理规定",
            "文件归档要求", 
            "电子档案标准",
            "档案保管期限",
            "档案利用服务"
        );
        
        try {
            System.out.printf("批量处理 %d 个文本\n", batchTexts.size());
            
            long startTime = System.currentTimeMillis();
            Map<String, float[]> results = ollamaService.getBatchEmbeddings(batchTexts);
            long endTime = System.currentTimeMillis();
            
            System.out.printf("批量处理时间: %d ms\n", endTime - startTime);
            System.out.printf("平均每个文本: %.1f ms\n", (double)(endTime - startTime) / batchTexts.size());
            System.out.printf("成功处理: %d/%d\n", results.size(), batchTexts.size());
            
            // 验证结果
            assert results.size() == batchTexts.size() : "应该处理所有文本";
            
            for (Map.Entry<String, float[]> entry : results.entrySet()) {
                float[] embedding = entry.getValue();
                assert embedding.length == 1024 : "每个向量都应该是1024维";
                assert !isZeroVector(embedding) : "不应该有零向量";
            }
            
            System.out.println("✓ 批量处理测试通过");
            
        } catch (Exception e) {
            System.err.printf("✗ 批量处理测试失败: %s\n", e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testOptimizedServiceIntegration() {
        System.out.println("=== 优化服务集成测试 ===\n");
        
        String testText = "国发〔2023〕15号 关于加强档案管理工作的通知";
        
        try {
            System.out.printf("测试文本: %s\n", testText);
            
            // 测试优化服务的策略选择
            long startTime = System.currentTimeMillis();
            float[] embedding = optimizedService.getTextEmbedding(testText);
            long endTime = System.currentTimeMillis();
            
            System.out.printf("处理时间: %d ms\n", endTime - startTime);
            System.out.printf("向量维度: %d\n", embedding.length);
            
            // 获取服务统计
            Map<String, Object> stats = optimizedService.getCacheStats();
            System.out.println("\n服务统计:");
            for (Map.Entry<String, Object> entry : stats.entrySet()) {
                System.out.printf("  %s: %s\n", entry.getKey(), entry.getValue());
            }
            
            // 验证Ollama被优先使用
            Boolean ollamaEnabled = (Boolean) stats.get("ollamaEnabled");
            assert ollamaEnabled != null && ollamaEnabled : "Ollama应该被启用";
            
            System.out.println("\n✓ 优化服务集成测试通过");
            
        } catch (Exception e) {
            System.err.printf("✗ 优化服务集成测试失败: %s\n", e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testCachePerformance() {
        System.out.println("=== 缓存性能测试 ===\n");
        
        String testText = "重复测试文本用于验证缓存效果";
        
        try {
            // 第一次调用（无缓存）
            long startTime1 = System.currentTimeMillis();
            float[] embedding1 = ollamaService.getTextEmbedding(testText);
            long endTime1 = System.currentTimeMillis();
            long firstCallTime = endTime1 - startTime1;
            
            // 第二次调用（有缓存）
            long startTime2 = System.currentTimeMillis();
            float[] embedding2 = ollamaService.getTextEmbedding(testText);
            long endTime2 = System.currentTimeMillis();
            long secondCallTime = endTime2 - startTime2;
            
            System.out.printf("第一次调用时间: %d ms\n", firstCallTime);
            System.out.printf("第二次调用时间: %d ms\n", secondCallTime);
            System.out.printf("缓存加速比: %.1fx\n", (double)firstCallTime / secondCallTime);
            
            // 验证结果一致性
            assert Arrays.equals(embedding1, embedding2) : "缓存结果应该一致";
            assert secondCallTime < firstCallTime : "缓存应该提高速度";
            
            // 获取缓存统计
            Map<String, Object> cacheStats = ollamaService.getCacheStats();
            System.out.println("\n缓存统计:");
            for (Map.Entry<String, Object> entry : cacheStats.entrySet()) {
                System.out.printf("  %s: %s\n", entry.getKey(), entry.getValue());
            }
            
            System.out.println("\n✓ 缓存性能测试通过");
            
        } catch (Exception e) {
            System.err.printf("✗ 缓存性能测试失败: %s\n", e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testSemanticSimilarity() {
        System.out.println("=== 语义相似度测试 ===\n");
        
        // 测试相似文本的向量相似度
        Map<String, String> similarPairs = new HashMap<>();
        similarPairs.put("档案管理办法", "档案管理规定");
        similarPairs.put("发文日期", "文件日期");
        similarPairs.put("责任者", "发文单位");
        
        for (Map.Entry<String, String> pair : similarPairs.entrySet()) {
            try {
                String text1 = pair.getKey();
                String text2 = pair.getValue();
                
                float[] embedding1 = ollamaService.getTextEmbedding(text1);
                float[] embedding2 = ollamaService.getTextEmbedding(text2);
                
                double similarity = calculateCosineSimilarity(embedding1, embedding2);
                
                System.out.printf("'%s' vs '%s': 相似度 = %.3f\n", text1, text2, similarity);
                
                // 相似文本的相似度应该较高
                assert similarity > 0.5 : String.format("相似文本的相似度应该 > 0.5，实际: %.3f", similarity);
                
            } catch (Exception e) {
                System.err.printf("语义相似度测试失败: %s\n", e.getMessage());
            }
        }
        
        System.out.println("\n✓ 语义相似度测试通过");
    }
    
    @Test
    public void testErrorHandling() {
        System.out.println("=== 错误处理测试 ===\n");
        
        try {
            // 测试空文本
            float[] emptyResult = ollamaService.getTextEmbedding("");
            assert emptyResult.length == 1024 : "空文本应该返回默认维度向量";
            
            // 测试null文本
            float[] nullResult = ollamaService.getTextEmbedding(null);
            assert nullResult.length == 1024 : "null文本应该返回默认维度向量";
            
            // 测试超长文本
            String longText = "很长的文本".repeat(1000);
            float[] longResult = ollamaService.getTextEmbedding(longText);
            assert longResult.length == 1024 : "超长文本应该正常处理";
            
            System.out.println("✓ 错误处理测试通过");
            
        } catch (Exception e) {
            System.err.printf("✗ 错误处理测试失败: %s\n", e.getMessage());
            e.printStackTrace();
        }
    }
    
    // 辅助方法
    private boolean isZeroVector(float[] vector) {
        for (float value : vector) {
            if (Math.abs(value) > 1e-6) {
                return false;
            }
        }
        return true;
    }
    
    private double calculateCosineSimilarity(float[] vector1, float[] vector2) {
        if (vector1.length != vector2.length) {
            return 0.0;
        }
        
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;
        
        for (int i = 0; i < vector1.length; i++) {
            dotProduct += vector1[i] * vector2[i];
            norm1 += vector1[i] * vector1[i];
            norm2 += vector2[i] * vector2[i];
        }
        
        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }
        
        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }
}
