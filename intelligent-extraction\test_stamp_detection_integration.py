#!/usr/bin/env python3
"""
印章检测集成测试
测试OCR Worker中集成的印章检测功能
"""

import os
import sys
import json
import time
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.models.ocr_subprocess import OCRSubprocessRunner
from src.services.archive_extraction_service import ArchiveExtractionService
from src.core.model_pool import get_model_pool


def test_ocr_worker_stamp_detection():
    """测试OCR Worker中的印章检测功能"""
    print("=" * 60)
    print("测试1: OCR Worker印章检测功能")
    print("=" * 60)
    
    # 使用测试图像
    test_image = project_root / "Image_00002.jpg"
    if not test_image.exists():
        print(f"❌ 测试图像不存在: {test_image}")
        return False
    
    try:
        # 创建OCR子进程运行器
        ocr_runner = OCRSubprocessRunner(device_id=0)
        
        print(f"📄 测试图像: {test_image}")
        print(f"📏 图像大小: {test_image.stat().st_size / 1024:.1f}KB")
        
        # 测试标准OCR（不带印章检测）
        print("\n🔍 执行标准OCR...")
        start_time = time.time()
        
        standard_result = ocr_runner.recognize(
            image_path=test_image,
            confidence_threshold=0.5,
            use_cpu=True
        )
        
        standard_time = time.time() - start_time
        print(f"⏱️  标准OCR耗时: {standard_time:.2f}s")
        
        if standard_result.get('success'):
            print(f"✅ 标准OCR成功，检测到 {len(standard_result.get('results', []))} 个文本块")
        else:
            print(f"❌ 标准OCR失败: {standard_result.get('error')}")
            return False
        
        # 测试带印章检测的OCR
        print("\n🔍 执行带印章检测的OCR...")
        start_time = time.time()
        
        stamp_result = ocr_runner.recognize_with_stamp_detection(
            image_path=test_image,
            confidence_threshold=0.5,
            use_cpu=True,
            enable_stamp_processing=True,
            stamp_confidence_threshold=0.8
        )
        
        stamp_time = time.time() - start_time
        print(f"⏱️  印章检测OCR耗时: {stamp_time:.2f}s")
        
        if stamp_result.get('success'):
            print(f"✅ 印章检测OCR成功，检测到 {len(stamp_result.get('results', []))} 个文本块")
            
            # 检查印章检测结果
            stamp_detection = stamp_result.get('stamp_detection', {})
            print(f"🔖 印章检测状态:")
            print(f"   - 启用状态: {stamp_detection.get('enabled', False)}")
            print(f"   - 模型可用: {stamp_detection.get('available', False)}")
            print(f"   - 检测到印章: {stamp_detection.get('total_stamps', 0)} 个")
            
            if stamp_detection.get('stamps'):
                print(f"📋 印章详情:")
                for i, stamp in enumerate(stamp_detection['stamps']):
                    print(f"   印章 {i+1}: 置信度 {stamp['confidence']:.3f}")
            
        else:
            print(f"❌ 印章检测OCR失败: {stamp_result.get('error')}")
            return False
        
        # 性能对比
        print(f"\n📊 性能对比:")
        print(f"   标准OCR: {standard_time:.2f}s")
        print(f"   印章检测OCR: {stamp_time:.2f}s")
        print(f"   性能差异: +{stamp_time - standard_time:.2f}s ({((stamp_time/standard_time-1)*100):+.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_service_integration():
    """测试服务层集成"""
    print("\n" + "=" * 60)
    print("测试2: 服务层印章检测集成")
    print("=" * 60)
    
    # 使用测试图像
    test_image = project_root / "Image_00002.jpg"
    if not test_image.exists():
        print(f"❌ 测试图像不存在: {test_image}")
        return False
    
    try:
        # 初始化模型池和服务
        model_pool = get_model_pool()
        model_pool.initialize()
        
        archive_service = ArchiveExtractionService(model_pool)
        
        print(f"📄 测试图像: {test_image}")
        
        # 测试不带印章检测的提取
        print("\n🔍 执行标准档案要素提取...")
        start_time = time.time()
        
        standard_result = await archive_service.extract_from_image(
            image_path=test_image,
            elements=["题名", "责任者", "文号", "发文日期"],
            confidence_threshold=0.5,
            enable_stamp_processing=False
        )
        
        standard_time = time.time() - start_time
        print(f"⏱️  标准提取耗时: {standard_time:.2f}s")
        
        if standard_result.get('success'):
            print(f"✅ 标准提取成功")
            elements = standard_result.get('elements', {})
            print(f"📋 提取要素: {len(elements)} 个")
            for key, value in elements.items():
                print(f"   {key}: {value}")
        else:
            print(f"❌ 标准提取失败: {standard_result.get('error')}")
        
        # 测试带印章检测的提取
        print("\n🔍 执行带印章检测的档案要素提取...")
        start_time = time.time()
        
        stamp_result = await archive_service.extract_from_image(
            image_path=test_image,
            elements=["题名", "责任者", "文号", "发文日期"],
            confidence_threshold=0.5,
            enable_stamp_processing=True,
            stamp_confidence_threshold=0.8
        )
        
        stamp_time = time.time() - start_time
        print(f"⏱️  印章检测提取耗时: {stamp_time:.2f}s")
        
        if stamp_result.get('success'):
            print(f"✅ 印章检测提取成功")
            elements = stamp_result.get('elements', {})
            print(f"📋 提取要素: {len(elements)} 个")
            for key, value in elements.items():
                print(f"   {key}: {value}")
            
            # 检查印章检测信息
            if 'stamp_detection' in stamp_result:
                stamp_info = stamp_result['stamp_detection']
                print(f"🔖 印章检测信息: {stamp_info}")
        else:
            print(f"❌ 印章检测提取失败: {stamp_result.get('error')}")
        
        # 性能对比
        print(f"\n📊 服务层性能对比:")
        print(f"   标准提取: {standard_time:.2f}s")
        print(f"   印章检测提取: {stamp_time:.2f}s")
        print(f"   性能差异: +{stamp_time - standard_time:.2f}s ({((stamp_time/standard_time-1)*100):+.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务层测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始印章检测集成测试")
    print(f"📁 项目根目录: {project_root}")
    
    # 检查测试环境
    test_image_dir = project_root / "uploads"
    if not test_image_dir.exists():
        print(f"❌ 测试图像目录不存在: {test_image_dir}")
        print("请确保测试图像目录存在并包含测试图像")
        return
    
    success_count = 0
    total_tests = 2
    
    # 测试1: OCR Worker印章检测
    if test_ocr_worker_stamp_detection():
        success_count += 1
    
    # 测试2: 服务层集成
    if asyncio.run(test_service_integration()):
        success_count += 1
    
    # 总结
    print("\n" + "=" * 60)
    print("🏁 测试总结")
    print("=" * 60)
    print(f"✅ 成功: {success_count}/{total_tests}")
    print(f"❌ 失败: {total_tests - success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！印章检测集成成功！")
    else:
        print("⚠️  部分测试失败，请检查错误信息")


if __name__ == "__main__":
    main()
