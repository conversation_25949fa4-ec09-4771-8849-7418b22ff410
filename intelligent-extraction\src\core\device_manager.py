"""
设备管理器 - 智能管理单卡/双卡GPU资源
"""
import torch
import logging
from typing import List, Dict, Optional, Any
from dataclasses import dataclass

@dataclass
class GPUInfo:
    """GPU信息"""
    device_id: int
    name: str
    memory_total: int  # MB
    memory_free: int   # MB
    memory_used: int   # MB
    utilization: float # %

class DeviceManager:
    """设备管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.available_gpus = []
        self.device_allocation = {}
        self.current_mode = None  # 'single' or 'dual'
        
        self._detect_gpus()
        self._determine_mode()
        self._allocate_devices()
    
    def _detect_gpus(self):
        """检测可用GPU"""
        if not torch.cuda.is_available():
            raise RuntimeError("CUDA不可用，请检查GPU驱动和PyTorch安装")
        
        gpu_count = torch.cuda.device_count()
        self.logger.info(f"检测到 {gpu_count} 个GPU")
        
        for i in range(gpu_count):
            gpu_info = self._get_gpu_info(i)
            self.available_gpus.append(gpu_info)
            self.logger.info(f"GPU {i}: {gpu_info.name}, 显存: {gpu_info.memory_total}MB")
    
    def _get_gpu_info(self, device_id: int) -> GPUInfo:
        """获取GPU信息"""
        with torch.cuda.device(device_id):
            props = torch.cuda.get_device_properties(device_id)
            memory_total = props.total_memory // (1024 * 1024)  # 转换为MB
            memory_free = torch.cuda.memory_reserved(device_id) // (1024 * 1024)
            memory_used = memory_total - memory_free
            
            return GPUInfo(
                device_id=device_id,
                name=props.name,
                memory_total=memory_total,
                memory_free=memory_free,
                memory_used=memory_used,
                utilization=0.0  # 需要nvidia-ml-py获取实时利用率
            )
    
    def _determine_mode(self):
        """确定运行模式"""
        gpu_count = len(self.available_gpus)
        force_mode = self.config.get('force_mode', None)
        
        if force_mode:
            self.current_mode = force_mode
            self.logger.info(f"强制使用模式: {self.current_mode}")
        elif gpu_count >= 2:
            # 检查是否有足够显存支持双卡模式
            if self._can_support_dual_gpu():
                self.current_mode = 'dual'
                self.logger.info("自动选择双卡模式")
            else:
                self.current_mode = 'single'
                self.logger.info("显存不足，降级到单卡模式")
        else:
            self.current_mode = 'single'
            self.logger.info("单GPU环境，使用单卡模式")
    
    def _can_support_dual_gpu(self) -> bool:
        """检查是否支持双卡模式"""
        if len(self.available_gpus) < 2:
            return False
        
        # 检查前两个GPU的显存
        gpu0_memory = self.available_gpus[0].memory_total
        gpu1_memory = self.available_gpus[1].memory_total
        
        # 双卡模式最低要求：每卡至少4GB
        min_memory_per_gpu = self.config.get('min_memory_per_gpu', 4096)  # MB
        
        return gpu0_memory >= min_memory_per_gpu and gpu1_memory >= min_memory_per_gpu
    
    def _allocate_devices(self):
        """分配设备"""
        if self.current_mode == 'dual':
            self.device_allocation = {
                'document_processing': 0,  # GPU 0: 文档处理
                'llm_inference': 1,        # GPU 1: LLM推理
                'primary': 0,
                'secondary': 1
            }
        else:
            self.device_allocation = {
                'document_processing': 0,  # GPU 0: 所有任务
                'llm_inference': 0,
                'primary': 0,
                'secondary': None
            }
        
        self.logger.info(f"设备分配: {self.device_allocation}")
    
    def get_device(self, task_type: str) -> torch.device:
        """获取指定任务的设备"""
        device_id = self.device_allocation.get(task_type, 0)
        return torch.device(f'cuda:{device_id}')
    
    def get_device_context(self, task_type: str):
        """获取设备上下文管理器"""
        device = self.get_device(task_type)
        return torch.cuda.device(device)
    
    def is_dual_gpu_mode(self) -> bool:
        """是否为双卡模式"""
        return self.current_mode == 'dual'
    
    def get_memory_info(self, device_id: Optional[int] = None) -> Dict[str, Any]:
        """获取显存信息"""
        if device_id is None:
            # 返回所有GPU的信息
            return {f'gpu_{i}': self._get_gpu_memory_info(i) 
                   for i in range(len(self.available_gpus))}
        else:
            return self._get_gpu_memory_info(device_id)
    
    def _get_gpu_memory_info(self, device_id: int) -> Dict[str, int]:
        """获取单个GPU的显存信息"""
        with torch.cuda.device(device_id):
            memory_allocated = torch.cuda.memory_allocated(device_id) // (1024 * 1024)
            memory_reserved = torch.cuda.memory_reserved(device_id) // (1024 * 1024)
            memory_total = torch.cuda.get_device_properties(device_id).total_memory // (1024 * 1024)
            
            return {
                'allocated': memory_allocated,
                'reserved': memory_reserved,
                'total': memory_total,
                'free': memory_total - memory_reserved
            }
    
    def clear_cache(self, device_id: Optional[int] = None):
        """清理GPU缓存"""
        if device_id is None:
            # 清理所有GPU
            for i in range(len(self.available_gpus)):
                with torch.cuda.device(i):
                    torch.cuda.empty_cache()
        else:
            with torch.cuda.device(device_id):
                torch.cuda.empty_cache()
        
        self.logger.info("GPU缓存已清理")
    
    def monitor_memory_usage(self) -> Dict[str, Any]:
        """监控显存使用情况"""
        usage_info = {}
        
        for i, gpu in enumerate(self.available_gpus):
            memory_info = self._get_gpu_memory_info(i)
            usage_percent = (memory_info['allocated'] / memory_info['total']) * 100
            
            usage_info[f'gpu_{i}'] = {
                'name': gpu.name,
                'usage_percent': usage_percent,
                'allocated_mb': memory_info['allocated'],
                'total_mb': memory_info['total'],
                'status': 'warning' if usage_percent > 80 else 'normal'
            }
        
        return usage_info
    
    def get_optimal_batch_size(self, task_type: str, base_batch_size: int = 4) -> int:
        """根据显存情况获取最优批处理大小"""
        device_id = self.device_allocation.get(task_type, 0)
        memory_info = self._get_gpu_memory_info(device_id)
        
        # 根据可用显存调整批处理大小
        free_memory_gb = memory_info['free'] / 1024
        
        if free_memory_gb > 4:
            return base_batch_size
        elif free_memory_gb > 2:
            return max(1, base_batch_size // 2)
        else:
            return 1
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"DeviceManager(mode={self.current_mode}, gpus={len(self.available_gpus)})"
