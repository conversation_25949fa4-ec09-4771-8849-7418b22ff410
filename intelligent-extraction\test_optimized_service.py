#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的FastAPI服务
"""
import asyncio
import aiohttp
import json
import time
from pathlib import Path
import sys
import os

project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

async def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get("http://localhost:8080/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 健康检查通过: {data}")
                    return True
                else:
                    print(f"❌ 健康检查失败: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False


async def test_archive_extraction():
    """测试档案要素提取"""
    print("\n📄 测试档案要素提取...")
    
    # 检查测试图片
    test_image = project_root / "uploads/125ef0ca-61e6-4cf5-9845-e8b9f3bd1358.jpg"
    if not test_image.exists():
        print("❌ 测试图片不存在: test_image.png")
        return False
    
    async with aiohttp.ClientSession() as session:
        try:
            # 准备文件上传
            data = aiohttp.FormData()
            data.add_field('file', 
                          open(test_image, 'rb'), 
                          filename='test_image.png',
                          content_type='image/png')
            data.add_field('custom_keys', '题名,责任者,文号,发文日期')
            data.add_field('confidence_threshold', '0.5')
            
            start_time = time.time()
            
            async with session.post("http://localhost:8080/extract/archive", 
                                  data=data) as response:
                
                processing_time = time.time() - start_time
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 档案要素提取成功")
                    print(f"⏱️ 处理时间: {processing_time:.2f}秒")
                    print(f"📊 提取结果:")
                    
                    if result.get('success'):
                        elements = result.get('results', {})
                        for key, value in elements.items():
                            print(f"   {key}: {value}")
                        
                        print(f"🔧 服务处理时间: {result.get('processing_time', 0):.2f}秒")
                    else:
                        print(f"❌ 提取失败: {result.get('error')}")
                    
                    return result.get('success', False)
                else:
                    error_text = await response.text()
                    print(f"❌ 档案要素提取失败: {response.status}")
                    print(f"错误详情: {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ 档案要素提取异常: {e}")
            return False


async def test_concurrent_requests():
    """测试并发请求"""
    print("\n🚀 测试并发请求...")
    
    test_image = project_root / "uploads/125ef0ca-61e6-4cf5-9845-e8b9f3bd1358.jpg"
    if not test_image.exists():
        print("❌ 测试图片不存在: test_image.png")
        return False
    
    async def single_request(session, request_id):
        """单个请求"""
        try:
            data = aiohttp.FormData()
            data.add_field('file', 
                          open(test_image, 'rb'), 
                          filename=f'test_image_{request_id}.png',
                          content_type='image/png')
            data.add_field('custom_keys', '题名,责任者')
            data.add_field('confidence_threshold', '0.5')
            
            start_time = time.time()
            
            async with session.post("http://localhost:8080/extract/archive", 
                                  data=data) as response:
                
                processing_time = time.time() - start_time
                
                if response.status == 200:
                    result = await response.json()
                    return {
                        'request_id': request_id,
                        'success': result.get('success', False),
                        'processing_time': processing_time,
                        'service_time': result.get('processing_time', 0)
                    }
                else:
                    return {
                        'request_id': request_id,
                        'success': False,
                        'error': f"HTTP {response.status}",
                        'processing_time': processing_time
                    }
                    
        except Exception as e:
            return {
                'request_id': request_id,
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time if 'start_time' in locals() else 0
            }
    
    # 并发测试
    concurrent_count = 3
    print(f"发起{concurrent_count}个并发请求...")
    
    async with aiohttp.ClientSession() as session:
        start_time = time.time()
        
        tasks = [single_request(session, i+1) for i in range(concurrent_count)]
        results = await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        
        print(f"📊 并发测试结果:")
        print(f"   总耗时: {total_time:.2f}秒")
        
        success_count = 0
        total_processing_time = 0
        total_service_time = 0
        
        for result in results:
            if result['success']:
                success_count += 1
                total_processing_time += result['processing_time']
                total_service_time += result.get('service_time', 0)
                print(f"   请求{result['request_id']}: ✅ {result['processing_time']:.2f}s")
            else:
                print(f"   请求{result['request_id']}: ❌ {result.get('error', '未知错误')}")
        
        print(f"   成功率: {success_count}/{concurrent_count} ({success_count/concurrent_count*100:.1f}%)")
        if success_count > 0:
            print(f"   平均处理时间: {total_processing_time/success_count:.2f}秒")
            print(f"   平均服务时间: {total_service_time/success_count:.2f}秒")
        
        return success_count == concurrent_count


async def test_model_pool_performance():
    """测试模型池性能"""
    print("\n⚡ 测试模型池性能...")
    
    test_image = project_root / "uploads/125ef0ca-61e6-4cf5-9845-e8b9f3bd1358.jpg"
    if not test_image.exists():
        print("❌ 测试图片不存在: test_image.png")
        return False
    
    # 连续请求测试模型复用
    request_count = 5
    times = []
    
    async with aiohttp.ClientSession() as session:
        for i in range(request_count):
            print(f"执行第{i+1}次请求...")
            
            data = aiohttp.FormData()
            data.add_field('file', 
                          open(test_image, 'rb'), 
                          filename=f'test_{i+1}.png',
                          content_type='image/png')
            data.add_field('custom_keys', '题名,责任者')
            data.add_field('confidence_threshold', '0.5')
            
            start_time = time.time()
            
            try:
                async with session.post("http://localhost:8080/extract/archive", 
                                      data=data) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        processing_time = time.time() - start_time
                        service_time = result.get('processing_time', 0)
                        
                        times.append({
                            'request': i+1,
                            'total_time': processing_time,
                            'service_time': service_time,
                            'success': result.get('success', False)
                        })
                        
                        print(f"   ✅ 总时间: {processing_time:.2f}s, 服务时间: {service_time:.2f}s")
                    else:
                        print(f"   ❌ HTTP {response.status}")
                        
            except Exception as e:
                print(f"   ❌ 异常: {e}")
    
    # 分析性能趋势
    if times:
        print(f"\n📈 性能分析:")
        first_time = times[0]['service_time']
        last_time = times[-1]['service_time']
        avg_time = sum(t['service_time'] for t in times) / len(times)
        
        print(f"   首次请求: {first_time:.2f}秒")
        print(f"   最后请求: {last_time:.2f}秒")
        print(f"   平均时间: {avg_time:.2f}秒")
        
        if first_time > last_time:
            improvement = (first_time - last_time) / first_time * 100
            print(f"   性能提升: {improvement:.1f}% (模型预热效果)")
        
        return True
    
    return False


async def main():
    """主测试函数"""
    print("🧪 开始测试优化后的FastAPI服务")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    await asyncio.sleep(2)
    
    # 测试序列
    tests = [
        ("健康检查", test_health_check),
        ("档案要素提取", test_archive_extraction),
        ("并发请求", test_concurrent_requests),
        ("模型池性能", test_model_pool_performance),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = await test_func()
            results[test_name] = result
            print(f"{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results[test_name] = False
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    
    passed = sum(1 for r in results.values() if r)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！服务优化成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步优化")


if __name__ == "__main__":
    asyncio.run(main())
