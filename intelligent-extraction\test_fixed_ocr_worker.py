#!/usr/bin/env python3
"""
测试修复后的OCR Worker - 验证PaddleOCR 3.0.2 API兼容性
"""

import os
import sys
import json
import time
import subprocess
import tempfile
from pathlib import Path

def create_test_config(image_path, use_cpu=True):
    """创建测试配置文件"""
    config = {
        "image_path": str(image_path),
        "use_cpu": use_cpu,
        "use_angle_cls": False,
        "lang": "ch",
        "confidence_threshold": 0.5,
        "det_db_thresh": 0.3,
        "det_db_box_thresh": 0.6,
        "rec_batch_num": 6,
        "gpu_id": 0
    }
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
        return f.name

def test_ocr_worker(image_path, use_cpu=True):
    """测试OCR Worker"""
    print(f"\n🧪 测试OCR Worker - {'CPU' if use_cpu else 'GPU'}模式")
    print(f"📁 图像路径: {image_path}")
    
    # 检查图像文件
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return False
    
    # 创建配置文件
    config_file = create_test_config(image_path, use_cpu)
    print(f"📝 配置文件: {config_file}")
    
    try:
        # 运行OCR Worker
        worker_script = "src/models/ocr_worker.py"
        if not os.path.exists(worker_script):
            print(f"❌ OCR Worker脚本不存在: {worker_script}")
            return False
        
        print("🚀 启动OCR Worker...")
        start_time = time.time()
        
        # 使用subprocess运行OCR worker，指定UTF-8编码
        result = subprocess.run([
            sys.executable, worker_script, config_file
        ], capture_output=True, text=True, encoding='utf-8', errors='replace', timeout=120)  # 2分钟超时
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"⏱️  处理时间: {processing_time:.2f}秒")
        print(f"🔄 返回码: {result.returncode}")
        
        # 分析输出
        if result.stdout and result.stdout.strip():
            print("📤 标准输出:")
            try:
                output_data = json.loads(result.stdout)
                print(json.dumps(output_data, ensure_ascii=False, indent=2))

                if output_data.get('success'):
                    results = output_data.get('results', [])
                    print(f"✅ OCR成功! 检测到 {len(results)} 个文本块")

                    for i, item in enumerate(results[:3]):  # 显示前3个结果
                        print(f"  📝 文本{i+1}: {item.get('text', 'N/A')}")
                        print(f"     置信度: {item.get('confidence', 0):.3f}")

                    return True
                else:
                    print(f"❌ OCR失败: {output_data.get('error', 'Unknown error')}")
                    return False

            except json.JSONDecodeError as e:
                print(f"❌ 输出解析失败: {e}")
                print(f"原始输出: {result.stdout}")
                return False
        else:
            print("⚠️  没有标准输出 - OCR子进程可能卡住或崩溃")
        
        if result.stderr:
            print("📥 调试信息:")
            for line in result.stderr.strip().split('\n'):
                if line.strip():
                    try:
                        debug_data = json.loads(line)
                        if debug_data.get('debug'):
                            print(f"  🔍 {debug_data}")
                    except:
                        print(f"  📄 {line}")
        
        if result.returncode != 0:
            print(f"❌ 进程异常退出，返回码: {result.returncode}")
            return False
            
        return True
        
    except subprocess.TimeoutExpired:
        print("⏰ OCR Worker超时 (2分钟)")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            os.unlink(config_file)
        except:
            pass

def main():
    """主函数"""
    print("🔧 PaddleOCR 3.0.2 API兼容性测试")
    print("=" * 50)
    
    # 检查测试图像
    test_images = [
        "test_processed_image.jpg",
        "test_medium_image.jpg", 
        "create_test_image.py"  # 如果没有图像，先创建
    ]
    
    test_image = None
    for img in test_images:
        if os.path.exists(img) and img.endswith(('.jpg', '.png', '.jpeg')):
            test_image = img
            break
    
    if not test_image:
        print("📸 创建测试图像...")
        # 运行图像创建脚本
        if os.path.exists("create_test_image.py"):
            try:
                subprocess.run([sys.executable, "create_test_image.py"], check=True)
                test_image = "test_medium_image.jpg"
            except:
                print("❌ 无法创建测试图像")
                return
        else:
            print("❌ 找不到测试图像和创建脚本")
            return
    
    print(f"🖼️  使用测试图像: {test_image}")
    
    # 测试CPU模式
    success_cpu = test_ocr_worker(test_image, use_cpu=True)
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    print(f"  CPU模式: {'✅ 成功' if success_cpu else '❌ 失败'}")
    
    if success_cpu:
        print("\n🎉 OCR Worker修复成功!")
        print("✨ PaddleOCR 3.0.2 API兼容性问题已解决")
    else:
        print("\n⚠️  仍存在问题，需要进一步调试")

if __name__ == "__main__":
    main()
