package com.deeppaas.paddleocr.model;

import lombok.Data;
import java.util.ArrayList;
import java.util.List;

/**
 * Embedding响应
 */
@Data
public class EmbeddingResponse {
    private String object = "list";
    private List<EmbeddingData> data = new ArrayList<>();
    private String model;
    private Usage usage = new Usage();
    
    @Data
    public static class EmbeddingData {
        private String object = "embedding";
        private float[] embedding = new float[768]; // 默认768维
        private int index = 0;
    }
    
    @Data
    public static class Usage {
        private int prompt_tokens = 0;
        private int total_tokens = 0;
    }
}
