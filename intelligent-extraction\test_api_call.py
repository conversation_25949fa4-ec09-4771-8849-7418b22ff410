#!/usr/bin/env python3
"""
测试API调用
"""
import requests
import json
import os
from pathlib import Path

def test_api():
    """测试API调用"""
    try:
        # API基础URL
        base_url = "http://localhost:8080"
        
        # 测试健康检查
        print("=== 测试健康检查 ===")
        response = requests.get(f"{base_url}/health")
        print(f"健康检查状态: {response.status_code}")
        if response.status_code == 200:
            print(f"响应: {response.json()}")
        else:
            print(f"错误: {response.text}")
            return False
        
        # 创建测试图像
        print("\n=== 创建测试图像 ===")
        import numpy as np
        import cv2
        import tempfile
        
        # 创建一个包含中文文字的测试图像
        img = np.ones((300, 600, 3), dtype=np.uint8) * 255  # 白色背景
        
        # 添加中文文字（如果支持的话）
        try:
            # 尝试添加英文文字
            cv2.putText(img, 'Archive Document Test', (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
            cv2.putText(img, 'Title: Test Document', (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
            cv2.putText(img, 'Responsible: Test Unit', (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
            cv2.putText(img, 'Doc No: TEST-2025-001', (50, 250), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
        except Exception as e:
            print(f"添加文字时出错: {e}")
        
        # 保存测试图像
        test_image_path = "test_archive_document.jpg"
        cv2.imwrite(test_image_path, img)
        print(f"✅ 创建测试图像: {test_image_path}")
        
        # 测试档案元素提取
        print("\n=== 测试档案元素提取 ===")
        
        with open(test_image_path, 'rb') as f:
            files = {'file': ('test_archive_document.jpg', f, 'image/jpeg')}
            data = {
                'extraction_type': 'archive',
                'confidence_threshold': 0.3  # 降低阈值便于测试
            }
            
            response = requests.post(f"{base_url}/extract/archive", files=files, data=data)
            
        print(f"API响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误响应: {response.text}")
        
        # 清理测试文件
        try:
            os.unlink(test_image_path)
        except:
            pass
            
        return response.status_code == 200
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试API调用...")
    success = test_api()
    
    if success:
        print("\n🎉 API测试成功!")
    else:
        print("\n⚠️ API测试失败，请检查服务状态")
