"""
Web API服务 - 为Java系统提供HTTP接口
"""
import os
import uuid
import asyncio
import time
from typing import Dict, Any, List, Optional
from pathlib import Path

# 配置日志
from ..utils.logger_config import get_logger
from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn

from .extraction_api import IntelligentExtractionAPI
from ..core.model_pool import get_model_pool, cleanup_model_pool
from ..services.archive_extraction_service import ArchiveExtractionService
from ..utils.image_preprocessor import get_image_processor

# 请求/响应模型
class ExtractionRequest(BaseModel):
    """提取请求"""
    key_list: List[str]
    options: Optional[Dict[str, Any]] = None
    sync: bool = True
    # 印章处理参数
    enable_stamp_processing: bool = False
    stamp_confidence_threshold: float = 0.8

class ExtractionResponse(BaseModel):
    """提取响应"""
    success: bool
    task_id: Optional[str] = None
    results: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_time: Optional[float] = None
    ocr_info: Optional[Dict[str, Any]] = None

class TaskStatusResponse(BaseModel):
    """任务状态响应"""
    task_id: str
    status: str
    results: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    progress: Optional[float] = None

class SystemStatusResponse(BaseModel):
    """系统状态响应"""
    status: str
    gpu_info: Dict[str, Any]
    model_status: Dict[str, Any]
    performance_stats: Dict[str, Any]

class ExtractionWebService:
    """信息提取Web服务"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.logger = get_logger(__name__)

        # 初始化FastAPI应用
        self.app = FastAPI(
            title="智能信息提取服务",
            description="为合规审查系统提供档案要素提取服务",
            version="1.0.0"
        )

        # 初始化模型池（常驻内存）
        self.model_pool = get_model_pool(config_path)

        # 初始化提取API
        self.extraction_api = IntelligentExtractionAPI(config_path)

        # 初始化档案要素提取服务
        self.archive_service = ArchiveExtractionService(config_path)

        # 初始化图像预处理器（强制重新初始化以确保使用最新配置）
        preprocessing_config = self.model_pool.config.get('image_preprocessing', {})
        self.image_processor = get_image_processor(preprocessing_config, self.model_pool, force_reinit=True)

        # 文件上传目录
        self.upload_dir = Path("uploads")
        self.upload_dir.mkdir(exist_ok=True)

        # 注册路由和事件处理
        self._register_routes()
        self._register_events()

        self.logger.info("Web服务初始化完成")

    def _register_events(self):
        """注册应用事件处理"""

        @self.app.on_event("startup")
        async def startup_event():
            """应用启动事件"""
            self.logger.info("正在初始化模型池...")
            try:
                # 在后台线程中初始化模型池，避免阻塞启动
                import threading
                init_thread = threading.Thread(target=self.model_pool.initialize)
                init_thread.daemon = True
                init_thread.start()
                self.logger.info("模型池初始化已启动")
            except Exception as e:
                self.logger.error(f"模型池初始化失败: {e}")

        @self.app.on_event("shutdown")
        async def shutdown_event():
            """应用关闭事件"""
            self.logger.info("正在清理资源...")
            try:
                self.model_pool.cleanup()
                cleanup_model_pool()
                self.logger.info("资源清理完成")
            except Exception as e:
                self.logger.error(f"资源清理失败: {e}")

    def _register_routes(self):
        """注册API路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            model_stats = self.model_pool.get_model_stats()
            preprocessing_stats = self.image_processor.get_stats()

            return {
                "status": "healthy",
                "service": "intelligent-extraction",
                "models": model_stats,
                "image_preprocessing": preprocessing_stats,
                "timestamp": time.time()
            }
        
        @self.app.get("/debug/logging")
        async def debug_logging():
            """日志配置调试接口"""
            import logging

            # 获取所有项目相关的logger
            loggers_info = {}

            # 检查根logger
            root_logger = logging.getLogger()
            loggers_info['root'] = {
                'level': logging.getLevelName(root_logger.getEffectiveLevel()),
                'handlers_count': len(root_logger.handlers),
                'disabled': root_logger.disabled
            }

            # 检查项目logger
            project_modules = [
                'src.services.archive_extraction_service',
                'src.models.llm_models',
                'src.core.model_pool',
                'src.utils.image_preprocessor'
            ]

            for module_name in project_modules:
                logger = logging.getLogger(module_name)
                loggers_info[module_name] = {
                    'level': logging.getLevelName(logger.getEffectiveLevel()),
                    'disabled': logger.disabled,
                    'propagate': logger.propagate
                }

            return {
                "loggers": loggers_info,
                "timestamp": time.time()
            }

        @self.app.get("/status", response_model=SystemStatusResponse)
        async def get_system_status():
            """获取系统状态"""
            try:
                status = self.extraction_api.get_system_status()
                return SystemStatusResponse(
                    status="running",
                    gpu_info=status['device_info'],
                    model_status=status['model_status'],
                    performance_stats=status['performance_stats']
                )
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/stats/preprocessing")
        async def get_preprocessing_stats():
            """获取图像预处理统计信息"""
            try:
                stats = self.image_processor.get_stats()
                return {
                    "success": True,
                    "stats": stats,
                    "timestamp": time.time()
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e),
                    "timestamp": time.time()
                }

        @self.app.post("/stats/preprocessing/reset")
        async def reset_preprocessing_stats():
            """重置图像预处理统计信息"""
            try:
                self.image_processor.reset_stats()
                return {
                    "success": True,
                    "message": "统计信息已重置",
                    "timestamp": time.time()
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e),
                    "timestamp": time.time()
                }

        @self.app.post("/extract/upload", response_model=ExtractionResponse)
        async def extract_from_upload(
            file: UploadFile = File(...),
            key_list: str = "题名,责任者,文号,发文日期",
            sync: bool = True,
            options: Optional[str] = None
        ):
            """从上传文件提取信息"""
            try:
                # 保存上传文件
                file_path = await self._save_upload_file(file)
                
                # 解析参数
                key_list_parsed = [k.strip() for k in key_list.split(',')]
                options_parsed = eval(options) if options else {}
                
                # 执行提取
                if sync:
                    result = self.extraction_api.extract(
                        str(file_path), key_list_parsed, sync=True, options=options_parsed
                    )
                    return ExtractionResponse(
                        success=True,
                        results=result['results'],
                        processing_time=result.get('processing_time')
                    )
                else:
                    task_id = self.extraction_api.extract(
                        str(file_path), key_list_parsed, sync=False, options=options_parsed
                    )
                    return ExtractionResponse(
                        success=True,
                        task_id=task_id
                    )
                    
            except Exception as e:
                self.logger.error(f"提取失败: {e}")
                return ExtractionResponse(
                    success=False,
                    error=str(e)
                )
            finally:
                # 调试期间保留文件，不删除
                # TODO: 生产环境需要启用文件清理
                if 'file_path' in locals():
                    self.logger.info(f"调试模式：保留上传文件 {file_path}")
                    # try:
                    #     os.unlink(file_path)
                    # except:
                    #     pass
        
        @self.app.post("/extract/archive", response_model=ExtractionResponse)
        async def extract_archive_elements(
            file: UploadFile = File(...),
            custom_keys: Optional[str] = Form(None),
            confidence_threshold: float = Form(0.5),
            enable_preprocessing: bool = Form(True),
            enable_stamp_processing: bool = Form(False),
            stamp_confidence_threshold: float = Form(0.8)
        ):
            """提取档案要素 - 带图像预处理优化版本"""
            processing_start_time = time.time()
            file_path = None
            preprocessed_path = None

            # 添加参数调试日志
            self.logger.error(f"🔍 API参数调试:")
            self.logger.info(f"  - enable_stamp_processing: {enable_stamp_processing} (类型: {type(enable_stamp_processing)})")
            self.logger.info(f"  - stamp_confidence_threshold: {stamp_confidence_threshold}")
            self.logger.info(f"  - enable_preprocessing: {enable_preprocessing}")
            self.logger.info(f"  - custom_keys: {custom_keys}")
            self.logger.info(f"  - confidence_threshold: {confidence_threshold}")

            try:
                # 读取上传文件内容
                file_content = await file.read()
                original_filename = file.filename or "unknown"

                self.logger.info(f"开始处理档案文档: {original_filename} ({len(file_content)/1024:.1f}KB)")

                # 图像预处理（如果启用）
                image_data = file_content
                preprocessing_stats = {}

                if enable_preprocessing and self.model_pool.config.get('image_preprocessing', {}).get('enabled', True):
                    try:
                        # 动态配置印章处理参数
                        preprocessing_config = self.model_pool.config.get('image_preprocessing', {}).copy()

                        # 根据端点参数动态设置印章处理
                        if enable_stamp_processing:
                            preprocessing_config['stamp_processing'] = {
                                'enabled': True,
                                'model_name': 'PP-OCRv4_mobile_seal_det',
                                'confidence_threshold': stamp_confidence_threshold,
                                'enhancement_methods': ['redblue_removal'],
                                'create_multiple_versions': True,
                                'enable_stats': True,
                            }
                            self.logger.info(f"启用印章处理，置信度阈值: {stamp_confidence_threshold}")
                        else:
                            preprocessing_config['stamp_processing'] = {'enabled': False}

                        # 创建临时图像处理器（使用动态配置）
                        temp_processor = get_image_processor(preprocessing_config, self.model_pool, force_reinit=True)

                        self.logger.info("执行图像预处理...")
                        preprocessed_data, preprocessing_stats = temp_processor.process_image(
                            file_content,
                            output_format='JPEG'
                        )
                        image_data = preprocessed_data

                        self.logger.info(
                            f"图像预处理完成: "
                            f"{preprocessing_stats.get('original_size', 'N/A')} -> "
                            f"{preprocessing_stats.get('processed_size', 'N/A')}, "
                            f"耗时 {preprocessing_stats.get('processing_time', 0):.2f}s"
                        )

                        # 记录印章处理统计
                        if enable_stamp_processing and 'stamp_processing' in preprocessing_stats:
                            stamp_stats = preprocessing_stats['stamp_processing']
                            self.logger.info(f"印章处理统计: {stamp_stats}")

                    except Exception as e:
                        self.logger.warning(f"图像预处理失败，使用原始图像: {e}")
                        image_data = file_content
                        preprocessing_stats = {'error': str(e)}

                # 保存处理后的图像到临时文件
                file_id = str(uuid.uuid4())
                file_ext = '.jpg' if enable_preprocessing else Path(original_filename).suffix
                file_path = self.upload_dir / f"{file_id}{file_ext}"

                with open(file_path, 'wb') as f:
                    f.write(image_data)

                # 解析自定义要素
                elements = None
                if custom_keys:
                    elements = [k.strip() for k in custom_keys.split(',')]

                # 使用优化的档案要素提取服务（传递印章检测参数）
                result = await self.archive_service.extract_from_image(
                    image_path=file_path,
                    elements=elements,
                    confidence_threshold=confidence_threshold,
                    enable_stamp_processing=enable_stamp_processing,
                    stamp_confidence_threshold=stamp_confidence_threshold
                )

                # 计算总处理时间
                total_processing_time = time.time() - processing_start_time

                # 合并处理统计信息
                if result.get('success'):
                    # 添加图像预处理统计信息
                    if preprocessing_stats:
                        result['preprocessing_stats'] = preprocessing_stats

                    # 添加图像信息到OCR信息中
                    if 'ocr_info' not in result:
                        result['ocr_info'] = {}

                    result['ocr_info']['image_preprocessing'] = {
                        'enabled': enable_preprocessing,
                        'original_filename': original_filename,
                        'original_size_kb': len(file_content) / 1024,
                        'processed_size_kb': len(image_data) / 1024,
                        'size_reduction': (1 - len(image_data) / len(file_content)) * 100 if len(file_content) > 0 else 0
                    }

                    result['total_processing_time'] = total_processing_time

                # 从档案提取服务获取完整的OCR结果
                ocr_results = []
                ocr_text_count = 0
                if hasattr(self.archive_service, '_last_ocr_result') and self.archive_service._last_ocr_result:
                    last_ocr = self.archive_service._last_ocr_result
                    if last_ocr.get('success') and 'text_blocks' in last_ocr:
                        ocr_results = last_ocr['text_blocks']
                        ocr_text_count = len(ocr_results)

                return ExtractionResponse(
                    success=result.get('success', False),
                    results=result.get('elements') if result.get('success') else None,
                    error=result.get('error'),
                    processing_time=result.get('total_processing_time', total_processing_time),
                    ocr_info={
                        'total_blocks': ocr_text_count,
                        'text_blocks': ocr_results,
                        **result.get('ocr_info', {})
                    },
                    task_id=result.get('task_id')
                )

            except Exception as e:
                self.logger.error(f"档案要素提取失败: {e}")
                return ExtractionResponse(
                    success=False,
                    error=str(e),
                    processing_time=time.time() - processing_start_time
                )
            finally:
                # 清理临时文件
                for temp_file in [file_path, preprocessed_path]:
                    if temp_file and temp_file.exists():
                        try:
                            temp_file.unlink()
                        except Exception as e:
                            self.logger.warning(f"清理临时文件失败: {e}")
                    #     os.unlink(file_path)
                    # except:
                    #     pass
        
        @self.app.get("/task/{task_id}", response_model=TaskStatusResponse)
        async def get_task_status(task_id: str):
            """获取任务状态"""
            try:
                status = self.extraction_api.get_task_status(task_id)
                
                if 'error' in status:
                    return TaskStatusResponse(
                        task_id=task_id,
                        status="not_found",
                        error=status['error']
                    )
                
                # 计算进度
                progress = None
                if status['status'] == 'processing':
                    elapsed = status.get('elapsed_time', 0)
                    # 简单的进度估算
                    progress = min(90, elapsed / 10 * 100)  # 假设10秒完成90%
                elif status['status'] == 'completed':
                    progress = 100
                
                return TaskStatusResponse(
                    task_id=task_id,
                    status=status['status'],
                    progress=progress
                )
                
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/task/{task_id}/result", response_model=ExtractionResponse)
        async def get_task_result(task_id: str, timeout: int = 60):
            """获取任务结果"""
            try:
                result = self.extraction_api.wait_for_result(task_id, timeout)
                return ExtractionResponse(
                    success=True,
                    task_id=task_id,
                    results=result['results'],
                    processing_time=result.get('processing_time')
                )
                
            except TimeoutError:
                return ExtractionResponse(
                    success=False,
                    task_id=task_id,
                    error="任务超时"
                )
            except Exception as e:
                return ExtractionResponse(
                    success=False,
                    task_id=task_id,
                    error=str(e)
                )
        
        @self.app.delete("/task/{task_id}")
        async def cancel_task(task_id: str):
            """取消任务"""
            try:
                success = self.extraction_api.cancel_task(task_id)
                return {"success": success, "task_id": task_id}
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/extract/archive_folder", response_model=ExtractionResponse)
        async def extract_archive_folder(
            files: List[UploadFile] = File(...),
            custom_keys: Optional[str] = None,
            sync: bool = True
        ):
            """提取档案文件夹中的要素（多页档案统一提取）"""
            try:
                # 保存所有上传文件到临时目录
                temp_folder = self.upload_dir / f"archive_{uuid.uuid4()}"
                temp_folder.mkdir(exist_ok=True)

                saved_files = []
                for file in files:
                    file_path = temp_folder / file.filename
                    with open(file_path, "wb") as f:
                        content = await file.read()
                        f.write(content)
                    saved_files.append(str(file_path))

                # 解析自定义关键词
                custom_keys_parsed = None
                if custom_keys:
                    custom_keys_parsed = [k.strip() for k in custom_keys.split(',')]

                # 执行档案文件夹要素提取
                if sync:
                    result = self.extraction_api.extract_archive_folder(
                        str(temp_folder), sync=True, custom_keys=custom_keys_parsed
                    )
                    return ExtractionResponse(
                        success=True,
                        results=result['results'],
                        processing_time=result.get('processing_time')
                    )
                else:
                    task_id = self.extraction_api.extract_archive_folder(
                        str(temp_folder), sync=False, custom_keys=custom_keys_parsed
                    )
                    return ExtractionResponse(
                        success=True,
                        task_id=task_id
                    )

            except Exception as e:
                self.logger.error(f"档案文件夹要素提取失败: {e}")
                return ExtractionResponse(
                    success=False,
                    error=str(e)
                )
            finally:
                # 清理临时文件夹
                if 'temp_folder' in locals():
                    try:
                        import shutil
                        shutil.rmtree(temp_folder)
                    except:
                        pass

        @self.app.post("/batch/extract", response_model=List[ExtractionResponse])
        async def batch_extract(
            files: List[UploadFile] = File(...),
            key_list: str = "题名,责任者,文号,发文日期",
            sync: bool = False
        ):
            """批量提取"""
            results = []
            key_list_parsed = [k.strip() for k in key_list.split(',')]

            for file in files:
                try:
                    file_path = await self._save_upload_file(file)

                    if sync:
                        result = self.extraction_api.extract(
                            str(file_path), key_list_parsed, sync=True
                        )
                        results.append(ExtractionResponse(
                            success=True,
                            results=result['results']
                        ))
                    else:
                        task_id = self.extraction_api.extract(
                            str(file_path), key_list_parsed, sync=False
                        )
                        results.append(ExtractionResponse(
                            success=True,
                            task_id=task_id
                        ))

                except Exception as e:
                    results.append(ExtractionResponse(
                        success=False,
                        error=str(e)
                    ))
                finally:
                    if 'file_path' in locals():
                        try:
                            os.unlink(file_path)
                        except:
                            pass

            return results
    
    async def _save_upload_file(self, file: UploadFile) -> Path:
        """保存上传文件"""
        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        file_ext = Path(file.filename).suffix if file.filename else '.tmp'
        file_path = self.upload_dir / f"{file_id}{file_ext}"
        
        # 保存文件
        with open(file_path, "wb") as f:
            content = await file.read()
            f.write(content)
        
        return file_path
    
    def run(self, host: str = "0.0.0.0", port: int = 8080, **kwargs):
        """运行Web服务"""
        self.logger.info(f"启动Web服务: http://{host}:{port}")
        uvicorn.run(self.app, host=host, port=port, **kwargs)
    
    def cleanup(self):
        """清理资源"""
        self.extraction_api.cleanup()

# 便捷函数
def create_web_service(config_path: Optional[str] = None) -> ExtractionWebService:
    """创建Web服务实例"""
    return ExtractionWebService(config_path)

def run_web_service(config_path: Optional[str] = None, 
                   host: str = "0.0.0.0", 
                   port: int = 8080):
    """运行Web服务"""
    service = create_web_service(config_path)
    try:
        service.run(host=host, port=port)
    finally:
        service.cleanup()

if __name__ == "__main__":
    run_web_service()
