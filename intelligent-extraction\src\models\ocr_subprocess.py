#!/usr/bin/env python3
"""
PaddleOCR子进程模块
通过独立进程运行PaddleOCR，避免与PyTorch的冲突
位置: intelligent-extraction/src/models/ocr_subprocess.py
"""
import os
import sys
import json
import pickle
import base64
import logging
from pathlib import Path
from typing import Dict, Any, List, Union
import subprocess
import tempfile

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OCRSubprocessRunner:
    """PaddleOCR子进程运行器"""
    
    def __init__(self, device_id: int = 0):
        self.device_id = device_id
        self.logger = logging.getLogger(__name__)
        
        # 获取当前脚本路径
        self.script_dir = Path(__file__).parent
        self.ocr_worker_script = self.script_dir / "ocr_worker.py"
        
        # 确保worker脚本存在
        if not self.ocr_worker_script.exists():
            self._create_worker_script()
    
    def _create_worker_script(self):
        """创建OCR工作进程脚本"""
        worker_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR工作进程
独立运行PaddleOCR，避免与PyTorch冲突
"""
import os
import sys
import json
import warnings

# 设置编码
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# 设置环境变量 - 在导入任何库之前
os.environ['FLAGS_allocator_strategy'] = 'auto_growth'
os.environ['FLAGS_fraction_of_gpu_memory_to_use'] = '0.5'
os.environ['FLAGS_eager_delete_tensor_gb'] = '0.0'
os.environ['FLAGS_fast_eager_deletion_mode'] = 'true'
os.environ['PADDLE_DISABLE_STATIC'] = '1'

# 解决OpenCV问题
os.environ['OPENCV_IO_ENABLE_OPENEXR'] = '0'
os.environ['OPENCV_IO_ENABLE_JASPER'] = '0'

warnings.filterwarnings('ignore')

def main():
    """主函数"""
    try:
        if len(sys.argv) != 2:
            print(json.dumps({"error": "Usage: python ocr_worker.py <config_file>"}))
            sys.exit(1)

        config_file = sys.argv[1]

        # 读取配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 导入PaddleOCR - 延迟导入避免循环依赖
        try:
            from paddleocr import PaddleOCR
        except Exception as import_error:
            print(json.dumps({
                "success": False,
                "error": f"PaddleOCR导入失败: {import_error}",
                "error_type": "ImportError"
            }))
            sys.exit(1)

        # 初始化OCR - 使用PaddleOCR 3.0的新参数格式
        ocr_kwargs = {
            'use_angle_cls': config.get('use_angle_cls', True),
            'lang': config.get('lang', 'ch')
        }

        # 设备配置 - PaddleOCR 3.0使用device参数
        if config.get('use_cpu', True):
            ocr_kwargs['device'] = 'cpu'
        else:
            ocr_kwargs['device'] = 'gpu'
            # 注意：PaddleOCR 3.0可能不再支持gpu_id参数

        # 创建OCR引擎
        try:
            ocr_engine = PaddleOCR(**ocr_kwargs)
        except Exception as ocr_error:
            print(json.dumps({
                "success": False,
                "error": f"OCR引擎初始化失败: {ocr_error}",
                "error_type": "OCRInitError"
            }))
            sys.exit(1)

        # 处理图像
        image_path = config['image_path']
        try:
            results = ocr_engine.ocr(image_path, cls=config.get('use_angle_cls', True))
        except Exception as ocr_process_error:
            print(json.dumps({
                "success": False,
                "error": f"OCR处理失败: {ocr_process_error}",
                "error_type": "OCRProcessError"
            }))
            sys.exit(1)

        # 处理结果
        processed_results = []
        if results and results[0]:
            for line in results[0]:
                if line and len(line) >= 2:
                    bbox = line[0]
                    text_info = line[1]
                    if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                        text = text_info[0]
                        confidence = float(text_info[1])
                        processed_results.append({
                            'text': text,
                            'confidence': confidence,
                            'bbox': bbox
                        })

        # 返回结果
        result = {
            'success': True,
            'results': processed_results,
            'total_blocks': len(processed_results)
        }

        print(json.dumps(result, ensure_ascii=False))

    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'error_type': type(e).__name__
        }
        print(json.dumps(error_result, ensure_ascii=False))
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
        
        # 写入worker脚本
        with open(self.ocr_worker_script, 'w', encoding='utf-8') as f:
            f.write(worker_code)
        
        self.logger.info(f"创建OCR工作脚本: {self.ocr_worker_script}")
    
    def recognize(self, image_path: Union[str, Path],
                  confidence_threshold: float = 0.5,
                  use_cpu: bool = False) -> Dict[str, Any]:
        """
        通过子进程识别图像

        Args:
            image_path: 图像路径
            confidence_threshold: 置信度阈值
            use_cpu: 是否使用CPU模式

        Returns:
            Dict: OCR结果
        """
        # 使用默认配置调用process_with_config
        config = {
            'use_angle_cls': True,
            'lang': 'ch',
            'use_cpu': use_cpu,
            'gpu_id': self.device_id,
            'confidence_threshold': confidence_threshold
        }
        return self.process_with_config(image_path, config)

    def recognize_with_stamp_detection(self, image_path: Union[str, Path],
                                     confidence_threshold: float = 0.5,
                                     use_cpu: bool = False,
                                     enable_stamp_processing: bool = True,
                                     stamp_confidence_threshold: float = 0.8) -> Dict[str, Any]:
        """
        通过子进程识别图像，同时进行印章检测

        Args:
            image_path: 图像路径
            confidence_threshold: OCR置信度阈值
            use_cpu: 是否使用CPU模式
            enable_stamp_processing: 是否启用印章检测
            stamp_confidence_threshold: 印章检测置信度阈值

        Returns:
            Dict: OCR结果和印章检测结果
        """
        # 使用扩展配置调用process_with_config
        config = {
            'use_angle_cls': True,
            'lang': 'ch',
            'use_cpu': use_cpu,
            'gpu_id': self.device_id,
            'confidence_threshold': confidence_threshold,
            'enable_stamp_processing': enable_stamp_processing,
            'stamp_confidence_threshold': stamp_confidence_threshold
        }
        return self.process_with_config(image_path, config)

    def process_with_config(self, image_path: Union[str, Path],
                           config: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用自定义配置通过子进程识别图像

        Args:
            image_path: 图像路径
            config: OCR配置参数

        Returns:
            Dict: OCR结果
        """
        try:
            # 创建临时配置文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json',
                                           delete=False, encoding='utf-8') as f:
                # 确保使用绝对路径
                abs_image_path = os.path.abspath(str(image_path))
                full_config = {
                    'image_path': abs_image_path,
                    **config  # 合并用户配置
                }
                json.dump(full_config, f, ensure_ascii=False)
                config_file = f.name
            
            try:
                # 运行子进程
                cmd = [sys.executable, str(self.ocr_worker_script), config_file]
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    errors='ignore',  # 忽略编码错误
                    timeout=60,  # 60秒超时
                    env=dict(os.environ, PYTHONIOENCODING='utf-8')  # 设置Python IO编码
                )
                
                if result.returncode != 0:
                    error_msg = f"OCR子进程失败: {result.stderr}"
                    self.logger.error(error_msg)
                    return {
                        'success': False,
                        'error': error_msg,
                        'results': []
                    }
                
                # 解析结果
                try:
                    ocr_result = json.loads(result.stdout)

                    # 过滤低置信度结果
                    confidence_threshold = config.get('confidence_threshold', 0.5)
                    if ocr_result.get('success') and 'results' in ocr_result:
                        filtered_results = [
                            r for r in ocr_result['results']
                            if r['confidence'] >= confidence_threshold
                        ]
                        ocr_result['results'] = filtered_results
                        ocr_result['total_blocks'] = len(filtered_results)

                    return ocr_result
                    
                except json.JSONDecodeError as e:
                    error_msg = f"解析OCR结果失败: {e}, 输出: {result.stdout}"
                    self.logger.error(error_msg)
                    return {
                        'success': False,
                        'error': error_msg,
                        'results': []
                    }
                    
            finally:
                # 清理临时文件
                try:
                    os.unlink(config_file)
                except:
                    pass
                    
        except subprocess.TimeoutExpired:
            error_msg = "OCR子进程超时"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'results': []
            }
        except Exception as e:
            error_msg = f"OCR子进程运行失败: {e}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'results': []
            }
    
    def __str__(self) -> str:
        return f"OCRSubprocessRunner(device_id={self.device_id})"


if __name__ == "__main__":
    # 测试代码
    runner = OCRSubprocessRunner()
    print("OCR子进程运行器创建成功")
