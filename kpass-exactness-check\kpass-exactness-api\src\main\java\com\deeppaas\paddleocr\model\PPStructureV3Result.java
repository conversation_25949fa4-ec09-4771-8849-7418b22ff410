package com.deeppaas.paddleocr.model;

import lombok.Data;
import java.awt.Rectangle;
import java.util.ArrayList;
import java.util.List;

/**
 * PP-StructureV3解析结果
 */
@Data
public class PPStructureV3Result {
    private List<LayoutRegion> layoutRegions = new ArrayList<>();
    private String markdown = "";
    private int pageWidth = 0;
    private int pageHeight = 0;
    
    @Data
    public static class LayoutRegion {
        private String type; // text, title, image, etc.
        private String content;
        private Rectangle bbox;
        private double confidence;
    }
}
