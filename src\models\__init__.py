"""
模型模块 - 智能信息提取的各种AI模型
"""

# LLM模型
from .llm_models import (
    LLMResponse,
    OllamaLLMModel,
    QwenLLMModel
)

# 嵌入模型
from .embedding_models import (
    EmbeddingResponse,
    OllamaEmbeddingModel,
    QwenEmbeddingModel,
    BGEEmbeddingModel
)

# OCR模型
from .ocr_models import (
    OCRResult,
    DocumentOCRResult,
    PaddleOCRModel
)

__all__ = [
    # LLM相关
    'LLMResponse',
    'OllamaLLMModel',
    'QwenLLMModel',
    
    # 嵌入相关
    'EmbeddingResponse',
    'OllamaEmbeddingModel',
    'QwenEmbeddingModel',
    'BGEEmbeddingModel',
    
    # OCR相关
    'OCRResult',
    'DocumentOCRResult',
    'PaddleOCRModel',
]

# 版本信息
__version__ = "1.0.0"

# 模型工厂函数
def create_llm_model(model_type: str = "qwen", **kwargs):
    """
    创建LLM模型实例
    
    Args:
        model_type: 模型类型 "qwen", "ollama"
        **kwargs: 模型参数
        
    Returns:
        LLM模型实例
    """
    if model_type.lower() == "qwen":
        return QwenLLMModel(**kwargs)
    elif model_type.lower() == "ollama":
        return OllamaLLMModel(**kwargs)
    else:
        raise ValueError(f"不支持的LLM模型类型: {model_type}")

def create_embedding_model(model_type: str = "bge", **kwargs):
    """
    创建嵌入模型实例
    
    Args:
        model_type: 模型类型 "bge", "qwen", "ollama"
        **kwargs: 模型参数
        
    Returns:
        嵌入模型实例
    """
    if model_type.lower() == "bge":
        return BGEEmbeddingModel(**kwargs)
    elif model_type.lower() == "qwen":
        return QwenEmbeddingModel(**kwargs)
    elif model_type.lower() == "ollama":
        return OllamaEmbeddingModel(**kwargs)
    else:
        raise ValueError(f"不支持的嵌入模型类型: {model_type}")

def create_ocr_model(model_type: str = "paddleocr", **kwargs):
    """
    创建OCR模型实例
    
    Args:
        model_type: 模型类型 "paddleocr"
        **kwargs: 模型参数
        
    Returns:
        OCR模型实例
    """
    if model_type.lower() == "paddleocr":
        return PaddleOCRModel(**kwargs)
    else:
        raise ValueError(f"不支持的OCR模型类型: {model_type}")

# 模型配置预设
MODEL_PRESETS = {
    # LLM预设
    "qwen_7b": {
        "model_type": "qwen",
        "model_size": "7b",
        "temperature": 0.1,
        "top_p": 0.9,
    },
    "qwen_3b": {
        "model_type": "qwen", 
        "model_size": "3b",
        "temperature": 0.1,
        "top_p": 0.9,
    },
    
    # 嵌入模型预设
    "bge_m3": {
        "model_type": "bge",
        "model_size": "m3",
        "max_length": 512,
        "batch_size": 8,
    },
    "bge_large": {
        "model_type": "bge",
        "model_size": "large", 
        "max_length": 512,
        "batch_size": 4,
    },
    
    # OCR预设
    "paddleocr_ch": {
        "model_type": "paddleocr",
        "lang": "ch",
        "use_angle_cls": True,
        "use_gpu": True,
    },
    "paddleocr_en": {
        "model_type": "paddleocr",
        "lang": "en",
        "use_angle_cls": True,
        "use_gpu": True,
    },
}

def create_model_from_preset(preset_name: str, **override_kwargs):
    """
    从预设创建模型
    
    Args:
        preset_name: 预设名称
        **override_kwargs: 覆盖参数
        
    Returns:
        模型实例
    """
    if preset_name not in MODEL_PRESETS:
        raise ValueError(f"未知的模型预设: {preset_name}")
    
    preset_config = MODEL_PRESETS[preset_name].copy()
    model_type = preset_config.pop("model_type")
    
    # 合并覆盖参数
    preset_config.update(override_kwargs)
    
    # 根据模型类型创建实例
    if preset_name.startswith("qwen") or "llm" in preset_name:
        return create_llm_model(model_type, **preset_config)
    elif preset_name.startswith("bge") or "embed" in preset_name:
        return create_embedding_model(model_type, **preset_config)
    elif preset_name.startswith("paddleocr") or "ocr" in preset_name:
        return create_ocr_model(model_type, **preset_config)
    else:
        raise ValueError(f"无法确定预设 {preset_name} 的模型类型")

# 模型兼容性检查
def check_model_compatibility():
    """检查模型兼容性"""
    compatibility_report = {
        "ollama_service": False,
        "paddleocr": False,
        "gpu_available": False,
        "errors": []
    }
    
    # 检查Ollama服务
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            compatibility_report["ollama_service"] = True
    except Exception as e:
        compatibility_report["errors"].append(f"Ollama服务不可用: {e}")
    
    # 检查PaddleOCR
    try:
        from paddleocr import PaddleOCR
        compatibility_report["paddleocr"] = True
    except Exception as e:
        compatibility_report["errors"].append(f"PaddleOCR不可用: {e}")
    
    # 检查GPU
    try:
        import torch
        if torch.cuda.is_available():
            compatibility_report["gpu_available"] = True
            compatibility_report["gpu_count"] = torch.cuda.device_count()
            compatibility_report["gpu_names"] = [
                torch.cuda.get_device_name(i) 
                for i in range(torch.cuda.device_count())
            ]
    except Exception as e:
        compatibility_report["errors"].append(f"GPU检查失败: {e}")
    
    return compatibility_report
