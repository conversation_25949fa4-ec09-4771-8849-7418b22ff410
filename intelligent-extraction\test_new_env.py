#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新conda环境中的PaddleOCR功能
"""

import os
import sys
import time
import traceback

def test_paddle_basic():
    """测试PaddlePaddle基础功能"""
    print("=" * 50)
    print("测试PaddlePaddle基础功能")
    print("=" * 50)
    
    try:
        import paddle
        print(f"✅ PaddlePaddle版本: {paddle.__version__}")
        
        # 检查GPU
        if paddle.is_compiled_with_cuda():
            print(f"✅ CUDA编译支持: 是")
            print(f"✅ GPU数量: {paddle.device.cuda.device_count()}")
            for i in range(paddle.device.cuda.device_count()):
                gpu_name = paddle.device.cuda.get_device_name(i)
                print(f"   GPU {i}: {gpu_name}")
        else:
            print("⚠️  CUDA编译支持: 否")
            
        return True
    except Exception as e:
        print(f"❌ PaddlePaddle测试失败: {e}")
        traceback.print_exc()
        return False

def test_paddleocr_import():
    """测试PaddleOCR导入"""
    print("\n" + "=" * 50)
    print("测试PaddleOCR导入")
    print("=" * 50)
    
    try:
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR导入成功")
        return True
    except Exception as e:
        print(f"❌ PaddleOCR导入失败: {e}")
        traceback.print_exc()
        return False

def test_paddleocr_init():
    """测试PaddleOCR初始化"""
    print("\n" + "=" * 50)
    print("测试PaddleOCR初始化")
    print("=" * 50)
    
    try:
        from paddleocr import PaddleOCR
        
        print("正在初始化PaddleOCR (CPU模式)...")
        start_time = time.time()
        
        # 使用最简单的参数初始化PaddleOCR 3.0
        # PaddleOCR 3.0 API大幅变化，使用最基本参数
        ocr = PaddleOCR(
            lang='ch'  # 只指定语言，其他使用默认值
        )
        
        init_time = time.time() - start_time
        print(f"✅ PaddleOCR初始化成功，耗时: {init_time:.2f}秒")
        return ocr
    except Exception as e:
        print(f"❌ PaddleOCR初始化失败: {e}")
        traceback.print_exc()
        return None

def test_ocr_recognition(ocr):
    """测试OCR识别功能"""
    print("\n" + "=" * 50)
    print("测试OCR识别功能")
    print("=" * 50)
    
    if ocr is None:
        print("❌ OCR对象为空，跳过识别测试")
        return False
    
    try:
        # 创建一个简单的测试图片URL
        test_url = "https://paddle-model-ecology.bj.bcebos.com/paddlex/imgs/demo_image/general_ocr_002.png"

        print(f"正在识别测试图片: {test_url}")
        start_time = time.time()

        # PaddleOCR 3.0 使用 predict 方法而不是 ocr 方法
        result = ocr.predict(test_url)

        recognition_time = time.time() - start_time
        print(f"✅ OCR识别完成，耗时: {recognition_time:.2f}秒")

        # PaddleOCR 3.0 结果格式：[{'rec_texts': [...], 'rec_scores': [...], ...}]
        if result and len(result) > 0:
            page_result = result[0]
            rec_texts = page_result.get('rec_texts', [])
            rec_scores = page_result.get('rec_scores', [])

            if rec_texts:
                print(f"✅ 识别到 {len(rec_texts)} 个文本区域:")
                for i, (text, score) in enumerate(zip(rec_texts[:3], rec_scores[:3])):  # 只显示前3个结果
                    print(f"   {i+1}. 文本: '{text}' (置信度: {score:.3f})")
                if len(rec_texts) > 3:
                    print(f"   ... 还有 {len(rec_texts) - 3} 个结果")
            else:
                print("⚠️  未识别到文本内容")
        else:
            print("⚠️  识别结果为空")

        return True
    except Exception as e:
        print(f"❌ OCR识别失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试新conda环境中的PaddleOCR功能")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 测试步骤
    tests = [
        ("PaddlePaddle基础功能", test_paddle_basic),
        ("PaddleOCR导入", test_paddleocr_import),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 出现异常: {e}")
            results.append((test_name, False))
    
    # 如果前面的测试都通过，进行OCR初始化和识别测试
    if all(result for _, result in results):
        try:
            ocr = test_paddleocr_init()
            ocr_init_success = ocr is not None
            results.append(("PaddleOCR初始化", ocr_init_success))
            
            if ocr_init_success:
                ocr_recognition_success = test_ocr_recognition(ocr)
                results.append(("OCR识别功能", ocr_recognition_success))
        except Exception as e:
            print(f"❌ OCR测试出现异常: {e}")
            results.append(("PaddleOCR初始化", False))
            results.append(("OCR识别功能", False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    if all_passed:
        print("\n🎉 所有测试通过！新环境可以正常使用PaddleOCR")
    else:
        print("\n⚠️  部分测试失败，请检查错误信息")
    
    return all_passed

if __name__ == "__main__":
    main()
