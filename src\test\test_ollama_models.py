#!/usr/bin/env python3
"""
测试Ollama模型功能
位置: intelligent-extraction\src\test\test_ollama_models.py
"""
import sys
import os
import logging
from pathlib import Path

# 添加src目录到路径，以便导入models模块
current_dir = Path(__file__).parent
src_dir = current_dir.parent
sys.path.insert(0, str(src_dir))

# 导入模型相关模块
try:
    from models import (
        create_llm_model,
        create_embedding_model,
        check_model_compatibility,
        MODEL_PRESETS
    )
    print("✅ 模型模块导入成功")
except ImportError as e:
    print(f"❌ 模型模块导入失败: {e}")
    print(f"当前路径: {sys.path}")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_model_compatibility():
    """测试模型兼容性"""
    print("=== 模型兼容性检查 ===")
    
    compatibility = check_model_compatibility()
    
    print(f"Ollama服务: {'✅' if compatibility['ollama_service'] else '❌'}")
    print(f"PaddleOCR: {'✅' if compatibility['paddleocr'] else '❌'}")
    print(f"GPU可用: {'✅' if compatibility['gpu_available'] else '❌'}")
    
    if compatibility['gpu_available']:
        print(f"GPU数量: {compatibility.get('gpu_count', 0)}")
        for i, name in enumerate(compatibility.get('gpu_names', [])):
            print(f"  GPU {i}: {name}")
    
    if compatibility['errors']:
        print("错误信息:")
        for error in compatibility['errors']:
            print(f"  ❌ {error}")
    
    return compatibility

def test_llm_model():
    """测试LLM模型"""
    print("\n=== LLM模型测试 ===")
    
    try:
        # 创建Qwen模型
        print("正在创建Qwen 7B模型...")
        llm = create_llm_model("qwen", model_size="7b")
        print(f"✅ LLM模型创建成功: {llm}")
        
        # 获取模型信息
        print("获取模型信息...")
        model_info = llm.get_model_info()
        print(f"模型信息: {model_info}")
        
        # 测试简单生成
        print("\n测试文本生成...")
        response = llm.generate(
            prompt="你好，请简单介绍一下你自己，不超过50字。",
            temperature=0.1
        )
        print(f"生成结果: {response.content}")
        print(f"响应时间: {response.response_time:.2f}s")
        print(f"Token使用: {response.usage}")
        
        # 测试档案要素提取
        print("\n测试档案要素提取...")
        ocr_text = """
        关于加强档案管理工作的通知
        
        各部门：
        
        为进一步规范档案管理工作，现通知如下：
        
        一、严格按照档案管理制度执行
        二、定期整理和归档文件
        
        特此通知。
        
        办公室
        2024年12月19日
        办字[2024]001号
        """
        
        result = llm.extract_archive_elements(
            ocr_text=ocr_text,
            target_fields=["题名", "责任者", "文号", "发文日期"]
        )
        
        print("档案要素提取结果:")
        for field, value in result.items():
            print(f"  {field}: {value}")
        
        # 清理
        llm.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ LLM模型测试失败: {e}")
        logger.exception("LLM模型测试详细错误:")
        return False

def test_embedding_model():
    """测试嵌入模型"""
    print("\n=== 嵌入模型测试 ===")
    
    try:
        # 创建BGE模型
        print("正在创建BGE-M3嵌入模型...")
        embedding = create_embedding_model("bge", model_size="m3")
        print(f"✅ 嵌入模型创建成功: {embedding}")
        
        # 获取模型信息
        print("获取模型信息...")
        model_info = embedding.get_model_info()
        print(f"模型信息: {model_info}")
        
        # 测试文本编码
        print("\n测试文本编码...")
        texts = [
            "档案管理工作通知",
            "关于加强文件整理的规定",
            "办公室管理制度"
        ]
        
        embeddings = embedding.encode(texts)
        print(f"编码结果形状: {embeddings.shape}")
        print(f"嵌入维度: {embeddings.shape[1]}")
        
        # 测试相似度计算
        print("\n测试相似度计算...")
        similarity = embedding.similarity(texts[0], texts[1])
        print(f"'{texts[0]}' 与 '{texts[1]}' 的相似度: {similarity:.4f}")
        
        # 测试语义搜索
        print("\n测试语义搜索...")
        query = "档案管理"
        documents = [
            "档案管理工作规范",
            "文件整理标准",
            "办公用品采购",
            "档案数字化建设",
            "会议记录模板"
        ]
        
        search_results = embedding.semantic_search(query, documents, top_k=3)
        print(f"查询: '{query}'")
        print("搜索结果:")
        for i, result in enumerate(search_results):
            print(f"  {i+1}. {result['document']} (相似度: {result['similarity']:.4f})")
        
        # 清理
        embedding.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ 嵌入模型测试失败: {e}")
        logger.exception("嵌入模型测试详细错误:")
        return False

def test_model_presets():
    """测试模型预设"""
    print("\n=== 模型预设测试 ===")
    
    print("可用预设:")
    for preset_name, config in MODEL_PRESETS.items():
        print(f"  {preset_name}: {config}")
    
    # 测试从预设创建模型
    try:
        from models import create_model_from_preset
        
        # 测试LLM预设
        print("\n测试LLM预设...")
        llm = create_model_from_preset("qwen_3b")
        print(f"✅ 从预设创建LLM成功: {llm}")
        llm.cleanup()
        
        # 测试嵌入预设
        print("\n测试嵌入预设...")
        embedding = create_model_from_preset("bge_m3")
        print(f"✅ 从预设创建嵌入模型成功: {embedding}")
        embedding.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ 模型预设测试失败: {e}")
        logger.exception("模型预设测试详细错误:")
        return False

def test_ollama_connection():
    """测试Ollama连接"""
    print("\n=== Ollama连接测试 ===")
    
    try:
        import requests
        
        # 测试基本连接
        print("测试Ollama服务连接...")
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        response.raise_for_status()
        
        models_data = response.json()
        available_models = [model['name'] for model in models_data.get('models', [])]
        
        print(f"✅ Ollama服务连接成功")
        print(f"已安装模型数量: {len(available_models)}")
        
        if available_models:
            print("已安装的模型:")
            for model in available_models[:5]:  # 只显示前5个
                print(f"  - {model}")
            if len(available_models) > 5:
                print(f"  ... 还有 {len(available_models) - 5} 个模型")
        else:
            print("⚠️ 未发现已安装的模型")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Ollama服务")
        print("请确保Ollama已启动: ollama serve")
        return False
    except Exception as e:
        print(f"❌ Ollama连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Ollama模型功能测试")
    print("=" * 50)
    print(f"src目录: {src_dir}")
    print(f"当前工作目录: {os.getcwd()}")

    # 兼容性检查
    test_model_compatibility()
    
    # Ollama连接测试
    ollama_ok = test_ollama_connection()
    
    if not ollama_ok:
        print("\n❌ Ollama服务不可用，跳过模型测试")
        print("请启动Ollama服务: ollama serve")
        return
    
    # 测试结果
    test_results = []
    
    # 测试LLM模型
    test_results.append(("LLM模型", test_llm_model()))
    
    # 测试嵌入模型
    test_results.append(("嵌入模型", test_embedding_model()))
    
    # 测试模型预设
    test_results.append(("模型预设", test_model_presets()))
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(test_results)} 个测试通过")
    
    if passed == len(test_results):
        print("🎉 所有测试通过！Ollama模型功能正常。")
    else:
        print("⚠️ 部分测试失败，请检查配置和依赖。")

if __name__ == "__main__":
    main()
