#!/usr/bin/env python3
"""
完整档案文档处理管道测试
测试：图像预处理 → OCR识别 → 智能提取
"""

import os
import sys
import json
import time
import requests
import subprocess
from pathlib import Path

def start_fastapi_server():
    """启动FastAPI服务器"""
    print("🚀 启动FastAPI服务器...")
    
    # 检查服务器是否已经运行
    try:
        response = requests.get("http://localhost:8080/health", timeout=2)
        if response.status_code == 200:
            print("✅ FastAPI服务器已在运行")
            return None
    except:
        pass
    
    # 启动服务器
    try:
        process = subprocess.Popen([
            sys.executable, "-m", "src.api.web_service"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        for i in range(30):  # 等待30秒
            try:
                response = requests.get("http://localhost:8080/health", timeout=1)
                if response.status_code == 200:
                    print("✅ FastAPI服务器启动成功")
                    return process
            except:
                time.sleep(1)
        
        print("❌ 服务器启动超时")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return None

def test_archive_extraction(image_path):
    """测试档案文档提取"""
    print(f"\n📄 测试档案文档提取")
    print(f"🖼️  图像: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return False
    
    try:
        # 准备文件上传
        with open(image_path, 'rb') as f:
            files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
            
            print("📤 发送请求到 /extract/archive...")
            start_time = time.time()

            # 添加印章处理参数
            data = {
                'enable_stamp_processing': 'true',  # 启用印章处理
                'stamp_confidence_threshold': '0.8',  # 印章检测置信度
                'enable_preprocessing': 'true'  # 启用图像预处理
            }

            # 调试：打印发送的参数
            print(f"🔍 发送的参数: {data}")

            response = requests.post(
                "http://localhost:8080/extract/archive",
                files=files,
                data=data,  # 添加表单数据
                timeout=120  # 2分钟超时
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"⏱️  处理时间: {processing_time:.2f}秒")
            print(f"📊 状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 请求成功!")
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
                # 分析结果
                if result.get('success'):
                    # OCR结果
                    ocr_info = result.get('ocr_info', {})
                    text_blocks = ocr_info.get('text_blocks', [])
                    total_blocks = ocr_info.get('total_blocks', 0)
                    print(f"\n📝 OCR识别结果: {total_blocks} 个文本块")

                    if text_blocks:
                        print("📄 OCR识别的文本内容:")
                        for i, block in enumerate(text_blocks[:5]):  # 只显示前5个
                            text = block.get('text', '').strip()
                            confidence = block.get('confidence', 0)
                            print(f"  {i+1}. {text} (置信度: {confidence:.3f})")
                        if len(text_blocks) > 5:
                            print(f"  ... 还有 {len(text_blocks) - 5} 个文本块")

                    # 智能提取结果
                    extracted_elements = result.get('results', {})
                    print(f"\n🧠 智能提取结果:")
                    for key, value in extracted_elements.items():
                        print(f"  {key}: {value}")
                    
                    # 性能统计
                    processing_time = result.get('processing_time', 0)
                    if processing_time:
                        print(f"\n📊 处理统计:")
                        print(f"  处理时间: {processing_time:.2f}秒")
                    
                    return True
                else:
                    print(f"❌ 处理失败: {result.get('error', 'Unknown error')}")
                    return False
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"错误详情: {error_detail}")
                except:
                    print(f"响应内容: {response.text}")
                return False
                
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_preprocessing_optimization(image_path):
    """测试图像预处理优化"""
    print(f"\n🔧 测试图像预处理优化")
    
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return False
    
    try:
        print("📤 发送请求到 /stats/preprocessing...")
        response = requests.get(
            "http://localhost:8080/stats/preprocessing",
            timeout=60
        )

        if response.status_code == 200:
            result = response.json()
            print("✅ 预处理测试成功!")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return True
        else:
            print(f"❌ 预处理测试失败: {response.status_code}")
            return False
                
    except Exception as e:
        print(f"❌ 预处理测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🔬 完整档案文档处理管道测试")
    print("=" * 60)
    
    # 查找测试图像
    test_images = [
        "uploads/1b248b7d-39c9-4cea-8f7f-84efefe58707.jpg",
        "Image_00002.jpg",
        "test_medium_image.jpg",
        "test_processed_image.jpg"
    ]
    
    test_image = None
    for img in test_images:
        if os.path.exists(img):
            test_image = img
            break
    
    if not test_image:
        print("❌ 找不到测试图像")
        return
    
    print(f"🖼️  使用测试图像: {test_image}")
    
    # 启动FastAPI服务器
    server_process = start_fastapi_server()
    
    try:
        # 等待服务器完全启动
        time.sleep(3)
        
        # 测试预处理优化
        preprocessing_success = test_preprocessing_optimization(test_image)
        
        # 测试完整的档案提取
        extraction_success = test_archive_extraction(test_image)
        
        # 总结
        print("\n" + "=" * 60)
        print("📊 测试总结:")
        print(f"  图像预处理: {'✅ 成功' if preprocessing_success else '❌ 失败'}")
        print(f"  档案提取: {'✅ 成功' if extraction_success else '❌ 失败'}")
        
        if preprocessing_success and extraction_success:
            print("\n🎉 完整管道测试成功!")
            print("✨ 图像预处理 + OCR识别 + 智能提取 全部正常工作")
        else:
            print("\n⚠️  部分功能存在问题，需要进一步调试")
            
    finally:
        # 清理服务器进程
        if server_process:
            print("\n🛑 关闭服务器...")
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
            except:
                server_process.kill()

if __name__ == "__main__":
    main()
