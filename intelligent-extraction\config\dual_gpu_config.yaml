# 双GPU配置 - 适用于2x6GB RTX1660环境
device:
  force_mode: "dual"  # 强制双GPU模式
  min_memory_per_gpu: 6144  # 6GB

models:
  load_strategy: "lazy"  # 懒加载
  
  ocr:
    model_name: "PPOCRv3"
    memory_usage: 800
    config:
      det_model_dir: null
      rec_model_dir: null
      use_angle_cls: true
      lang: "ch"
  
  structure:
    model_name: "PP-Structure"
    memory_usage: 500
    config:
      layout_model_dir: null
      table_model_dir: null
  
  table:
    model_name: "PP-Table"
    memory_usage: 400
    config:
      table_model_dir: null
  
  embedding:
    model_name: "bge-m3"  # 双GPU可以用更大的模型
    memory_usage: 1200
    config:
      model_path: null
      max_length: 512
      batch_size: 8
  
  llm:
    model_name: "Qwen2.5-7B-Instruct"  # 7B模型，专用GPU1
    memory_usage: 5500
    config:
      model_path: null
      max_length: 2048
      temperature: 0.1
      top_p: 0.9
      do_sample: true

pipeline:
  max_workers: 2  # 双线程，利用双GPU
  timeout: 300
  enable_cache: true
  cache_size: 100

extraction:
  default_options:
    confidence_threshold: 0.7
    max_retries: 2
    enable_multi_strategy: true
  
  archive:
    fusion_rules:
      题名:
        preferred_regions: ["title", "header"]
        position_weights:
          top: 0.8
          center: 0.6
        semantic_keywords: ["题名", "标题", "名称"]
      
      责任者:
        preferred_regions: ["text", "signature"]
        position_weights:
          bottom: 0.8
          right: 0.6
        semantic_keywords: ["单位", "机构", "责任者", "发文"]
      
      文号:
        preferred_regions: ["text", "header"]
        pattern_matching: "[\w\d]+号|[\w\d]+字"
        semantic_keywords: ["文号", "编号", "字号"]
      
      发文日期:
        preferred_regions: ["text", "footer"]
        pattern_matching: "\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?"
        semantic_keywords: ["日期", "时间", "发文日期"]

system:
  auto_initialize: true
  max_file_size: 52428800  # 50MB
  supported_formats:
    - ".jpg"
    - ".jpeg"
    - ".png"
    - ".bmp"
    - ".tiff"
    - ".pdf"
    - ".doc"
    - ".docx"
  log_level: "INFO"
  enable_performance_monitoring: true
