package com.toolbox.compliance.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 条目内容正确性检查配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "content.accuracy")
public class ContentAccuracyProperties {
    
    /**
     * AI提取服务配置
     */
    private ExtractionService extraction = new ExtractionService();
    
    /**
     * AI提取参数配置
     */
    private AiConfig ai = new AiConfig();
    
    /**
     * 错误检测配置
     */
    private ErrorConfig error = new ErrorConfig();
    
    /**
     * 匹配策略配置
     */
    private MatchConfig match = new MatchConfig();
    
    /**
     * 字段特定配置
     */
    private FieldConfig field = new FieldConfig();
    
    /**
     * 性能配置
     */
    private PerformanceConfig performance = new PerformanceConfig();
    
    /**
     * 文件处理配置
     */
    private FileConfig file = new FileConfig();
    
    /**
     * 报告配置
     */
    private ReportConfig report = new ReportConfig();
    
    /**
     * Excel标注配置
     */
    private ExcelConfig excel = new ExcelConfig();
    
    /**
     * 日志配置
     */
    private LogConfig log = new LogConfig();
    
    @Data
    public static class ExtractionService {
        private String url = "http://localhost:8080";
        private int timeout = 60000;
        private Retry retry = new Retry();
        
        @Data
        public static class Retry {
            private int max = 3;
            private int delay = 1000;
        }
    }
    
    @Data
    public static class AiConfig {
        private double confidenceThreshold = 0.7;
        private int maxRetries = 2;
        private int batchSize = 5;
        private int timeoutSeconds = 300;
    }
    
    @Data
    public static class ErrorConfig {
        private String level = "normal"; // strict, normal, loose
        private double similarityThreshold = 0.8;
        private boolean enableAutoSuggestion = true;
    }
    
    @Data
    public static class MatchConfig {
        private double exactThreshold = 1.0;
        private double fuzzyThreshold = 0.8;
        private double smartThreshold = 0.7;
    }
    
    @Data
    public static class FieldConfig {
        private double titleWeight = 1.0;
        private double responsibleWeight = 0.9;
        private double documentNoWeight = 0.95;
        private double issueDateWeight = 0.85;
        
        /**
         * 获取字段权重
         */
        public double getFieldWeight(String fieldCode) {
            switch (fieldCode) {
                case "title":
                    return titleWeight;
                case "responsible":
                    return responsibleWeight;
                case "documentNo":
                    return documentNoWeight;
                case "issueDate":
                    return issueDateWeight;
                default:
                    return 1.0;
            }
        }
    }
    
    @Data
    public static class PerformanceConfig {
        private int maxConcurrentTasks = 3;
        private int maxDocumentsPerBatch = 20;
        private int cacheSize = 100;
        private int cacheTtl = 3600; // seconds
    }
    
    @Data
    public static class FileConfig {
        private String maxSize = "50MB";
        private List<String> supportedFormats = List.of(
            ".jpg", ".jpeg", ".png", ".bmp", ".tiff", 
            ".pdf", ".doc", ".docx"
        );
        private String tempDir = "/tmp/content_accuracy";
        
        /**
         * 获取最大文件大小（字节）
         */
        public long getMaxSizeBytes() {
            String size = maxSize.toLowerCase();
            if (size.endsWith("mb")) {
                return Long.parseLong(size.replace("mb", "")) * 1024 * 1024;
            } else if (size.endsWith("kb")) {
                return Long.parseLong(size.replace("kb", "")) * 1024;
            } else if (size.endsWith("gb")) {
                return Long.parseLong(size.replace("gb", "")) * 1024 * 1024 * 1024;
            }
            return Long.parseLong(size);
        }
        
        /**
         * 检查文件格式是否支持
         */
        public boolean isSupportedFormat(String extension) {
            return supportedFormats.contains(extension.toLowerCase());
        }
    }
    
    @Data
    public static class ReportConfig {
        private String exportFormat = "pdf";
        private boolean includeSuggestions = true;
        private boolean includeStatistics = true;
    }
    
    @Data
    public static class ExcelConfig {
        private boolean annotationEnable = true;
        private AnnotationColors color = new AnnotationColors();
        
        @Data
        public static class AnnotationColors {
            private String mismatch = "RED";
            private String partial = "ORANGE";
            private String format = "YELLOW";
            private String missing = "ROSE";
            
            /**
             * 获取错误类型对应的颜色
             */
            public String getColorByErrorType(String errorType) {
                switch (errorType) {
                    case "mismatch":
                        return mismatch;
                    case "partial":
                        return partial;
                    case "format":
                        return format;
                    case "missing":
                        return missing;
                    default:
                        return "GRAY";
                }
            }
        }
    }
    
    @Data
    public static class LogConfig {
        private String level = "INFO";
        private boolean enableDetailed = false;
        private int maxEntries = 1000;
    }
    
    /**
     * 获取匹配阈值
     */
    public double getMatchThreshold(String strategy) {
        switch (strategy) {
            case "exact":
                return match.exactThreshold;
            case "fuzzy":
                return match.fuzzyThreshold;
            case "smart":
                return match.smartThreshold;
            default:
                return match.smartThreshold;
        }
    }
    
    /**
     * 获取错误级别对应的相似度阈值
     */
    public double getSimilarityThresholdByLevel() {
        switch (error.level) {
            case "strict":
                return 0.9;
            case "normal":
                return error.similarityThreshold;
            case "loose":
                return 0.6;
            default:
                return error.similarityThreshold;
        }
    }
    
    /**
     * 验证配置
     */
    public void validate() {
        if (ai.confidenceThreshold < 0.0 || ai.confidenceThreshold > 1.0) {
            throw new IllegalArgumentException("AI置信度阈值必须在0.0-1.0之间");
        }
        
        if (ai.maxRetries < 1 || ai.maxRetries > 10) {
            throw new IllegalArgumentException("最大重试次数必须在1-10之间");
        }
        
        if (ai.batchSize < 1 || ai.batchSize > 50) {
            throw new IllegalArgumentException("批处理大小必须在1-50之间");
        }
        
        if (!List.of("strict", "normal", "loose").contains(error.level)) {
            throw new IllegalArgumentException("错误级别必须是strict、normal或loose");
        }
        
        if (performance.maxConcurrentTasks < 1 || performance.maxConcurrentTasks > 10) {
            throw new IllegalArgumentException("最大并发任务数必须在1-10之间");
        }
    }
    
    /**
     * 获取系统配置信息（用于前端显示）
     */
    public Map<String, Object> getSystemConfigInfo() {
        return Map.of(
            "confidenceThreshold", ai.confidenceThreshold,
            "maxRetries", ai.maxRetries,
            "batchSize", ai.batchSize,
            "errorLevel", error.level,
            "enableAutoSuggestion", error.enableAutoSuggestion,
            "maxConcurrentTasks", performance.maxConcurrentTasks,
            "maxDocumentsPerBatch", performance.maxDocumentsPerBatch,
            "supportedFormats", file.supportedFormats,
            "maxFileSize", file.maxSize
        );
    }
}
