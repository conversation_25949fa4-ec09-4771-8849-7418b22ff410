package com.deeppaas.archive.strategy;

import com.deeppaas.archive.model.ArchiveElements;
import com.deeppaas.vector.LightweightEmbeddingService;
import com.deeppaas.vector.MemoryOptimizedEmbeddingService;
import com.deeppaas.paddleocr.PaddleOCRv3Service;
import com.deeppaas.paddleocr.model.PPChatOCRv4Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;

/**
 * 智能档案要素提取策略
 * 根据文档特征动态选择最优的提取方案
 */
@Component
@Slf4j
public class IntelligentExtractionStrategy {
    
    @Autowired
    private LightweightEmbeddingService lightweightService;
    
    @Autowired
    private MemoryOptimizedEmbeddingService optimizedService;
    
    @Autowired
    private PaddleOCRv3Service paddleOcrService;
    
    @Value("${archive.strategy.quality_threshold:0.8}")
    private double qualityThreshold;
    
    @Value("${archive.strategy.complexity_threshold:0.7}")
    private double complexityThreshold;
    
    @Value("${archive.strategy.gpu_memory_threshold:0.8}")
    private double gpuMemoryThreshold;
    
    @Value("${archive.strategy.enable_dynamic_switching:true}")
    private boolean enableDynamicSwitching;
    
    /**
     * 智能提取档案要素
     */
    public ArchiveElements extractElements(File documentFile, String volumeContext) {
        try {
            // 1. 文档质量和复杂度评估
            DocumentAnalysis analysis = analyzeDocument(documentFile);
            
            // 2. 选择最优提取策略
            ExtractionMethod method = selectExtractionMethod(analysis);
            
            // 3. 执行提取
            ArchiveElements elements = executeExtraction(documentFile, volumeContext, method, analysis);
            
            // 4. 结果优化和验证
            elements = optimizeResults(elements, analysis);
            
            log.debug("文档 {} 使用 {} 策略，质量评分: {:.2f}, 复杂度: {:.2f}", 
                documentFile.getName(), method, analysis.getQualityScore(), analysis.getComplexityScore());
            
            return elements;
            
        } catch (Exception e) {
            log.error("智能提取失败: {}", documentFile.getName(), e);
            return fallbackExtraction(documentFile);
        }
    }
    
    /**
     * 文档分析
     */
    private DocumentAnalysis analyzeDocument(File documentFile) {
        DocumentAnalysis analysis = new DocumentAnalysis();
        analysis.setFileName(documentFile.getName());
        analysis.setFileSize(documentFile.length());
        
        try {
            // 基础OCR解析获取文档信息
            var ocrResult = paddleOcrService.parseDocument(documentFile);
            
            // 1. 质量评估
            double qualityScore = calculateQualityScore(ocrResult, documentFile);
            analysis.setQualityScore(qualityScore);
            
            // 2. 复杂度评估
            double complexityScore = calculateComplexityScore(ocrResult);
            analysis.setComplexityScore(complexityScore);
            
            // 3. 结构化程度评估
            boolean isStructured = assessStructure(ocrResult);
            analysis.setStructured(isStructured);
            
            // 4. 重要性评估
            boolean isImportant = assessImportance(documentFile.getName(), ocrResult);
            analysis.setImportant(isImportant);
            
            // 5. 要素类型预判
            Set<String> expectedElements = predictElementTypes(ocrResult);
            analysis.setExpectedElements(expectedElements);
            
        } catch (Exception e) {
            log.warn("文档分析失败，使用默认评估: {}", documentFile.getName(), e);
            // 设置保守的默认值
            analysis.setQualityScore(0.6);
            analysis.setComplexityScore(0.8);
            analysis.setStructured(false);
            analysis.setImportant(false);
            analysis.setExpectedElements(new HashSet<>(Arrays.asList("title", "responsible_party", "document_number", "issue_date")));
        }
        
        return analysis;
    }
    
    /**
     * 选择提取策略
     */
    private ExtractionMethod selectExtractionMethod(DocumentAnalysis analysis) {
        if (!enableDynamicSwitching) {
            return ExtractionMethod.LIGHTWEIGHT_ONLY;
        }
        
        // 策略1: 高质量结构化文档 -> 轻量级方案
        if (analysis.getQualityScore() >= qualityThreshold && 
            analysis.isStructured() && 
            analysis.getComplexityScore() <= complexityThreshold) {
            return ExtractionMethod.LIGHTWEIGHT_ONLY;
        }
        
        // 策略2: 重要文档且GPU资源充足 -> ERNIE方案
        if (analysis.isImportant() && isGpuMemoryAvailable()) {
            return ExtractionMethod.ERNIE_PREFERRED;
        }
        
        // 策略3: 复杂文档但GPU资源有限 -> 混合方案
        if (analysis.getComplexityScore() > complexityThreshold) {
            return ExtractionMethod.HYBRID_SMART;
        }
        
        // 策略4: 低质量文档 -> 增强轻量级方案
        if (analysis.getQualityScore() < qualityThreshold) {
            return ExtractionMethod.LIGHTWEIGHT_ENHANCED;
        }
        
        // 默认策略
        return ExtractionMethod.HYBRID_SMART;
    }
    
    /**
     * 执行提取
     */
    private ArchiveElements executeExtraction(File documentFile, String volumeContext, 
                                            ExtractionMethod method, DocumentAnalysis analysis) {
        switch (method) {
            case LIGHTWEIGHT_ONLY:
                return extractWithLightweight(documentFile, analysis);
                
            case ERNIE_PREFERRED:
                return extractWithErnie(documentFile, volumeContext, analysis);
                
            case HYBRID_SMART:
                return extractWithHybridSmart(documentFile, volumeContext, analysis);
                
            case LIGHTWEIGHT_ENHANCED:
                return extractWithLightweightEnhanced(documentFile, analysis);
                
            default:
                return extractWithLightweight(documentFile, analysis);
        }
    }
    
    /**
     * 轻量级提取
     */
    private ArchiveElements extractWithLightweight(File documentFile, DocumentAnalysis analysis) {
        log.debug("使用轻量级提取: {}", documentFile.getName());
        
        ArchiveElements elements = new ArchiveElements();
        elements.setFileName(documentFile.getName());
        elements.setExtractionMethod("LIGHTWEIGHT");
        
        try {
            // 使用规则和TF-IDF进行快速提取
            var ocrResult = paddleOcrService.parseDocument(documentFile);
            
            // 基于规则的要素提取
            elements.setDocumentNumber(extractDocumentNumberByRule(ocrResult));
            elements.setIssueDate(extractIssueDateByRule(ocrResult));
            elements.setTitle(extractTitleByRule(ocrResult));
            elements.setResponsibleParty(extractResponsiblePartyByRule(ocrResult));
            
            // 设置置信度
            Map<String, Double> confidence = new HashMap<>();
            confidence.put("document_number", 0.85);
            confidence.put("issue_date", 0.80);
            confidence.put("title", 0.75);
            confidence.put("responsible_party", 0.70);
            elements.setConfidenceScores(confidence);
            
        } catch (Exception e) {
            log.error("轻量级提取失败", e);
        }
        
        return elements;
    }
    
    /**
     * ERNIE优先提取
     */
    private ArchiveElements extractWithErnie(File documentFile, String volumeContext, DocumentAnalysis analysis) {
        log.debug("使用ERNIE优先提取: {}", documentFile.getName());
        
        try {
            // 构建高质量的提取prompt
            String enhancedPrompt = buildEnhancedPrompt(documentFile.getName(), volumeContext, analysis);
            
            List<String> questions = Arrays.asList(
                "请精确提取文档的题名",
                "请识别文档的责任者",
                "请提取文档的文号",
                "请确定文档的发文日期"
            );
            
            PPChatOCRv4Result result = paddleOcrService.intelligentUnderstanding(
                documentFile, questions, enhancedPrompt, null);
            
            ArchiveElements elements = parseErnieResult(result, documentFile.getName());
            elements.setExtractionMethod("ERNIE_PREFERRED");
            
            return elements;
            
        } catch (Exception e) {
            log.warn("ERNIE提取失败，降级到轻量级: {}", documentFile.getName(), e);
            return extractWithLightweight(documentFile, analysis);
        }
    }
    
    /**
     * 智能混合提取
     */
    private ArchiveElements extractWithHybridSmart(File documentFile, String volumeContext, DocumentAnalysis analysis) {
        log.debug("使用智能混合提取: {}", documentFile.getName());
        
        // 1. 先用轻量级快速提取
        ArchiveElements lightweightResult = extractWithLightweight(documentFile, analysis);
        
        // 2. 对低置信度或重要要素使用ERNIE补充
        ArchiveElements finalResult = lightweightResult;
        finalResult.setExtractionMethod("HYBRID_SMART");
        
        try {
            if (isGpuMemoryAvailable()) {
                // 选择需要ERNIE增强的要素
                Set<String> elementsToEnhance = selectElementsForEnhancement(lightweightResult, analysis);
                
                if (!elementsToEnhance.isEmpty()) {
                    ArchiveElements ernieEnhancement = extractSpecificElementsWithErnie(
                        documentFile, volumeContext, elementsToEnhance);
                    
                    // 合并结果
                    finalResult = mergeResults(lightweightResult, ernieEnhancement, elementsToEnhance);
                }
            }
        } catch (Exception e) {
            log.warn("混合提取的ERNIE增强失败: {}", documentFile.getName(), e);
        }
        
        return finalResult;
    }
    
    /**
     * 增强轻量级提取
     */
    private ArchiveElements extractWithLightweightEnhanced(File documentFile, DocumentAnalysis analysis) {
        log.debug("使用增强轻量级提取: {}", documentFile.getName());
        
        // 基础轻量级提取
        ArchiveElements elements = extractWithLightweight(documentFile, analysis);
        elements.setExtractionMethod("LIGHTWEIGHT_ENHANCED");
        
        try {
            // 增强处理：多轮规则匹配、模糊匹配等
            enhanceWithAdvancedRules(elements, documentFile);
            
            // 提高置信度评估的准确性
            adjustConfidenceScores(elements, analysis);
            
        } catch (Exception e) {
            log.warn("增强处理失败: {}", documentFile.getName(), e);
        }
        
        return elements;
    }
    
    // 辅助方法
    private double calculateQualityScore(Object ocrResult, File file) {
        // 基于OCR结果计算文档质量分数
        // 考虑因素：清晰度、文字识别置信度、布局规整性等
        return 0.8; // 简化实现
    }
    
    private double calculateComplexityScore(Object ocrResult) {
        // 计算文档复杂度
        // 考虑因素：布局复杂性、文字密度、表格图片数量等
        return 0.6; // 简化实现
    }
    
    private boolean assessStructure(Object ocrResult) {
        // 评估文档是否结构化
        return true; // 简化实现
    }
    
    private boolean assessImportance(String fileName, Object ocrResult) {
        // 评估文档重要性
        String[] importantKeywords = {"重要", "紧急", "机密", "通知", "决定"};
        for (String keyword : importantKeywords) {
            if (fileName.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
    
    private Set<String> predictElementTypes(Object ocrResult) {
        // 预测文档包含的要素类型
        return new HashSet<>(Arrays.asList("title", "responsible_party", "document_number", "issue_date"));
    }
    
    private boolean isGpuMemoryAvailable() {
        // 检查GPU显存是否充足
        return true; // 简化实现，实际需要调用GPU监控API
    }
    
    private ArchiveElements fallbackExtraction(File documentFile) {
        // 降级提取方案
        ArchiveElements elements = new ArchiveElements();
        elements.setFileName(documentFile.getName());
        elements.setExtractionMethod("FALLBACK");
        return elements;
    }

    // 规则提取方法
    private String extractDocumentNumberByRule(Object ocrResult) {
        // 基于正则表达式提取文号
        // 模式：XX发〔YYYY〕NN号 或 XX发[YYYY]NN号
        return ""; // 简化实现
    }

    private String extractIssueDateByRule(Object ocrResult) {
        // 基于正则表达式提取日期
        return ""; // 简化实现
    }

    private String extractTitleByRule(Object ocrResult) {
        // 基于位置和格式特征提取题名
        return ""; // 简化实现
    }

    private String extractResponsiblePartyByRule(Object ocrResult) {
        // 基于关键词和位置提取责任者
        return ""; // 简化实现
    }

    private String buildEnhancedPrompt(String fileName, String volumeContext, DocumentAnalysis analysis) {
        // 构建针对性的提取prompt
        return String.format("""
            专业档案要素提取任务
            文档: %s
            质量评分: %.2f
            复杂度: %.2f
            预期要素: %s

            请精确提取档案四要素，返回JSON格式结果。
            """, fileName, analysis.getQualityScore(), analysis.getComplexityScore(),
            String.join(",", analysis.getExpectedElements()));
    }

    private ArchiveElements parseErnieResult(PPChatOCRv4Result result, String fileName) {
        // 解析ERNIE返回结果
        ArchiveElements elements = new ArchiveElements();
        elements.setFileName(fileName);

        // 设置高置信度
        Map<String, Double> confidence = new HashMap<>();
        confidence.put("title", 0.90);
        confidence.put("responsible_party", 0.85);
        confidence.put("document_number", 0.95);
        confidence.put("issue_date", 0.90);
        elements.setConfidenceScores(confidence);

        return elements;
    }

    private Set<String> selectElementsForEnhancement(ArchiveElements lightweightResult, DocumentAnalysis analysis) {
        Set<String> toEnhance = new HashSet<>();

        // 选择低置信度的要素进行ERNIE增强
        for (Map.Entry<String, Double> entry : lightweightResult.getConfidenceScores().entrySet()) {
            if (entry.getValue() < 0.8) {
                toEnhance.add(entry.getKey());
            }
        }

        // 重要文档的关键要素也进行增强
        if (analysis.isImportant()) {
            toEnhance.add("title");
            toEnhance.add("document_number");
        }

        return toEnhance;
    }

    private ArchiveElements extractSpecificElementsWithErnie(File documentFile, String volumeContext, Set<String> elements) {
        // 针对特定要素使用ERNIE提取
        ArchiveElements result = new ArchiveElements();
        result.setFileName(documentFile.getName());

        try {
            List<String> specificQuestions = new ArrayList<>();
            for (String element : elements) {
                switch (element) {
                    case "title":
                        specificQuestions.add("请精确提取文档标题");
                        break;
                    case "responsible_party":
                        specificQuestions.add("请识别发文单位或责任者");
                        break;
                    case "document_number":
                        specificQuestions.add("请提取文件编号");
                        break;
                    case "issue_date":
                        specificQuestions.add("请确定发文日期");
                        break;
                }
            }

            PPChatOCRv4Result chatResult = paddleOcrService.intelligentUnderstanding(
                documentFile, specificQuestions, volumeContext, null);

            result = parseErnieResult(chatResult, documentFile.getName());

        } catch (Exception e) {
            log.error("特定要素ERNIE提取失败", e);
        }

        return result;
    }

    private ArchiveElements mergeResults(ArchiveElements lightweightResult, ArchiveElements ernieResult, Set<String> enhancedElements) {
        ArchiveElements merged = new ArchiveElements();
        merged.setFileName(lightweightResult.getFileName());
        merged.setExtractionMethod("HYBRID_MERGED");

        // 合并要素值
        merged.setTitle(enhancedElements.contains("title") ? ernieResult.getTitle() : lightweightResult.getTitle());
        merged.setResponsibleParty(enhancedElements.contains("responsible_party") ? ernieResult.getResponsibleParty() : lightweightResult.getResponsibleParty());
        merged.setDocumentNumber(enhancedElements.contains("document_number") ? ernieResult.getDocumentNumber() : lightweightResult.getDocumentNumber());
        merged.setIssueDate(enhancedElements.contains("issue_date") ? ernieResult.getIssueDate() : lightweightResult.getIssueDate());

        // 合并置信度
        Map<String, Double> mergedConfidence = new HashMap<>(lightweightResult.getConfidenceScores());
        for (String element : enhancedElements) {
            if (ernieResult.getConfidenceScores().containsKey(element)) {
                mergedConfidence.put(element, ernieResult.getConfidenceScore(element));
            }
        }
        merged.setConfidenceScores(mergedConfidence);

        return merged;
    }

    private void enhanceWithAdvancedRules(ArchiveElements elements, File documentFile) {
        // 高级规则增强：模糊匹配、上下文分析等
        // 这里可以添加更复杂的规则逻辑
    }

    private void adjustConfidenceScores(ArchiveElements elements, DocumentAnalysis analysis) {
        // 根据文档分析结果调整置信度
        Map<String, Double> adjusted = new HashMap<>(elements.getConfidenceScores());

        // 高质量文档提高置信度
        if (analysis.getQualityScore() > 0.8) {
            adjusted.replaceAll((k, v) -> Math.min(v + 0.1, 1.0));
        }

        // 结构化文档的格式化要素置信度更高
        if (analysis.isStructured()) {
            adjusted.put("document_number", Math.min(adjusted.getOrDefault("document_number", 0.0) + 0.15, 1.0));
            adjusted.put("issue_date", Math.min(adjusted.getOrDefault("issue_date", 0.0) + 0.1, 1.0));
        }

        elements.setConfidenceScores(adjusted);
    }

    private ArchiveElements optimizeResults(ArchiveElements elements, DocumentAnalysis analysis) {
        // 结果优化：格式标准化、一致性检查等

        // 1. 格式标准化
        if (elements.getDocumentNumber() != null) {
            elements.setDocumentNumber(normalizeDocumentNumber(elements.getDocumentNumber()));
        }

        if (elements.getIssueDate() != null) {
            elements.setIssueDate(normalizeDate(elements.getIssueDate()));
        }

        // 2. 一致性检查
        validateConsistency(elements);

        // 3. 设置推理过程
        elements.setReasoningProcess(String.format(
            "使用%s策略，文档质量%.2f，复杂度%.2f",
            elements.getExtractionMethod(),
            analysis.getQualityScore(),
            analysis.getComplexityScore()));

        return elements;
    }

    private String normalizeDocumentNumber(String docNumber) {
        // 文号格式标准化
        return docNumber.trim();
    }

    private String normalizeDate(String date) {
        // 日期格式标准化
        return date.trim();
    }

    private void validateConsistency(ArchiveElements elements) {
        // 要素一致性验证
        // 例如：检查日期格式、文号格式等
    }
    
    // 枚举定义
    public enum ExtractionMethod {
        LIGHTWEIGHT_ONLY,      // 纯轻量级
        ERNIE_PREFERRED,       // ERNIE优先
        HYBRID_SMART,          // 智能混合
        LIGHTWEIGHT_ENHANCED   // 增强轻量级
    }
    
    // 文档分析结果类
    public static class DocumentAnalysis {
        private String fileName;
        private long fileSize;
        private double qualityScore;
        private double complexityScore;
        private boolean structured;
        private boolean important;
        private Set<String> expectedElements;
        
        // getters and setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public long getFileSize() { return fileSize; }
        public void setFileSize(long fileSize) { this.fileSize = fileSize; }
        public double getQualityScore() { return qualityScore; }
        public void setQualityScore(double qualityScore) { this.qualityScore = qualityScore; }
        public double getComplexityScore() { return complexityScore; }
        public void setComplexityScore(double complexityScore) { this.complexityScore = complexityScore; }
        public boolean isStructured() { return structured; }
        public void setStructured(boolean structured) { this.structured = structured; }
        public boolean isImportant() { return important; }
        public void setImportant(boolean important) { this.important = important; }
        public Set<String> getExpectedElements() { return expectedElements; }
        public void setExpectedElements(Set<String> expectedElements) { this.expectedElements = expectedElements; }
    }
}
