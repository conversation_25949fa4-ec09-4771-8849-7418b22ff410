# PaddleOCR环境安装脚本
# 适用于Windows 10 + CUDA 12.6 + 双GPU环境
# 作者：智能提取系统
# 日期：2025-06-25

Write-Host "🚀 开始安装PaddleOCR环境..." -ForegroundColor Green
Write-Host "系统信息检查..." -ForegroundColor Yellow

# 检查CUDA版本
try {
    $cudaVersion = nvidia-smi --query-gpu=driver_version --format=csv,noheader,nounits | Select-Object -First 1
    Write-Host "✅ 检测到CUDA驱动版本: $cudaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 无法检测CUDA版本，请确保NVIDIA驱动已安装" -ForegroundColor Red
    exit 1
}

# 检查conda
try {
    $condaVersion = conda --version
    Write-Host "✅ 检测到Conda: $condaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 未检测到Conda，请先安装Anaconda或Miniconda" -ForegroundColor Red
    exit 1
}

Write-Host "`n📦 第1步：创建新的Conda环境..." -ForegroundColor Cyan
Write-Host "环境名称: paddleocr_env" -ForegroundColor Yellow
Write-Host "Python版本: 3.9" -ForegroundColor Yellow

# 删除已存在的环境（如果有）
conda env remove -n paddleocr_env -y 2>$null

# 创建新环境
conda create -n paddleocr_env python=3.9 -y
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 创建Conda环境失败" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Conda环境创建成功" -ForegroundColor Green

Write-Host "`n📦 第2步：激活环境并安装PaddlePaddle..." -ForegroundColor Cyan
Write-Host "版本: PaddlePaddle 3.0.0 (CUDA 12.0)" -ForegroundColor Yellow

# 激活环境并安装PaddlePaddle
& conda activate paddleocr_env
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 激活Conda环境失败" -ForegroundColor Red
    exit 1
}

# 安装PaddlePaddle GPU版本
Write-Host "正在安装PaddlePaddle GPU版本..." -ForegroundColor Yellow
python -m pip install paddlepaddle-gpu==3.0.0 -i https://www.paddlepaddle.org.cn/packages/stable/cu120/
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ PaddlePaddle安装失败" -ForegroundColor Red
    exit 1
}

Write-Host "✅ PaddlePaddle安装成功" -ForegroundColor Green

Write-Host "`n📦 第3步：安装PaddleOCR..." -ForegroundColor Cyan
Write-Host "版本: PaddleOCR >= 2.7.0" -ForegroundColor Yellow

# 安装PaddleOCR
python -m pip install "paddleocr>=2.7.0"
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ PaddleOCR安装失败" -ForegroundColor Red
    exit 1
}

Write-Host "✅ PaddleOCR安装成功" -ForegroundColor Green

Write-Host "`n📦 第4步：安装其他依赖..." -ForegroundColor Cyan

# 安装其他必要依赖
$dependencies = @(
    "opencv-python",
    "pillow",
    "numpy",
    "requests",
    "tqdm",
    "matplotlib"
)

foreach ($dep in $dependencies) {
    Write-Host "安装 $dep..." -ForegroundColor Yellow
    python -m pip install $dep
    if ($LASTEXITCODE -ne 0) {
        Write-Host "⚠️ $dep 安装失败，但继续安装其他依赖" -ForegroundColor Yellow
    } else {
        Write-Host "✅ $dep 安装成功" -ForegroundColor Green
    }
}

Write-Host "`n🔍 第5步：验证安装..." -ForegroundColor Cyan

# 验证PaddlePaddle
Write-Host "验证PaddlePaddle..." -ForegroundColor Yellow
python -c "import paddle; print('PaddlePaddle版本:', paddle.__version__); print('CUDA可用:', paddle.is_compiled_with_cuda()); print('GPU数量:', paddle.device.cuda.device_count() if paddle.is_compiled_with_cuda() else 0)"
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ PaddlePaddle验证失败" -ForegroundColor Red
    exit 1
}

# 验证PaddleOCR
Write-Host "验证PaddleOCR..." -ForegroundColor Yellow
python -c "from paddleocr import PaddleOCR; print('✅ PaddleOCR导入成功')"
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ PaddleOCR验证失败" -ForegroundColor Red
    exit 1
}

Write-Host "`n🎉 安装完成！" -ForegroundColor Green
Write-Host "===============================================" -ForegroundColor Cyan
Write-Host "环境名称: paddleocr_env" -ForegroundColor White
Write-Host "Python版本: 3.9" -ForegroundColor White
Write-Host "PaddlePaddle版本: 3.0.0 (CUDA 12.0)" -ForegroundColor White
Write-Host "PaddleOCR版本: >= 2.7.0" -ForegroundColor White
Write-Host "===============================================" -ForegroundColor Cyan

Write-Host "`n📋 使用说明:" -ForegroundColor Yellow
Write-Host "1. 激活环境: conda activate paddleocr_env" -ForegroundColor White
Write-Host "2. 测试OCR: python -c `"from paddleocr import PaddleOCR; ocr = PaddleOCR(use_angle_cls=True, lang='ch')`"" -ForegroundColor White
Write-Host "3. 运行项目: cd intelligent-extraction && python start_service.py" -ForegroundColor White

Write-Host "`n✨ 环境安装成功，可以开始使用PaddleOCR了！" -ForegroundColor Green
