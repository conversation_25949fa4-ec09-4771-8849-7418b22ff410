#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试混合OCR处理器
验证GPU/CPU智能回退机制
"""

import sys
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from models.ocr_hybrid import HybridOCRProcessor

def test_hybrid_ocr():
    """测试混合OCR处理器"""
    print("🔧 混合OCR处理器测试")
    print("=" * 60)
    
    # 测试图片路径
    test_image = project_root / "uploads" / "125ef0ca-61e6-4cf5-9845-e8b9f3bd1358.jpg"
    
    if not test_image.exists():
        print(f"❌ 测试图片不存在: {test_image}")
        return
    
    print(f"📸 测试图片: {test_image}")
    
    # 创建混合处理器
    processor = HybridOCRProcessor(device_id=0, confidence_threshold=0.5)
    
    print("\n" + "=" * 60)
    print("🧪 测试1: 智能模式（GPU优先，自动回退）")
    print("=" * 60)
    
    start_time = time.time()
    result1 = processor.recognize(test_image, force_cpu=False, enable_fallback=True)
    test1_time = time.time() - start_time
    
    print(f"\n📊 测试1结果:")
    print(f"   ⏱️ 总时间: {test1_time:.2f}秒")
    print(f"   ✅ 成功: {result1.get('success', False)}")
    print(f"   🎯 模式: {result1.get('performance', {}).get('mode_used', 'unknown')}")
    print(f"   📝 文本块: {len(result1.get('results', []))}")
    
    if result1.get('results'):
        print(f"   📄 检测到的文本:")
        for i, item in enumerate(result1['results'][:3]):  # 只显示前3个
            text = item.get('text', '')[:30]  # 限制长度
            confidence = item.get('confidence', 0)
            print(f"      {i+1}. {text}... (置信度: {confidence:.3f})")
    
    print("\n" + "=" * 60)
    print("🧪 测试2: 强制CPU模式")
    print("=" * 60)
    
    start_time = time.time()
    result2 = processor.recognize(test_image, force_cpu=True, enable_fallback=False)
    test2_time = time.time() - start_time
    
    print(f"\n📊 测试2结果:")
    print(f"   ⏱️ 总时间: {test2_time:.2f}秒")
    print(f"   ✅ 成功: {result2.get('success', False)}")
    print(f"   🎯 模式: {result2.get('performance', {}).get('mode_used', 'unknown')}")
    print(f"   📝 文本块: {len(result2.get('results', []))}")
    
    print("\n" + "=" * 60)
    print("🧪 测试3: GPU模式（无回退）")
    print("=" * 60)
    
    # 重置GPU状态以确保测试
    processor.reset_gpu_status()
    
    start_time = time.time()
    result3 = processor.recognize(test_image, force_cpu=False, enable_fallback=False)
    test3_time = time.time() - start_time
    
    print(f"\n📊 测试3结果:")
    print(f"   ⏱️ 总时间: {test3_time:.2f}秒")
    print(f"   ✅ 成功: {result3.get('success', False)}")
    print(f"   🎯 模式: {result3.get('performance', {}).get('mode_used', 'unknown')}")
    print(f"   📝 文本块: {len(result3.get('results', []))}")
    
    # 性能统计
    print("\n" + "=" * 60)
    print("📈 性能统计")
    print("=" * 60)
    
    stats = processor.get_performance_stats()
    print(f"GPU尝试次数: {stats['gpu_attempts']}")
    print(f"GPU成功次数: {stats['gpu_successes']}")
    print(f"GPU失败次数: {stats['gpu_failures']}")
    print(f"CPU回退次数: {stats['cpu_fallbacks']}")
    print(f"GPU成功率: {stats['gpu_success_rate']:.1%}")
    print(f"GPU可用状态: {stats['gpu_available']}")
    
    if stats['avg_gpu_time'] > 0:
        print(f"平均GPU时间: {stats['avg_gpu_time']:.2f}秒")
    if stats['avg_cpu_time'] > 0:
        print(f"平均CPU时间: {stats['avg_cpu_time']:.2f}秒")
    
    # 结果对比
    print("\n" + "=" * 60)
    print("🔍 结果对比分析")
    print("=" * 60)
    
    results = [
        ("智能模式", result1, test1_time),
        ("CPU模式", result2, test2_time),
        ("GPU模式", result3, test3_time)
    ]
    
    successful_results = [(name, r, t) for name, r, t in results if r.get('success')]
    
    if len(successful_results) >= 2:
        print("✅ 多种模式成功，进行性能对比:")
        for name, result, test_time in successful_results:
            mode = result.get('performance', {}).get('mode_used', 'unknown')
            text_count = len(result.get('results', []))
            print(f"   {name}: {test_time:.2f}s, {text_count}个文本块 ({mode})")
        
        # 找出最快的成功模式
        fastest = min(successful_results, key=lambda x: x[2])
        print(f"\n🏆 最快模式: {fastest[0]} ({fastest[2]:.2f}s)")
        
    elif len(successful_results) == 1:
        name, result, test_time = successful_results[0]
        mode = result.get('performance', {}).get('mode_used', 'unknown')
        print(f"✅ 唯一成功模式: {name} ({mode}, {test_time:.2f}s)")
        
    else:
        print("❌ 所有模式都失败了")
    
    # 推荐配置
    print("\n" + "=" * 60)
    print("💡 推荐配置")
    print("=" * 60)
    
    if stats['gpu_success_rate'] > 0:
        print("🚀 建议: 使用智能模式（GPU优先，自动回退）")
        print("   - 优点: 充分利用GPU加速，自动处理GPU失败")
        print("   - 适用: 生产环境，追求最佳性能")
    else:
        print("🖥️ 建议: 使用CPU模式")
        print("   - 原因: GPU模式在当前环境下不稳定")
        print("   - 优点: 稳定可靠，结果准确")
        print("   - 适用: 当前硬件环境")
    
    return processor, stats

def test_multiple_images():
    """测试多张图片的处理"""
    print("\n" + "=" * 60)
    print("🔄 多图片批处理测试")
    print("=" * 60)
    
    # 这里可以添加多张图片的测试
    # 由于我们只有一张测试图片，暂时跳过
    print("⏭️ 跳过多图片测试（需要更多测试图片）")

if __name__ == "__main__":
    try:
        processor, stats = test_hybrid_ocr()
        
        # 可选：测试多张图片
        # test_multiple_images()
        
        print("\n" + "=" * 60)
        print("🎯 测试完成")
        print("=" * 60)
        print("混合OCR处理器测试成功完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
