#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LLM模型修复
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.models.llm_models import QwenLLMModel
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_llm_basic():
    """测试基本LLM功能"""
    print("🧪 测试LLM基本功能...")
    
    try:
        # 创建模型实例
        llm = QwenLLMModel(model_size="4b")
        print(f"✅ LLM模型创建成功: {llm}")
        
        # 测试简单生成
        print("\n📝 测试文本生成...")
        response = llm.generate("你好，请简单介绍一下自己。", max_length=100)
        print(f"✅ 生成成功:")
        print(f"   内容: {response.content}")
        print(f"   模型: {response.model}")
        print(f"   耗时: {response.response_time:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM测试失败: {e}")
        return False

def test_archive_extraction():
    """测试档案要素提取"""
    print("\n📄 测试档案要素提取...")
    
    try:
        llm = QwenLLMModel(model_size="4b")
        
        # 模拟OCR文本
        ocr_text = """
        关于加强档案管理工作的通知
        
        各部门：
        
        为进一步规范档案管理工作，现将有关事项通知如下：
        
        一、严格按照档案管理规定执行
        二、定期整理和归档文件
        
        特此通知。
        
        办公室
        2024年3月15日
        
        文号：办发〔2024〕第15号
        """
        
        # 构建提取提示词
        prompt = f"""请从以下档案文本中提取指定的要素信息。

档案文本：
{ocr_text}

需要提取的要素：题名、责任者、文号、发文日期

请按照以下JSON格式返回结果：
{{
    "题名": "文档标题",
    "责任者": "发文单位或责任人",
    "文号": "文件编号",
    "发文日期": "YYYY-MM-DD格式的日期"
}}

注意：
1. 如果某个要素在文本中找不到，请返回null
2. 日期请统一转换为YYYY-MM-DD格式
3. 只返回JSON，不要其他说明文字
"""
        
        print("🔍 执行要素提取...")
        response = llm.generate(prompt, max_length=512)
        
        print(f"✅ 提取成功:")
        print(f"   原始响应: {response.content}")
        print(f"   耗时: {response.response_time:.2f}秒")
        
        # 尝试解析JSON
        import json
        try:
            if '{' in response.content and '}' in response.content:
                json_start = response.content.find('{')
                json_end = response.content.rfind('}') + 1
                json_str = response.content[json_start:json_end]
                
                result = json.loads(json_str)
                print(f"📊 解析结果:")
                for key, value in result.items():
                    print(f"   {key}: {value}")
            else:
                print("⚠️ 响应中未找到JSON格式")
        except Exception as e:
            print(f"⚠️ JSON解析失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 档案要素提取测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试LLM修复")
    print("=" * 50)
    
    tests = [
        ("基本功能", test_llm_basic),
        ("档案要素提取", test_archive_extraction),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
            print(f"{'✅' if result else '❌'} {test_name}: {'通过' if result else '失败'}")
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results[test_name] = False
    
    # 总结
    print(f"\n{'='*50}")
    print("📊 测试总结:")
    
    passed = sum(1 for r in results.values() if r)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！LLM修复成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
