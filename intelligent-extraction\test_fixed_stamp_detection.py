#!/usr/bin/env python3
"""
测试修复后的印章检测功能
"""

import os
import sys
import json
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_seal_models_fix():
    """测试seal_models.py的修复"""
    print("=" * 60)
    print("测试1: seal_models.py修复验证")
    print("=" * 60)
    
    try:
        from src.models.seal_models import create_seal_detection_model
        
        # 测试配置
        config = {
            'enabled': True,
            'model_name': 'PP-OCRv4_mobile_seal_det',
            'confidence_threshold': 0.8
        }
        
        print("🔍 创建印章检测模型实例...")
        model = create_seal_detection_model(config)
        
        if model:
            print("✅ 印章检测模型实例创建成功")
            
            print("🔍 尝试加载模型...")
            if model.load_model():
                print("✅ 印章检测模型加载成功！")
                return True
            else:
                print("❌ 印章检测模型加载失败")
                return False
        else:
            print("❌ 印章检测模型实例创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ocr_worker_stamp_detection():
    """测试OCR Worker中的印章检测功能"""
    print("\n" + "=" * 60)
    print("测试2: OCR Worker印章检测功能")
    print("=" * 60)
    
    # 使用测试图像
    test_image = project_root /  "Image_00002.jpg"
    if not test_image.exists():
        print(f"❌ 测试图像不存在: {test_image}")
        print("💡 请确保测试图像存在，或使用其他测试图像")
        return False
    
    try:
        from src.models.ocr_subprocess import OCRSubprocessRunner
        
        # 创建OCR子进程运行器
        ocr_runner = OCRSubprocessRunner(device_id=0)
        
        print(f"📄 测试图像: {test_image}")
        print(f"📏 图像大小: {test_image.stat().st_size / 1024:.1f}KB")
        
        # 测试带印章检测的OCR
        print("\n🔍 执行带印章检测的OCR...")
        start_time = time.time()
        
        result = ocr_runner.recognize_with_stamp_detection(
            image_path=test_image,
            confidence_threshold=0.5,
            use_cpu=True,
            enable_stamp_processing=True,
            stamp_confidence_threshold=0.8
        )
        
        processing_time = time.time() - start_time
        print(f"⏱️  处理耗时: {processing_time:.2f}s")
        
        if result.get('success'):
            print(f"✅ 印章检测OCR成功")
            
            # 检查OCR结果
            ocr_results = result.get('results', [])
            print(f"📝 检测到文本块: {len(ocr_results)} 个")
            
            # 检查印章检测结果
            stamp_detection = result.get('stamp_detection', {})
            print(f"🔖 印章检测状态:")
            print(f"   - 启用状态: {stamp_detection.get('enabled', False)}")
            print(f"   - 模型可用: {stamp_detection.get('available', False)}")
            print(f"   - 检测到印章: {stamp_detection.get('total_stamps', 0)} 个")
            
            if stamp_detection.get('stamps'):
                print(f"📋 印章详情:")
                for i, stamp in enumerate(stamp_detection['stamps']):
                    print(f"   印章 {i+1}: 置信度 {stamp['confidence']:.3f}")
            
            return True
        else:
            print(f"❌ 印章检测OCR失败: {result.get('error')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_pool_cleanup():
    """测试模型池清理"""
    print("\n" + "=" * 60)
    print("测试3: 模型池印章检测清理验证")
    print("=" * 60)
    
    try:
        from src.core.model_pool import get_model_pool
        
        # 获取模型池实例
        model_pool = get_model_pool()
        
        print("🔍 初始化模型池...")
        model_pool.initialize()
        print("✅ 模型池初始化成功")
        
        print("🔍 测试废弃的印章检测模型获取...")
        seal_model = model_pool.get_seal_detection_model()
        
        if seal_model is None:
            print("✅ 印章检测模型正确返回None（已废弃）")
            return True
        else:
            print("⚠️  印章检测模型仍然返回实例（可能需要进一步清理）")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始修复后的印章检测功能测试")
    print(f"📁 项目根目录: {project_root}")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: seal_models.py修复
    if test_seal_models_fix():
        success_count += 1
    
    # 测试2: OCR Worker印章检测
    if test_ocr_worker_stamp_detection():
        success_count += 1
    
    # 测试3: 模型池清理
    if test_model_pool_cleanup():
        success_count += 1
    
    # 总结
    print("\n" + "=" * 60)
    print("🏁 测试总结")
    print("=" * 60)
    print(f"✅ 成功: {success_count}/{total_tests}")
    print(f"❌ 失败: {total_tests - success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！印章检测修复成功！")
        print("💡 现在可以通过FastAPI端点使用印章检测功能")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
        print("💡 建议:")
        print("   1. 检查PaddleOCR安装是否完整")
        print("   2. 确保测试图像存在")
        print("   3. 检查模型下载是否成功")

if __name__ == "__main__":
    main()
