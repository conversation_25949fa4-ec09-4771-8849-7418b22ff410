"""
文档处理器 - 负责文档解析和OCR处理
"""
import logging
from typing import Dict, Any, List
from pathlib import Path


class DocumentProcessor:
    """文档处理器"""
    
    def __init__(self, device_manager, model_manager):
        self.device_manager = device_manager
        self.model_manager = model_manager
        self.logger = logging.getLogger(__name__)
        
    def process(self, document_path: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理文档，进行OCR识别和结构化解析
        
        Args:
            document_path: 文档路径
            options: 处理选项
            
        Returns:
            结构化的文档信息
        """
        try:
            self.logger.info(f"开始处理文档: {document_path}")
            
            # 获取OCR模型
            ocr_model = self.model_manager.get_ocr_model()
            
            # 执行OCR识别
            ocr_result = ocr_model.recognize(document_path)
            
            # 提取文本内容
            text_content = self._extract_text_content(ocr_result)
            
            # 构建结构化信息
            structured_info = {
                'document_path': document_path,
                'text': text_content,
                'ocr_result': self._serialize_ocr_result(ocr_result),
                'page_index': options.get('page_index', 0),
                'is_main_page': options.get('is_main_page', True),
                'processing_options': options
            }
            
            self.logger.info(f"文档处理完成: {document_path}, 文本长度: {len(text_content)}")
            return structured_info
            
        except Exception as e:
            self.logger.error(f"文档处理失败: {document_path}, 错误: {e}")
            raise
    
    def _extract_text_content(self, ocr_result) -> str:
        """从OCR结果中提取文本内容"""
        try:
            # 处理DocumentOCRResult对象
            if hasattr(ocr_result, 'full_text'):
                return ocr_result.full_text
            elif hasattr(ocr_result, 'text'):
                return ocr_result.text
            elif hasattr(ocr_result, 'texts'):
                return '\n'.join(ocr_result.texts)
            elif isinstance(ocr_result, dict):
                if 'full_text' in ocr_result:
                    return ocr_result['full_text']
                elif 'text' in ocr_result:
                    return ocr_result['text']
                elif 'texts' in ocr_result:
                    return '\n'.join(ocr_result['texts'])
            elif isinstance(ocr_result, list):
                # 假设是文本行列表
                return '\n'.join(str(item) for item in ocr_result)
            else:
                return str(ocr_result)
        except Exception as e:
            self.logger.warning(f"文本提取失败，使用默认方式: {e}")
            return str(ocr_result)

    def _serialize_ocr_result(self, ocr_result) -> dict:
        """序列化OCR结果为字典"""
        try:
            if hasattr(ocr_result, '__dict__'):
                # 处理dataclass或普通对象
                result_dict = {}
                for key, value in ocr_result.__dict__.items():
                    if hasattr(value, '__dict__'):
                        # 嵌套对象也序列化
                        result_dict[key] = self._serialize_ocr_result(value)
                    elif isinstance(value, list):
                        # 处理列表
                        result_dict[key] = [
                            self._serialize_ocr_result(item) if hasattr(item, '__dict__') else item
                            for item in value
                        ]
                    else:
                        result_dict[key] = value
                return result_dict
            elif isinstance(ocr_result, dict):
                return ocr_result
            else:
                return {'raw_result': str(ocr_result)}
        except Exception as e:
            self.logger.warning(f"OCR结果序列化失败: {e}")
            return {'error': str(e), 'raw_result': str(ocr_result)}
    
    def batch_process(self, document_paths: List[str], options: Dict[str, Any]) -> List[Dict[str, Any]]:
        """批量处理文档"""
        results = []
        for i, doc_path in enumerate(document_paths):
            page_options = {**options, 'page_index': i, 'is_main_page': (i == 0)}
            result = self.process(doc_path, page_options)
            results.append(result)
        return results
