package com.toolbox.extraction.service;

import com.toolbox.extraction.client.ExtractionServiceClient;
import com.toolbox.extraction.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 档案要素提取服务
 * 整合Python智能提取模块到Java业务系统
 */
@Slf4j
@Service
public class ArchiveExtractionService {
    
    @Autowired
    private ExtractionServiceClient extractionClient;
    
    // 临时文件目录
    private final Path tempDir;
    
    public ArchiveExtractionService() throws IOException {
        this.tempDir = Files.createTempDirectory("archive_extraction");
        log.info("档案提取服务初始化完成，临时目录: {}", tempDir);
    }
    
    /**
     * 提取档案要素（主要接口）
     */
    public ArchiveElements extractArchiveElements(MultipartFile file) {
        File tempFile = null;
        try {
            // 保存临时文件
            tempFile = saveTemporaryFile(file);
            
            // 调用Python服务提取
            ExtractionResult result = extractionClient.extractArchiveElements(tempFile);
            
            if (!result.isSuccess()) {
                throw new ExtractionException("档案要素提取失败: " + result.getError());
            }
            
            // 转换为档案要素对象
            ArchiveElements elements = ArchiveElements.fromExtractionResult(result.getResults());
            
            log.info("档案要素提取成功: {}", elements);
            return elements;
            
        } catch (Exception e) {
            log.error("档案要素提取失败", e);
            throw new ExtractionException("档案要素提取失败", e);
        } finally {
            // 清理临时文件
            if (tempFile != null) {
                cleanupTempFile(tempFile);
            }
        }
    }
    
    /**
     * 异步提取档案要素
     */
    public CompletableFuture<ArchiveElements> extractArchiveElementsAsync(MultipartFile file) {
        return CompletableFuture.supplyAsync(() -> extractArchiveElements(file));
    }
    
    /**
     * 批量提取档案要素
     */
    public List<ArchiveElements> batchExtractArchiveElements(List<MultipartFile> files) {
        List<File> tempFiles = new ArrayList<>();
        
        try {
            // 保存所有临时文件
            for (MultipartFile file : files) {
                tempFiles.add(saveTemporaryFile(file));
            }
            
            // 批量提取
            List<String> keyList = Arrays.asList("题名", "责任者", "文号", "发文日期");
            List<ExtractionResult> results = extractionClient.batchExtract(tempFiles, keyList, true);
            
            // 转换结果
            List<ArchiveElements> elementsList = new ArrayList<>();
            for (ExtractionResult result : results) {
                if (result.isSuccess()) {
                    elementsList.add(ArchiveElements.fromExtractionResult(result.getResults()));
                } else {
                    log.warn("批量提取中的单个文件失败: {}", result.getError());
                    elementsList.add(new ArchiveElements()); // 空结果
                }
            }
            
            return elementsList;
            
        } catch (Exception e) {
            log.error("批量档案要素提取失败", e);
            throw new ExtractionException("批量档案要素提取失败", e);
        } finally {
            // 清理所有临时文件
            tempFiles.forEach(this::cleanupTempFile);
        }
    }
    
    /**
     * 自定义信息提取
     */
    public Map<String, Object> extractCustomInformation(MultipartFile file, 
                                                       List<String> keyList,
                                                       ExtractionOptions options) {
        File tempFile = null;
        try {
            tempFile = saveTemporaryFile(file);
            
            Map<String, Object> optionsMap = options != null ? options.toMap() : new HashMap<>();
            
            ExtractionResult result = extractionClient.extractCustom(
                tempFile, keyList, optionsMap, true
            );
            
            if (!result.isSuccess()) {
                throw new ExtractionException("自定义信息提取失败: " + result.getError());
            }
            
            return result.getResults();
            
        } catch (Exception e) {
            log.error("自定义信息提取失败", e);
            throw new ExtractionException("自定义信息提取失败", e);
        } finally {
            if (tempFile != null) {
                cleanupTempFile(tempFile);
            }
        }
    }
    
    /**
     * 验证档案要素完整性
     */
    public ArchiveValidationResult validateArchiveElements(ArchiveElements elements) {
        ArchiveValidationResult validation = new ArchiveValidationResult();
        
        // 检查必填字段
        if (elements.getTitle() == null || elements.getTitle().trim().isEmpty()) {
            validation.addError("题名", "题名不能为空");
        }
        
        if (elements.getResponsible() == null || elements.getResponsible().trim().isEmpty()) {
            validation.addError("责任者", "责任者不能为空");
        }
        
        if (elements.getDocumentNo() == null || elements.getDocumentNo().trim().isEmpty()) {
            validation.addError("文号", "文号不能为空");
        }
        
        if (elements.getIssueDate() == null || elements.getIssueDate().trim().isEmpty()) {
            validation.addError("发文日期", "发文日期不能为空");
        } else {
            // 验证日期格式
            if (!isValidDate(elements.getIssueDate())) {
                validation.addWarning("发文日期", "日期格式可能不正确: " + elements.getIssueDate());
            }
        }
        
        // 检查文号格式
        if (elements.getDocumentNo() != null && !isValidDocumentNumber(elements.getDocumentNo())) {
            validation.addWarning("文号", "文号格式可能不正确: " + elements.getDocumentNo());
        }
        
        validation.setValid(validation.getErrors().isEmpty());
        
        return validation;
    }
    
    /**
     * 获取提取服务状态
     */
    @Cacheable(value = "systemStatus", unless = "#result == null")
    public SystemStatus getExtractionServiceStatus() {
        try {
            return extractionClient.getSystemStatus();
        } catch (Exception e) {
            log.error("获取提取服务状态失败", e);
            return new SystemStatus("error", null, null, null);
        }
    }
    
    /**
     * 检查提取服务健康状态
     */
    public boolean isExtractionServiceHealthy() {
        return extractionClient.isHealthy();
    }
    
    /**
     * 保存临时文件
     */
    private File saveTemporaryFile(MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("上传文件为空");
        }
        
        // 验证文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new IllegalArgumentException("文件名为空");
        }
        
        String extension = getFileExtension(originalFilename);
        if (!isSupportedFormat(extension)) {
            throw new IllegalArgumentException("不支持的文件格式: " + extension);
        }
        
        // 生成临时文件
        String tempFileName = UUID.randomUUID().toString() + extension;
        Path tempFilePath = tempDir.resolve(tempFileName);
        
        // 保存文件
        Files.copy(file.getInputStream(), tempFilePath, StandardCopyOption.REPLACE_EXISTING);
        
        return tempFilePath.toFile();
    }
    
    /**
     * 清理临时文件
     */
    private void cleanupTempFile(File tempFile) {
        try {
            if (tempFile != null && tempFile.exists()) {
                Files.delete(tempFile.toPath());
            }
        } catch (IOException e) {
            log.warn("清理临时文件失败: {}", tempFile.getPath(), e);
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex).toLowerCase() : "";
    }
    
    /**
     * 检查是否支持的文件格式
     */
    private boolean isSupportedFormat(String extension) {
        Set<String> supportedFormats = Set.of(
            ".jpg", ".jpeg", ".png", ".bmp", ".tiff",
            ".pdf", ".doc", ".docx"
        );
        return supportedFormats.contains(extension);
    }
    
    /**
     * 验证日期格式
     */
    private boolean isValidDate(String dateStr) {
        // 简单的日期格式验证
        return dateStr.matches("\\d{4}[-年]\\d{1,2}[-月]\\d{1,2}[日]?") ||
               dateStr.matches("\\d{4}/\\d{1,2}/\\d{1,2}") ||
               dateStr.matches("\\d{4}\\.\\d{1,2}\\.\\d{1,2}");
    }
    
    /**
     * 验证文号格式
     */
    private boolean isValidDocumentNumber(String docNo) {
        // 简单的文号格式验证
        return docNo.matches(".*[号字].*") || docNo.matches(".*\\d+.*");
    }
}

/**
 * 档案验证结果
 */
@Data
class ArchiveValidationResult {
    private boolean valid;
    private Map<String, String> errors = new HashMap<>();
    private Map<String, String> warnings = new HashMap<>();
    
    public void addError(String field, String message) {
        errors.put(field, message);
    }
    
    public void addWarning(String field, String message) {
        warnings.put(field, message);
    }
    
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
}
