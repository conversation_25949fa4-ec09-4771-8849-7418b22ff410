package test.backend.archive;

import com.deeppaas.archive.extractor.SimpleIntelligentExtractor;
import com.deeppaas.archive.model.ArchiveElements;
import com.deeppaas.vector.InMemoryVectorService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 档案要素提取测试
 */
@SpringBootTest
public class ArchiveElementExtractionTest {
    
    @Autowired
    private SimpleIntelligentExtractor intelligentExtractor;
    
    @Autowired
    private InMemoryVectorService vectorService;
    
    @Test
    public void testSingleVolumeExtraction() {
        // 准备测试文档
        List<File> testDocuments = Arrays.asList(
            new File("test/data/archive/doc1.jpg"),
            new File("test/data/archive/doc2.jpg"),
            new File("test/data/archive/doc3.pdf")
        );
        
        String volumeId = "test_volume_001";
        
        try {
            // 执行要素提取
            Map<String, ArchiveElements> results = intelligentExtractor.extractVolumeElements(
                volumeId, testDocuments);
            
            // 验证结果
            assert !results.isEmpty();
            
            for (Map.Entry<String, ArchiveElements> entry : results.entrySet()) {
                String fileName = entry.getKey();
                ArchiveElements elements = entry.getValue();
                
                System.out.println("文档: " + fileName);
                System.out.println("题名: " + elements.getTitle());
                System.out.println("责任者: " + elements.getResponsibleParty());
                System.out.println("文号: " + elements.getDocumentNumber());
                System.out.println("发文日期: " + elements.getIssueDate());
                System.out.println("平均置信度: " + elements.getAverageConfidence());
                System.out.println("---");
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            assert false : "要素提取失败: " + e.getMessage();
        }
    }
    
    @Test
    public void testMemoryVectorService() {
        List<File> testDocuments = Arrays.asList(
            new File("test/data/archive/sample1.jpg"),
            new File("test/data/archive/sample2.jpg")
        );
        
        String volumeId = "test_memory_vector";
        
        try {
            // 构建向量
            vectorService.buildVolumeVectors(volumeId, testDocuments);
            
            // 测试搜索
            var similarTexts = vectorService.searchSimilarTexts(volumeId, "通知", 5);
            assert !similarTexts.isEmpty();
            
            // 测试上下文获取
            String context = vectorService.getVolumeContext(volumeId, 1000);
            assert !context.isEmpty();
            
            // 检查内存使用
            Map<String, Object> memoryUsage = vectorService.getMemoryUsage();
            System.out.println("内存使用情况: " + memoryUsage);
            
            // 清理
            vectorService.clearVolumeVectors(volumeId);
            
        } catch (Exception e) {
            e.printStackTrace();
            assert false : "内存向量测试失败: " + e.getMessage();
        }
    }
    
    @Test
    public void testPerformance() {
        // 性能测试：100个文档
        List<File> largeDocumentSet = generateTestDocuments(100);
        String volumeId = "performance_test";
        
        long startTime = System.currentTimeMillis();
        
        try {
            Map<String, ArchiveElements> results = intelligentExtractor.extractVolumeElements(
                volumeId, largeDocumentSet);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            System.out.println("处理100个文档耗时: " + duration + "ms");
            System.out.println("平均每个文档: " + (duration / 100.0) + "ms");
            System.out.println("成功提取: " + results.size() + " 个文档");
            
            // 验证性能要求（每个文档平均不超过5秒）
            assert (duration / 100.0) < 5000 : "性能不达标";
            
        } catch (Exception e) {
            e.printStackTrace();
            assert false : "性能测试失败: " + e.getMessage();
        }
    }
    
    private List<File> generateTestDocuments(int count) {
        // 生成测试文档列表
        // 实际实现中需要准备真实的测试文档
        return Arrays.asList(); // 简化实现
    }
}
