package com.deeppaas.rule.factory.impl.archive;

import com.deeppaas.archive.extractor.SimpleIntelligentExtractor;
import com.deeppaas.archive.model.ArchiveElements;
import com.deeppaas.rule.factory.RuleExecuteFactoryBase;
import com.deeppaas.rule.factory.RuleExecuteFactoryService;
import com.deeppaas.rule.dto.PublicRuleDTO;
import com.deeppaas.task.dto.*;
import com.deeppaas.task.entity.TaskErrorResultDO;
import com.deeppaas.common.helper.JsonHelper;
import com.deeppaas.common.helper.StringHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 简化的档案要素检查规则
 * 基于内存向量和PP-ChatOCRv4的智能提取
 */
@Service("rule_execute_simpleArchiveElement")
@Slf4j
public class SimpleArchiveElementRule extends RuleExecuteFactoryBase implements RuleExecuteFactoryService {
    
    @Autowired
    private SimpleIntelligentExtractor intelligentExtractor;
    
    @Override
    public List<TaskErrorResultDO> ruleExecute(PublicRuleDTO ruleDTO, 
                                               List<ProjectTaskFormDataDTO> formDataDTOList,
                                               List<ProjectTaskImageDataDTO> taskImageDataDTOList,
                                               ProjectTaskInfoDTO projectTaskInfoDTO,
                                               ProjectTaskPdfDataDTO projectTaskPdfDataDTO,
                                               ProjectTaskConfigDTO taskConfigDTO) {
        
        List<TaskErrorResultDO> errorResults = new ArrayList<>();
        
        try {
            log.info("开始执行简化档案要素检查规则，任务ID: {}", projectTaskInfoDTO.getId());
            
            // 1. 收集所有文档文件
            List<File> documentFiles = collectAllDocuments(taskImageDataDTOList, projectTaskPdfDataDTO);
            
            if (documentFiles.isEmpty()) {
                log.warn("未找到任何文档文件");
                return errorResults;
            }
            
            // 2. 生成卷ID
            String volumeId = "volume_" + projectTaskInfoDTO.getId() + "_" + System.currentTimeMillis();
            
            // 3. 批量提取档案要素
            Map<String, ArchiveElements> extractedElements = intelligentExtractor.extractVolumeElements(
                volumeId, documentFiles);
            
            // 4. 与条目数据进行比对
            for (Map.Entry<String, ArchiveElements> entry : extractedElements.entrySet()) {
                String fileName = entry.getKey();
                ArchiveElements elements = entry.getValue();
                
                // 找到对应的条目数据
                ProjectTaskFormDataDTO matchingFormData = findMatchingFormData(
                    formDataDTOList, fileName, elements);
                
                if (matchingFormData != null) {
                    List<TaskErrorResultDO> documentErrors = compareElementsWithFormData(
                        elements, matchingFormData, fileName, ruleDTO);
                    errorResults.addAll(documentErrors);
                } else {
                    log.warn("未找到文档 {} 对应的条目数据", fileName);
                }
            }
            
            log.info("简化档案要素检查完成，发现 {} 个问题", errorResults.size());
            
        } catch (Exception e) {
            log.error("简化档案要素检查规则执行失败", e);
            errorResults.add(buildRuleExecutionError(ruleDTO, e.getMessage()));
        }
        
        return errorResults;
    }
    
    /**
     * 收集所有文档文件
     */
    private List<File> collectAllDocuments(List<ProjectTaskImageDataDTO> imageDataList,
                                          ProjectTaskPdfDataDTO pdfData) {
        List<File> documents = new ArrayList<>();
        
        // 添加图像文件
        if (imageDataList != null) {
            for (ProjectTaskImageDataDTO imageData : imageDataList) {
                File imageFile = new File(imageData.getImageFilePath());
                if (imageFile.exists()) {
                    documents.add(imageFile);
                } else {
                    log.warn("图像文件不存在: {}", imageData.getImageFilePath());
                }
            }
        }
        
        // 添加PDF文件
        if (pdfData != null && !StringHelper.isEmpty(pdfData.getPdfFilePath())) {
            File pdfFile = new File(pdfData.getPdfFilePath());
            if (pdfFile.exists()) {
                documents.add(pdfFile);
            } else {
                log.warn("PDF文件不存在: {}", pdfData.getPdfFilePath());
            }
        }
        
        return documents;
    }
    
    /**
     * 查找匹配的条目数据
     */
    private ProjectTaskFormDataDTO findMatchingFormData(List<ProjectTaskFormDataDTO> formDataList,
                                                       String fileName,
                                                       ArchiveElements elements) {
        if (formDataList == null || formDataList.isEmpty()) {
            return null;
        }
        
        // 策略1: 通过dataKey匹配
        String fileNameWithoutExt = removeFileExtension(fileName);
        for (ProjectTaskFormDataDTO formData : formDataList) {
            if (fileNameWithoutExt.equals(formData.getDataKey())) {
                return formData;
            }
        }
        
        // 策略2: 通过题名相似度匹配
        if (!StringHelper.isEmpty(elements.getTitle())) {
            ProjectTaskFormDataDTO bestMatch = null;
            double bestSimilarity = 0.0;
            
            for (ProjectTaskFormDataDTO formData : formDataList) {
                try {
                    Map<String, Object> formDataMap = JsonHelper.fromJson(formData.getTaskJson(), Map.class);
                    String formTitle = (String) formDataMap.get("题名");
                    
                    if (!StringHelper.isEmpty(formTitle)) {
                        double similarity = calculateTextSimilarity(elements.getTitle(), formTitle);
                        if (similarity > bestSimilarity && similarity > 0.7) {
                            bestSimilarity = similarity;
                            bestMatch = formData;
                        }
                    }
                } catch (Exception e) {
                    log.warn("解析条目数据失败: {}", formData.getDataKey(), e);
                }
            }
            
            if (bestMatch != null) {
                log.info("通过题名匹配找到条目: {} -> {} (相似度: {})", 
                    fileName, bestMatch.getDataKey(), bestSimilarity);
                return bestMatch;
            }
        }
        
        // 策略3: 如果只有一个条目数据，直接使用
        if (formDataList.size() == 1) {
            log.info("只有一个条目数据，直接匹配: {} -> {}", fileName, formDataList.get(0).getDataKey());
            return formDataList.get(0);
        }
        
        return null;
    }
    
    /**
     * 比对要素与条目数据
     */
    private List<TaskErrorResultDO> compareElementsWithFormData(ArchiveElements elements,
                                                               ProjectTaskFormDataDTO formData,
                                                               String fileName,
                                                               PublicRuleDTO ruleDTO) {
        List<TaskErrorResultDO> errors = new ArrayList<>();
        
        try {
            Map<String, Object> formDataMap = JsonHelper.fromJson(formData.getTaskJson(), Map.class);
            
            // 比对题名
            errors.addAll(compareElement("题名", 
                elements.getTitle(), 
                (String) formDataMap.get("题名"),
                elements.getConfidenceScore("title"),
                fileName, formData, ruleDTO));
            
            // 比对责任者
            errors.addAll(compareElement("责任者",
                elements.getResponsibleParty(),
                (String) formDataMap.get("责任者"),
                elements.getConfidenceScore("responsible_party"),
                fileName, formData, ruleDTO));
            
            // 比对文号
            errors.addAll(compareElement("文号",
                elements.getDocumentNumber(),
                (String) formDataMap.get("文号"),
                elements.getConfidenceScore("document_number"),
                fileName, formData, ruleDTO));
            
            // 比对发文日期
            errors.addAll(compareElement("发文日期",
                elements.getIssueDate(),
                (String) formDataMap.get("发文日期"),
                elements.getConfidenceScore("issue_date"),
                fileName, formData, ruleDTO));
                
        } catch (Exception e) {
            log.error("要素比对失败: {}", fileName, e);
            errors.add(buildComparisonError(fileName, formData, ruleDTO, e.getMessage()));
        }
        
        return errors;
    }
    
    /**
     * 比对单个要素
     */
    private List<TaskErrorResultDO> compareElement(String elementName,
                                                  String extractedValue,
                                                  String expectedValue,
                                                  Double confidence,
                                                  String fileName,
                                                  ProjectTaskFormDataDTO formData,
                                                  PublicRuleDTO ruleDTO) {
        List<TaskErrorResultDO> errors = new ArrayList<>();
        
        // 如果提取置信度过低，记录低置信度错误
        if (confidence != null && confidence < 0.6) {
            errors.add(buildLowConfidenceError(elementName, extractedValue, confidence, 
                fileName, formData, ruleDTO));
        }
        
        // 如果期望值为空，跳过比对
        if (StringHelper.isEmpty(expectedValue)) {
            return errors;
        }
        
        // 如果提取值为空，记录缺失错误
        if (StringHelper.isEmpty(extractedValue)) {
            errors.add(buildMissingElementError(elementName, expectedValue, 
                fileName, formData, ruleDTO));
            return errors;
        }
        
        // 执行相似度比较
        double similarity = calculateElementSimilarity(extractedValue, expectedValue, elementName);
        double threshold = getElementThreshold(elementName);
        
        if (similarity < threshold) {
            errors.add(buildElementMismatchError(elementName, expectedValue, extractedValue, 
                similarity, confidence, fileName, formData, ruleDTO));
        }
        
        return errors;
    }
    
    /**
     * 计算要素相似度
     */
    private double calculateElementSimilarity(String value1, String value2, String elementType) {
        if (StringHelper.isEmpty(value1) || StringHelper.isEmpty(value2)) {
            return 0.0;
        }
        
        switch (elementType) {
            case "文号":
                // 文号要求精确匹配
                return value1.trim().equals(value2.trim()) ? 1.0 : 0.0;
                
            case "发文日期":
                // 日期进行标准化后比较
                return compareDatesIntelligently(value1, value2);
                
            case "题名":
            case "责任者":
                // 文本内容进行相似度比较
                return calculateTextSimilarity(value1, value2);
                
            default:
                return calculateTextSimilarity(value1, value2);
        }
    }
    
    /**
     * 计算文本相似度
     */
    private double calculateTextSimilarity(String text1, String text2) {
        if (StringHelper.isEmpty(text1) || StringHelper.isEmpty(text2)) {
            return 0.0;
        }
        
        // 简化的相似度计算
        String clean1 = text1.replaceAll("[\\p{Punct}\\s]", "").toLowerCase();
        String clean2 = text2.replaceAll("[\\p{Punct}\\s]", "").toLowerCase();
        
        if (clean1.equals(clean2)) {
            return 1.0;
        }
        
        // 计算编辑距离相似度
        int distance = calculateEditDistance(clean1, clean2);
        int maxLength = Math.max(clean1.length(), clean2.length());
        
        return maxLength > 0 ? 1.0 - (double) distance / maxLength : 0.0;
    }
    
    /**
     * 智能日期比较
     */
    private double compareDatesIntelligently(String date1, String date2) {
        // 标准化日期格式后比较
        String normalized1 = normalizeDateString(date1);
        String normalized2 = normalizeDateString(date2);
        
        return normalized1.equals(normalized2) ? 1.0 : 0.0;
    }
    
    /**
     * 获取要素阈值
     */
    private double getElementThreshold(String elementName) {
        switch (elementName) {
            case "文号":
            case "发文日期":
                return 1.0; // 要求精确匹配
            case "题名":
                return 0.8; // 允许一定差异
            case "责任者":
                return 0.7; // 允许较大差异
            default:
                return 0.8;
        }
    }
    
    /**
     * 构建要素不匹配错误
     */
    private TaskErrorResultDO buildElementMismatchError(String elementName,
                                                       String expectedValue,
                                                       String actualValue,
                                                       double similarity,
                                                       Double confidence,
                                                       String fileName,
                                                       ProjectTaskFormDataDTO formData,
                                                       PublicRuleDTO ruleDTO) {
        return TaskErrorResultDO.builder()
            .taskId(formData.getTaskId())
            .taskConfigId(formData.getTaskConfigId())
            .fieldName(elementName)
            .ruleType("档案要素一致性检查")
            .ruleName(ruleDTO.getRuleAliasName())
            .errorType(1) // 表单错误类型
            .dataKey(formData.getDataKey())
            .errorFileValue(String.format("文件: %s, 期望: %s, 实际: %s, 相似度: %.2f, 置信度: %.2f", 
                fileName, expectedValue, actualValue, similarity, confidence != null ? confidence : 0.0))
            .aiCheck(1) // AI检查
            .build();
    }
    
    /**
     * 构建低置信度错误
     */
    private TaskErrorResultDO buildLowConfidenceError(String elementName,
                                                     String extractedValue,
                                                     Double confidence,
                                                     String fileName,
                                                     ProjectTaskFormDataDTO formData,
                                                     PublicRuleDTO ruleDTO) {
        return TaskErrorResultDO.builder()
            .taskId(formData.getTaskId())
            .taskConfigId(formData.getTaskConfigId())
            .fieldName(elementName)
            .ruleType("档案要素提取置信度")
            .ruleName(ruleDTO.getRuleAliasName())
            .errorType(1)
            .dataKey(formData.getDataKey())
            .errorFileValue(String.format("文件: %s, 提取值: %s, 置信度过低: %.2f", 
                fileName, extractedValue, confidence))
            .aiCheck(1)
            .build();
    }
    
    // 辅助方法
    private String removeFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
    }
    
    private String normalizeDateString(String dateStr) {
        // 简化的日期标准化
        return dateStr.replaceAll("[年月日\\-\\./]", "").trim();
    }
    
    private int calculateEditDistance(String s1, String s2) {
        // 简化的编辑距离计算
        int[][] dp = new int[s1.length() + 1][s2.length() + 1];
        
        for (int i = 0; i <= s1.length(); i++) {
            for (int j = 0; j <= s2.length(); j++) {
                if (i == 0) {
                    dp[i][j] = j;
                } else if (j == 0) {
                    dp[i][j] = i;
                } else if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = 1 + Math.min(Math.min(dp[i][j - 1], dp[i - 1][j]), dp[i - 1][j - 1]);
                }
            }
        }
        
        return dp[s1.length()][s2.length()];
    }
}
