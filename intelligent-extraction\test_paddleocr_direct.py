#!/usr/bin/env python3
"""
直接测试PaddleOCR 3.0是否能正常工作
"""
import os
import sys
from pathlib import Path

def test_paddleocr_direct():
    """直接测试PaddleOCR"""
    print("=== 直接测试PaddleOCR 3.0 ===")
    
    # 1. 测试导入
    try:
        print("1. 测试PaddleOCR导入...")
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR导入成功")
    except Exception as e:
        print(f"❌ PaddleOCR导入失败: {e}")
        return
    
    # 2. 创建测试图像
    test_image_path = Path(__file__).parent / "test_image.png"
    if not test_image_path.exists():
        print("2. 创建测试图像...")
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # 创建简单的测试图像
            img = Image.new('RGB', (400, 100), color='white')
            draw = ImageDraw.Draw(img)
            
            try:
                font = ImageFont.truetype("arial.ttf", 24)
            except:
                font = ImageFont.load_default()
            
            draw.text((50, 30), "测试文本 Test Text", fill='black', font=font)
            img.save(test_image_path)
            print(f"✅ 测试图像已创建: {test_image_path}")
        except Exception as e:
            print(f"❌ 创建测试图像失败: {e}")
            return
    
    # 3. 测试OCR初始化（CPU模式）
    try:
        print("3. 测试OCR初始化（CPU模式）...")
        # 使用PaddleOCR 3.0的新参数格式
        ocr = PaddleOCR(use_textline_orientation=True, lang='ch', device='cpu')
        print("✅ OCR初始化成功（CPU模式）")
    except Exception as e:
        print(f"❌ OCR初始化失败（CPU模式）: {e}")

        # 尝试使用PaddleX方式
        try:
            print("3.1 尝试使用PaddleX方式初始化...")
            import paddlex as pdx
            ocr = pdx.create_pipeline("OCR")
            print("✅ PaddleX OCR初始化成功")
        except Exception as e2:
            print(f"❌ PaddleX OCR初始化也失败: {e2}")
            return
    
    # 4. 测试OCR识别
    try:
        print("4. 测试OCR识别...")
        results = ocr.ocr(str(test_image_path), cls=True)
        print(f"✅ OCR识别成功，结果: {results}")
        
        # 处理结果
        if results and results[0]:
            for line in results[0]:
                if line and len(line) >= 2:
                    bbox = line[0]
                    text_info = line[1]
                    if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                        text = text_info[0]
                        confidence = float(text_info[1])
                        print(f"  文本: {text}, 置信度: {confidence:.3f}")
        
    except Exception as e:
        print(f"❌ OCR识别失败: {e}")
        return
    
    # 5. 测试GPU模式（如果可用）
    try:
        print("5. 测试OCR初始化（GPU模式）...")
        ocr_gpu = PaddleOCR(use_angle_cls=True, lang='ch', device='gpu')
        print("✅ OCR初始化成功（GPU模式）")
        
        results_gpu = ocr_gpu.ocr(str(test_image_path), cls=True)
        print(f"✅ GPU模式OCR识别成功")
        
    except Exception as e:
        print(f"⚠️ GPU模式测试失败（这是正常的，如果没有GPU）: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_paddleocr_direct()
