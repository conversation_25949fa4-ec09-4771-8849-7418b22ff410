#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比不同大小图像的OCR处理效果
使用相同的代码测试小图像和大图像
"""
import os
import sys
import json
import tempfile
import subprocess
import time
from pathlib import Path

def test_ocr_with_image(image_path, image_name="测试图像"):
    """使用指定图像测试OCR子进程"""
    print(f"\n=== {image_name} OCR测试 ===")
    
    # 显示图像信息
    image_file = Path(image_path)
    if not image_file.exists():
        print(f"❌ 图像文件不存在: {image_path}")
        return False
    
    try:
        from PIL import Image
        with Image.open(image_file) as img:
            width, height = img.size
            file_size = image_file.stat().st_size
            print(f"📊 图像信息: {width}×{height}, {file_size/1024:.1f}KB, {img.format}")
    except Exception as e:
        print(f"⚠️ 无法获取图像信息: {e}")
    
    # 1. 检查worker脚本
    script_dir = Path(__file__).parent / "src" / "models"
    worker_script = script_dir / "ocr_worker.py"
    
    if not worker_script.exists():
        print("❌ Worker脚本不存在")
        return False
    
    # 2. 创建配置文件
    config = {
        'image_path': str(image_path),
        'use_angle_cls': True,
        'lang': 'ch',
        'use_cpu': True,
        'confidence_threshold': 0.3
    }
    
    config_file = None
    try:
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
            config_file = f.name
        
        print(f"配置文件: {config_file}")
        
        # 3. 直接测试Worker脚本
        print("\n=== 直接测试Worker脚本 ===")
        cmd = [sys.executable, str(worker_script), config_file]
        print(f"命令: {' '.join(cmd)}")
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',  # 忽略编码错误
                timeout=120,  # 2分钟超时
                env=dict(os.environ, PYTHONIOENCODING='utf-8')
            )
            
            processing_time = time.time() - start_time
            
            print(f"⏱️ 处理时间: {processing_time:.2f}秒")
            print(f"返回码: {result.returncode}")
            
            if result.stdout:
                print(f"标准输出长度: {len(result.stdout)} 字符")
                # 只显示前500个字符避免输出过长
                stdout_preview = result.stdout[:500]
                if len(result.stdout) > 500:
                    stdout_preview += "..."
                print(f"标准输出预览: {stdout_preview}")
            
            if result.stderr:
                print(f"标准错误: {result.stderr}")
            
            if result.returncode == 0:
                # 尝试解析结果
                try:
                    ocr_result = json.loads(result.stdout)
                    if ocr_result.get('success'):
                        results = ocr_result.get('results', [])
                        print(f"✅ OCR处理成功: 检测到{len(results)}个文本块")
                        
                        # 显示前3个文本块
                        for i, item in enumerate(results[:3], 1):
                            text = item.get('text', '')
                            confidence = item.get('confidence', 0)
                            print(f"   {i}. '{text}' (置信度: {confidence:.3f})")
                        
                        if len(results) > 3:
                            print(f"   ... 还有 {len(results) - 3} 个文本块")
                        
                        return True
                    else:
                        print(f"❌ OCR处理失败: {ocr_result.get('error', '未知错误')}")
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"❌ 结果解析失败: {e}")
                    return False
            else:
                print(f"❌ 子进程执行失败，返回码: {result.returncode}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ 子进程超时")
            return False
        except Exception as e:
            print(f"❌ 子进程运行异常: {e}")
            return False
            
    finally:
        # 清理临时文件
        if config_file and os.path.exists(config_file):
            try:
                os.unlink(config_file)
            except:
                pass


def create_small_test_image():
    """创建小测试图像"""
    from PIL import Image, ImageDraw, ImageFont
    
    # 创建简单的测试图像
    img = Image.new('RGB', (400, 100), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        # 使用默认字体
        font = ImageFont.load_default()
    
    draw.text((50, 30), "测试文本 Test Text", fill='black', font=font)
    
    test_image_path = Path(__file__).parent / "small_test_image.jpg"
    img.save(test_image_path)
    
    return str(test_image_path)


def main():
    """主函数"""
    print("🧪 对比不同大小图像的OCR处理效果")
    print("=" * 60)
    
    # 测试1: 小图像
    print("\n📋 测试1: 小图像")
    small_image = create_small_test_image()
    small_success = test_ocr_with_image(small_image, "小图像(400×100)")
    
    # 测试2: 大图像（如果提供了路径）
    large_success = False
    if len(sys.argv) > 1:
        large_image_path = sys.argv[1]
        print(f"\n📋 测试2: 大图像")
        large_success = test_ocr_with_image(large_image_path, "大图像")
    else:
        print(f"\n📋 测试2: 大图像")
        print("❌ 未提供大图像路径")
        print("用法: python compare_image_sizes_ocr.py <大图像路径>")
        print("示例: python compare_image_sizes_ocr.py G:\\tmp\\Image_00002.jpg")
    
    # 总结
    print(f"\n🎯 测试结果总结:")
    print(f"   小图像测试: {'✅ 成功' if small_success else '❌ 失败'}")
    if len(sys.argv) > 1:
        print(f"   大图像测试: {'✅ 成功' if large_success else '❌ 失败'}")
    
    if small_success and not large_success and len(sys.argv) > 1:
        print(f"\n💡 结论: 小图像可以正常处理，大图像处理失败")
        print(f"   这证实了图像大小是导致OCR子进程失败的原因")
    elif small_success and large_success:
        print(f"\n💡 结论: 小图像和大图像都可以正常处理")
        print(f"   OCR子进程功能正常，可能是其他原因导致的问题")
    elif not small_success:
        print(f"\n💡 结论: 连小图像都处理失败，OCR子进程本身有问题")


if __name__ == "__main__":
    main()
