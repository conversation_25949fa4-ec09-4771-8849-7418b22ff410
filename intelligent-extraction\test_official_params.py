#!/usr/bin/env python3
"""
测试官方推荐参数配置
验证PP-OCRv5_server_det和PP-OCRv5_server_rec模型的效果
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from models.ocr_subprocess import OCRSubprocessRunner

def test_official_params():
    """测试官方推荐参数配置"""
    
    # 测试图像路径
    test_image = project_root / "uploads/125ef0ca-61e6-4cf5-9845-e8b9f3bd1358.jpg"
    
    if not test_image.exists():
        print(f"❌ 测试图像不存在: {test_image}")
        return False
    
    print("🔧 测试官方推荐参数配置")
    print("=" * 60)
    
    # 配置参数
    configs = {
        "CPU模式 + 官方参数": {
            'use_cpu': True,
            'use_angle_cls': True,
            'lang': 'ch',
            'text_detection_model_name': 'PP-OCRv5_server_det',
            'text_recognition_model_name': 'PP-OCRv5_server_rec',
            'confidence_threshold': 0.5
        },
        "GPU模式 + 官方参数": {
            'use_cpu': False,
            'gpu_id': 0,
            'use_angle_cls': True,
            'lang': 'ch',
            'text_detection_model_name': 'PP-OCRv5_server_det',
            'text_recognition_model_name': 'PP-OCRv5_server_rec',
            'det_db_thresh': 0.3,  # 官方默认值
            'det_db_box_thresh': 0.6,  # 官方默认值
            'rec_batch_num': 6,
            'confidence_threshold': 0.5
        }
    }
    
    results = {}
    
    for config_name, config in configs.items():
        print(f"\n📋 测试配置: {config_name}")
        print("-" * 40)
        
        try:
            # 创建OCR运行器
            ocr_runner = OCRSubprocessRunner()
            
            # 记录开始时间
            start_time = time.time()
            
            # 执行OCR
            result = ocr_runner.process_with_config(str(test_image), config)
            
            # 记录结束时间
            end_time = time.time()
            processing_time = end_time - start_time
            
            if result['success']:
                # 处理结果数据结构
                data_blocks = result.get('results', result.get('data', []))
                text_blocks = len(data_blocks)
                total_text = ' '.join([block['text'] for block in data_blocks])

                print(f"✅ 成功")
                print(f"⏱️  处理时间: {processing_time:.2f}秒")
                print(f"📝 检测到文本块: {text_blocks}个")
                print(f"📄 文本内容预览: {total_text[:100]}...")

                # 显示详细结果
                for i, block in enumerate(data_blocks[:3]):  # 只显示前3个
                    print(f"   块{i+1}: {block['text']} (置信度: {block['confidence']:.3f})")

                results[config_name] = {
                    'success': True,
                    'processing_time': processing_time,
                    'text_blocks': text_blocks,
                    'total_text_length': len(total_text),
                    'avg_confidence': sum([block['confidence'] for block in data_blocks]) / len(data_blocks) if data_blocks else 0
                }
            else:
                print(f"❌ 失败: {result.get('error', '未知错误')}")
                results[config_name] = {
                    'success': False,
                    'error': result.get('error', '未知错误')
                }
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            results[config_name] = {
                'success': False,
                'error': str(e)
            }
    
    # 结果对比
    print("\n" + "=" * 60)
    print("📊 测试结果对比")
    print("=" * 60)
    
    for config_name, result in results.items():
        print(f"\n🔧 {config_name}:")
        if result['success']:
            print(f"   ✅ 成功")
            print(f"   ⏱️  处理时间: {result['processing_time']:.2f}秒")
            print(f"   📝 文本块数量: {result['text_blocks']}")
            print(f"   📊 平均置信度: {result['avg_confidence']:.3f}")
        else:
            print(f"   ❌ 失败: {result['error']}")
    
    # 性能对比
    cpu_result = results.get("CPU模式 + 官方参数")
    gpu_result = results.get("GPU模式 + 官方参数")
    
    if cpu_result and gpu_result and cpu_result['success'] and gpu_result['success']:
        speedup = cpu_result['processing_time'] / gpu_result['processing_time']
        print(f"\n🚀 GPU加速比: {speedup:.2f}x")
        
        # 检查结果一致性
        if cpu_result['text_blocks'] == gpu_result['text_blocks']:
            print("✅ CPU和GPU模式检测到相同数量的文本块")
        else:
            print(f"⚠️  CPU和GPU模式检测到不同数量的文本块: CPU={cpu_result['text_blocks']}, GPU={gpu_result['text_blocks']}")
    
    return results

if __name__ == "__main__":
    results = test_official_params()
    
    # 保存结果到文件
    results_file = project_root / "test_results_official_params.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细结果已保存到: {results_file}")
