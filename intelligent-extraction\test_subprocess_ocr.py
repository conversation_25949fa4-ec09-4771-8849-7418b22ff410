#!/usr/bin/env python3
"""
测试子进程OCR功能
"""
import sys
import os
from pathlib import Path

# 添加项目路径
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

def test_subprocess_ocr():
    """测试子进程OCR"""
    try:
        print("=== 测试子进程OCR功能 ===")
        
        # 导入子进程运行器
        from src.models.ocr_subprocess import OCRSubprocessRunner
        
        print("✅ 成功导入OCRSubprocessRunner")
        
        # 创建运行器
        runner = OCRSubprocessRunner(device_id=0)
        print("✅ 成功创建OCRSubprocessRunner实例")
        
        # 检查worker脚本是否存在
        print(f"Worker脚本路径: {runner.ocr_worker_script}")
        print(f"Worker脚本存在: {runner.ocr_worker_script.exists()}")
        
        # 创建测试图像（简单的文字图像）
        import numpy as np
        import cv2
        import tempfile
        
        # 创建一个简单的测试图像
        img = np.ones((200, 400, 3), dtype=np.uint8) * 255  # 白色背景
        cv2.putText(img, 'Test OCR', (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
        
        # 保存为临时文件
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            temp_path = f.name
            cv2.imwrite(temp_path, img)
        
        print(f"✅ 创建测试图像: {temp_path}")
        
        # 测试OCR识别
        print("开始OCR识别...")
        result = runner.recognize(
            image_path=temp_path,
            confidence_threshold=0.1,  # 降低阈值便于测试
            use_cpu=True
        )
        
        print(f"OCR结果: {result}")
        
        if result.get('success'):
            print("✅ 子进程OCR测试成功!")
            print(f"识别到 {len(result.get('results', []))} 个文本块")
            for i, item in enumerate(result.get('results', [])):
                print(f"  文本块 {i+1}: '{item['text']}' (置信度: {item['confidence']:.2f})")
        else:
            print(f"❌ 子进程OCR测试失败: {result.get('error')}")
        
        # 清理临时文件
        try:
            os.unlink(temp_path)
        except:
            pass
            
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ppocrv3_model():
    """测试PPOCRv3Model"""
    try:
        print("\n=== 测试PPOCRv3Model ===")
        
        from src.models.ocr_models import PPOCRv3Model
        
        print("✅ 成功导入PPOCRv3Model")
        
        # 创建模型实例
        model = PPOCRv3Model(device_id=0)
        print("✅ 成功创建PPOCRv3Model实例")
        
        # 创建测试图像
        import numpy as np
        import cv2
        import tempfile
        
        img = np.ones((200, 400, 3), dtype=np.uint8) * 255
        cv2.putText(img, 'PPOCRv3 Test', (30, 100), cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 2)
        
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            temp_path = f.name
            cv2.imwrite(temp_path, img)
        
        print(f"✅ 创建测试图像: {temp_path}")
        
        # 测试识别
        print("开始PPOCRv3识别...")
        result = model.recognize(temp_path, confidence_threshold=0.1)
        
        print(f"识别结果类型: {type(result)}")
        print(f"识别到文本块数量: {len(result.text_blocks)}")
        print(f"全文: {result.full_text}")
        
        # 清理临时文件
        try:
            os.unlink(temp_path)
        except:
            pass
            
        print("✅ PPOCRv3Model测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ PPOCRv3Model测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试子进程OCR功能...")
    
    # 测试子进程运行器
    subprocess_ok = test_subprocess_ocr()
    
    # 测试PPOCRv3Model
    if subprocess_ok:
        model_ok = test_ppocrv3_model()
    else:
        print("⚠️  跳过PPOCRv3Model测试，因为子进程测试失败")
        model_ok = False
    
    print(f"\n=== 测试总结 ===")
    print(f"子进程OCR: {'✅ 通过' if subprocess_ok else '❌ 失败'}")
    print(f"PPOCRv3Model: {'✅ 通过' if model_ok else '❌ 失败'}")
    
    if subprocess_ok and model_ok:
        print("🎉 所有测试通过！子进程OCR功能正常")
    else:
        print("⚠️  存在问题，需要进一步调试")
