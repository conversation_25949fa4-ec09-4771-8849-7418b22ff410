#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PaddleOCR 3.0.2的OCRResult格式
"""
import sys
import time
from pathlib import Path

def test_ocr_result_format(image_path: str):
    """测试OCRResult格式和正确的访问方式"""
    print("🧪 测试PaddleOCR 3.0.2的OCRResult格式")
    print("=" * 60)
    
    image_file = Path(image_path)
    if not image_file.exists():
        print(f"❌ 图像文件不存在: {image_path}")
        return False
    
    try:
        print("📋 步骤1: 导入PaddleOCR")
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR导入成功")
        
        print("\n📋 步骤2: 创建OCR实例")
        ocr = PaddleOCR(
            use_textline_orientation=True,  # 使用新参数名
            lang='ch'
        )
        print("✅ OCR实例创建成功")
        
        print("\n📋 步骤3: 执行OCR识别")
        result = ocr.predict(str(image_file))
        print(f"✅ OCR识别完成")
        
        print(f"\n📊 结果分析:")
        print(f"   结果类型: {type(result)}")
        print(f"   结果长度: {len(result) if hasattr(result, '__len__') else 'N/A'}")
        
        if result and len(result) > 0:
            ocr_result = result[0]  # 获取第一个结果
            print(f"   OCR结果类型: {type(ocr_result)}")
            
            # 检查OCRResult对象的属性
            if hasattr(ocr_result, '__dict__'):
                print(f"\n📋 OCRResult对象属性:")
                for attr_name in dir(ocr_result):
                    if not attr_name.startswith('_'):
                        try:
                            attr_value = getattr(ocr_result, attr_name)
                            if not callable(attr_value):
                                print(f"   {attr_name}: {type(attr_value)}")
                        except:
                            print(f"   {attr_name}: <无法访问>")
            
            # 尝试访问文本检测和识别结果
            print(f"\n📋 尝试访问文本结果:")
            
            # 检查常见的属性名
            text_attrs = ['rec_texts', 'texts', 'text', 'results']
            score_attrs = ['rec_scores', 'scores', 'score', 'confidences']
            bbox_attrs = ['dt_polys', 'boxes', 'bbox', 'polygons']
            
            for attr in text_attrs:
                if hasattr(ocr_result, attr):
                    value = getattr(ocr_result, attr)
                    print(f"   {attr}: {value} (类型: {type(value)}, 长度: {len(value) if hasattr(value, '__len__') else 'N/A'})")
            
            for attr in score_attrs:
                if hasattr(ocr_result, attr):
                    value = getattr(ocr_result, attr)
                    print(f"   {attr}: {value} (类型: {type(value)}, 长度: {len(value) if hasattr(value, '__len__') else 'N/A'})")
            
            for attr in bbox_attrs:
                if hasattr(ocr_result, attr):
                    value = getattr(ocr_result, attr)
                    print(f"   {attr}: {type(value)}, 长度: {len(value) if hasattr(value, '__len__') else 'N/A'}")
            
            # 尝试调用可能的方法
            print(f"\n📋 尝试调用可能的方法:")
            methods = ['get_text', 'get_texts', 'to_dict', 'to_list']
            for method in methods:
                if hasattr(ocr_result, method):
                    try:
                        result_method = getattr(ocr_result, method)
                        if callable(result_method):
                            method_result = result_method()
                            print(f"   {method}(): {method_result} (类型: {type(method_result)})")
                    except Exception as e:
                        print(f"   {method}(): 调用失败 - {e}")
            
            # 检查是否有文本被检测到
            rec_texts = getattr(ocr_result, 'rec_texts', [])
            rec_scores = getattr(ocr_result, 'rec_scores', [])
            
            if rec_texts and len(rec_texts) > 0:
                print(f"\n✅ 检测到 {len(rec_texts)} 个文本:")
                for i, (text, score) in enumerate(zip(rec_texts, rec_scores), 1):
                    print(f"   {i}. '{text}' (置信度: {score:.3f})")
                return True
            else:
                print(f"\n⚠️ 没有检测到文本内容")
                print(f"   可能原因:")
                print(f"   1. 图像质量问题")
                print(f"   2. 文本太小或不清晰")
                print(f"   3. OCR参数需要调整")
                return False
        
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python test_ocr_result_format.py <图像路径>")
        print("示例: python test_ocr_result_format.py test_processed_image.jpg")
        return
    
    image_path = sys.argv[1]
    success = test_ocr_result_format(image_path)
    
    print(f"\n🎯 测试结果:")
    if success:
        print("✅ OCRResult格式解析成功，检测到文本!")
    else:
        print("⚠️ OCRResult格式解析完成，但未检测到文本")
        print("💡 这可能是正常的，取决于图像内容和OCR参数")


if __name__ == "__main__":
    main()
