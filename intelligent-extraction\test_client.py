#!/usr/bin/env python3
"""
测试客户端 - 用于快速测试API接口
位置: intelligent-extraction/test_client.py
"""
import os
import sys
import json
import time
import requests
from pathlib import Path

def test_health():
    """测试健康检查"""
    print("=== 健康检查 ===")
    try:
        response = requests.get("http://localhost:8080/health", timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def test_status():
    """测试系统状态"""
    print("\n=== 系统状态 ===")
    try:
        response = requests.get("http://localhost:8080/status", timeout=30)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("系统状态:")
            print(f"  状态: {data.get('status')}")
            print(f"  GPU信息: {data.get('gpu_info')}")
            print(f"  模型状态: {data.get('model_status')}")
            return True
        else:
            print(f"❌ 状态获取失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 系统状态获取失败: {e}")
        return False

def test_archive_extraction(image_path: str):
    """测试档案要素提取（单张图片）"""
    print(f"\n=== 档案要素提取: {image_path} ===")

    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return False

    try:
        with open(image_path, 'rb') as f:
            files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
            data = {'sync': 'true'}

            print("正在上传图片并提取档案要素...")
            start_time = time.time()

            response = requests.post(
                "http://localhost:8080/extract/archive",
                files=files,
                data=data,
                timeout=120
            )

            response_time = time.time() - start_time
            print(f"响应时间: {response_time:.2f}s")
            print(f"状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                print(f"提取成功: {result.get('success')}")

                if result.get('success'):
                    print("提取结果:")
                    extracted_fields = result.get('results', {})
                    for field, value in extracted_fields.items():
                        print(f"  {field}: {value}")

                    processing_time = result.get('processing_time')
                    if processing_time:
                        print(f"处理时间: {processing_time:.2f}s")

                    return True
                else:
                    print(f"❌ 提取失败: {result.get('error')}")
                    return False
            else:
                print(f"❌ 请求失败: {response.text}")
                return False

    except Exception as e:
        print(f"❌ 档案要素提取失败: {e}")
        return False

def test_archive_folder_extraction(folder_path: str):
    """测试档案文件夹要素提取（多页档案）"""
    print(f"\n=== 档案文件夹要素提取: {folder_path} ===")

    if not os.path.exists(folder_path):
        print(f"❌ 文件夹不存在: {folder_path}")
        return False

    if not os.path.isdir(folder_path):
        print(f"❌ 路径不是文件夹: {folder_path}")
        return False

    # 查找图片文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    image_files = []

    for ext in image_extensions:
        image_files.extend(Path(folder_path).glob(f"*{ext}"))
        image_files.extend(Path(folder_path).glob(f"*{ext.upper()}"))

    if not image_files:
        print(f"❌ 文件夹中未找到图片文件: {folder_path}")
        return False

    # 按文件名排序
    image_files.sort(key=lambda x: x.name)
    print(f"找到 {len(image_files)} 个图片文件:")
    for img in image_files:
        print(f"  - {img.name}")

    try:
        # 准备文件上传
        files = []
        for img_path in image_files:
            with open(img_path, 'rb') as f:
                files.append(('files', (img_path.name, f.read(), 'image/jpeg')))

        data = {'sync': 'true'}

        print("正在上传档案文件夹并提取要素...")
        start_time = time.time()

        response = requests.post(
            "http://localhost:8080/extract/archive_folder",
            files=files,
            data=data,
            timeout=300  # 多页档案需要更长时间
        )

        response_time = time.time() - start_time
        print(f"响应时间: {response_time:.2f}s")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"提取成功: {result.get('success')}")

            if result.get('success'):
                print("提取结果:")
                extracted_fields = result.get('results', {})
                for field, value in extracted_fields.items():
                    print(f"  {field}: {value}")

                processing_time = result.get('processing_time')
                if processing_time:
                    print(f"处理时间: {processing_time:.2f}s")

                page_count = result.get('page_count')
                if page_count:
                    print(f"处理页数: {page_count}")

                return True
            else:
                print(f"❌ 提取失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.text}")
            return False

    except Exception as e:
        print(f"❌ 档案文件夹要素提取失败: {e}")
        return False

def test_custom_extraction(image_path: str, key_list: str):
    """测试自定义字段提取"""
    print(f"\n=== 自定义字段提取: {image_path} ===")
    print(f"提取字段: {key_list}")
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return False
    
    try:
        with open(image_path, 'rb') as f:
            files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
            data = {
                'key_list': key_list,
                'sync': 'true'
            }
            
            print("正在上传图片并提取自定义字段...")
            start_time = time.time()
            
            response = requests.post(
                "http://localhost:8080/extract/upload",
                files=files,
                data=data,
                timeout=120
            )
            
            response_time = time.time() - start_time
            print(f"响应时间: {response_time:.2f}s")
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"提取成功: {result.get('success')}")
                
                if result.get('success'):
                    print("提取结果:")
                    extracted_fields = result.get('results', {})
                    for field, value in extracted_fields.items():
                        print(f"  {field}: {value}")
                    
                    return True
                else:
                    print(f"❌ 提取失败: {result.get('error')}")
                    return False
            else:
                print(f"❌ 请求失败: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 自定义字段提取失败: {e}")
        return False

def interactive_test():
    """交互式测试"""
    print("🧪 智能信息提取服务 - 交互式测试客户端")
    print("=" * 50)
    
    # 检查服务状态
    if not test_health():
        print("❌ 服务未启动或不可用")
        print("请先启动服务: python start_service.py")
        return
    
    # 获取系统状态
    test_status()
    
    while True:
        print("\n" + "=" * 50)
        print("请选择测试选项:")
        print("1. 档案要素提取测试（单张图片）")
        print("2. 档案文件夹要素提取测试（多页档案）")
        print("3. 自定义字段提取测试")
        print("4. 重新检查系统状态")
        print("5. 退出")

        choice = input("\n请输入选项 (1-5): ").strip()

        if choice == '1':
            image_path = input("请输入图片路径: ").strip()
            if image_path:
                test_archive_extraction(image_path)
            else:
                print("❌ 图片路径不能为空")

        elif choice == '2':
            folder_path = input("请输入档案文件夹路径: ").strip()
            if folder_path:
                test_archive_folder_extraction(folder_path)
            else:
                print("❌ 文件夹路径不能为空")

        elif choice == '3':
            image_path = input("请输入图片路径: ").strip()
            key_list = input("请输入要提取的字段 (用逗号分隔): ").strip()
            if image_path and key_list:
                test_custom_extraction(image_path, key_list)
            else:
                print("❌ 图片路径和字段列表不能为空")

        elif choice == '4':
            test_status()

        elif choice == '5':
            print("👋 退出测试客户端")
            break

        else:
            print("❌ 无效选项，请重新选择")

def batch_test():
    """批量测试"""
    print("🧪 批量测试模式")
    print("=" * 50)

    # 检查服务状态
    if not test_health():
        print("❌ 服务未启动或不可用")
        return

    # 测试目录
    test_dir = Path("test_images")
    if not test_dir.exists():
        print(f"❌ 测试目录不存在: {test_dir}")
        print("请创建test_images目录并放入测试图片或档案文件夹")
        return

    # 查找图片文件和档案文件夹
    image_files = []
    archive_folders = []

    for item in test_dir.iterdir():
        if item.is_file():
            # 单张图片文件
            if item.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                image_files.append(item)
        elif item.is_dir():
            # 档案文件夹（包含多张图片）
            folder_images = []
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                folder_images.extend(item.glob(f"*{ext}"))
                folder_images.extend(item.glob(f"*{ext.upper()}"))

            if folder_images:
                archive_folders.append(item)

    total_tests = len(image_files) + len(archive_folders)
    if total_tests == 0:
        print(f"❌ 在 {test_dir} 中未找到测试文件或档案文件夹")
        return

    print(f"找到测试项目:")
    print(f"  - 单张图片: {len(image_files)} 个")
    print(f"  - 档案文件夹: {len(archive_folders)} 个")
    print(f"  - 总计: {total_tests} 个")

    # 批量测试
    success_count = 0

    # 测试单张图片
    for img_path in image_files:
        print(f"\n{'='*20} 测试图片 {img_path.name} {'='*20}")
        if test_archive_extraction(str(img_path)):
            success_count += 1

    # 测试档案文件夹
    for folder_path in archive_folders:
        print(f"\n{'='*20} 测试档案文件夹 {folder_path.name} {'='*20}")
        if test_archive_folder_extraction(str(folder_path)):
            success_count += 1

    # 测试结果
    print(f"\n{'='*50}")
    print(f"📊 批量测试结果:")
    print(f"总计: {total_tests} 个测试项目")
    print(f"成功: {success_count} 个")
    print(f"失败: {total_tests - success_count} 个")
    print(f"成功率: {success_count/total_tests*100:.1f}%")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "batch":
            batch_test()
        elif sys.argv[1] == "health":
            test_health()
        elif sys.argv[1] == "status":
            test_status()
        else:
            print("用法:")
            print("  python test_client.py          # 交互式测试")
            print("  python test_client.py batch    # 批量测试")
            print("  python test_client.py health   # 健康检查")
            print("  python test_client.py status   # 系统状态")
    else:
        interactive_test()

if __name__ == "__main__":
    main()
