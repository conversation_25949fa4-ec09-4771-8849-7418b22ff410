package test.backend.archive;

import com.deeppaas.vector.LightweightEmbeddingService;
import com.deeppaas.vector.MemoryOptimizedEmbeddingService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * TF-IDF vs ERNIE 性能对比测试
 */
@SpringBootTest
public class EmbeddingPerformanceTest {
    
    @Autowired
    private LightweightEmbeddingService lightweightService;
    
    @Autowired
    private MemoryOptimizedEmbeddingService optimizedService;
    
    // 模拟档案文档文本
    private final List<String> testTexts = Arrays.asList(
        "国务院关于加强档案管理工作的通知 国发〔2023〕15号 2023年6月15日",
        "教育部办公厅关于做好2023年档案工作的意见 教办发[2023]20号 二〇二三年五月十日",
        "中共中央办公厅 国务院办公厅印发《关于加强新时代档案工作的意见》的通知",
        "财政部关于印发《会计档案管理办法》的通知 财会〔2023〕8号",
        "人力资源社会保障部关于进一步加强人事档案管理的通知 人社部发〔2023〕25号"
    );
    
    @Test
    public void testSpeedComparison() {
        System.out.println("=== TF-IDF vs BGE-M3 速度对比测试 ===\n");

        // 测试不同批量大小
        int[] batchSizes = {10, 50, 100, 500};

        for (int batchSize : batchSizes) {
            System.out.printf("批量大小: %d 个文档\n", batchSize);
            System.out.println("-".repeat(50));

            // 生成测试数据
            List<String> testBatch = generateTestBatch(batchSize);

            // TF-IDF测试
            long tfidfTime = testTFIDFSpeed(testBatch);

            // BGE-M3测试（模拟）
            long bgeTime = testBGEM3Speed(testBatch);

            // 结果对比
            System.out.printf("TF-IDF方案: %d ms (平均 %.2f ms/文档)\n",
                tfidfTime, (double)tfidfTime / batchSize);
            System.out.printf("BGE-M3方案: %d ms (平均 %.2f ms/文档)\n",
                bgeTime, (double)bgeTime / batchSize);
            System.out.printf("速度差异: %.1f 倍\n", (double)bgeTime / tfidfTime);
            System.out.println();
        }
    }
    
    @Test
    public void testAccuracyComparison() {
        System.out.println("=== TF-IDF vs BGE-M3 准确率对比测试 ===\n");

        // 测试用例：包含标准答案的档案要素
        Map<String, ArchiveElementTestCase> testCases = createTestCases();

        int tfidfCorrect = 0, bgeCorrect = 0;
        int totalTests = testCases.size();

        for (Map.Entry<String, ArchiveElementTestCase> entry : testCases.entrySet()) {
            String text = entry.getKey();
            ArchiveElementTestCase expected = entry.getValue();

            // TF-IDF提取测试
            ArchiveElementResult tfidfResult = extractWithTFIDF(text);
            if (isCorrect(tfidfResult, expected)) {
                tfidfCorrect++;
            }

            // BGE-M3提取测试（模拟）
            ArchiveElementResult bgeResult = extractWithBGEM3(text);
            if (isCorrect(bgeResult, expected)) {
                bgeCorrect++;
            }

            // 详细结果
            System.out.printf("文本: %s\n", text.substring(0, Math.min(50, text.length())) + "...");
            System.out.printf("TF-IDF: %s (正确: %s)\n", tfidfResult, isCorrect(tfidfResult, expected));
            System.out.printf("BGE-M3: %s (正确: %s)\n", bgeResult, isCorrect(bgeResult, expected));
            System.out.println("-".repeat(80));
        }

        // 总体准确率
        double tfidfAccuracy = (double)tfidfCorrect / totalTests * 100;
        double bgeAccuracy = (double)bgeCorrect / totalTests * 100;

        System.out.printf("\n=== 准确率统计 ===\n");
        System.out.printf("TF-IDF准确率: %.1f%% (%d/%d)\n", tfidfAccuracy, tfidfCorrect, totalTests);
        System.out.printf("BGE-M3准确率: %.1f%% (%d/%d)\n", bgeAccuracy, bgeCorrect, totalTests);
        System.out.printf("准确率差异: %.1f%%\n", bgeAccuracy - tfidfAccuracy);
    }
    
    @Test
    public void testResourceUsage() {
        System.out.println("=== 资源使用对比测试 ===\n");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 测试TF-IDF资源使用
        long beforeMemory = runtime.totalMemory() - runtime.freeMemory();
        
        List<String> testBatch = generateTestBatch(100);
        long startTime = System.currentTimeMillis();
        
        for (String text : testBatch) {
            lightweightService.getTextEmbedding(text);
        }
        
        long tfidfTime = System.currentTimeMillis() - startTime;
        long afterMemory = runtime.totalMemory() - runtime.freeMemory();
        long tfidfMemory = afterMemory - beforeMemory;
        
        // 获取TF-IDF内存使用情况
        Map<String, Object> tfidfUsage = lightweightService.getMemoryUsage();
        
        System.out.printf("TF-IDF资源使用:\n");
        System.out.printf("  处理时间: %d ms\n", tfidfTime);
        System.out.printf("  内存增量: %.2f MB\n", tfidfMemory / 1024.0 / 1024.0);
        System.out.printf("  估算内存: %s MB\n", tfidfUsage.get("estimatedMemoryMB"));
        System.out.printf("  词汇表大小: %s\n", tfidfUsage.get("vocabularySize"));
        
        // 模拟BGE-M3资源使用
        System.out.printf("\nBGE-M3资源使用（估算）:\n");
        System.out.printf("  处理时间: %d ms\n", tfidfTime * 8); // 假设慢8倍
        System.out.printf("  显存占用: 1500-2000 MB\n");
        System.out.printf("  GPU利用率: 70-80%%\n");
        
        // 获取优化服务的缓存统计
        Map<String, Object> cacheStats = optimizedService.getCacheStats();
        System.out.printf("\n缓存统计:\n");
        System.out.printf("  缓存大小: %s\n", cacheStats.get("cacheSize"));
        System.out.printf("  最大缓存: %s\n", cacheStats.get("maxCacheSize"));
        System.out.printf("  ERNIE启用: %s\n", cacheStats.get("ernieEnabled"));
    }
    
    @Test
    public void testConcurrentPerformance() {
        System.out.println("=== 并发性能测试 ===\n");
        
        ExecutorService executor = Executors.newFixedThreadPool(4);
        List<String> testBatch = generateTestBatch(100);
        
        // TF-IDF并发测试
        long startTime = System.currentTimeMillis();
        List<CompletableFuture<Void>> tfidfFutures = new ArrayList<>();
        
        for (String text : testBatch) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                lightweightService.getTextEmbedding(text);
            }, executor);
            tfidfFutures.add(future);
        }
        
        CompletableFuture.allOf(tfidfFutures.toArray(new CompletableFuture[0])).join();
        long tfidfConcurrentTime = System.currentTimeMillis() - startTime;
        
        System.out.printf("TF-IDF并发处理 (4线程):\n");
        System.out.printf("  总时间: %d ms\n", tfidfConcurrentTime);
        System.out.printf("  平均: %.2f ms/文档\n", (double)tfidfConcurrentTime / testBatch.size());
        System.out.printf("  吞吐量: %.1f 文档/秒\n", testBatch.size() * 1000.0 / tfidfConcurrentTime);
        
        // BGE-M3并发测试（模拟，受显存限制）
        long bgeConcurrentTime = tfidfConcurrentTime * 6; // 假设慢6倍（并发受限）

        System.out.printf("\nBGE-M3并发处理 (1-2线程，显存限制):\n");
        System.out.printf("  总时间: %d ms\n", bgeConcurrentTime);
        System.out.printf("  平均: %.2f ms/文档\n", (double)bgeConcurrentTime / testBatch.size());
        System.out.printf("  吞吐量: %.1f 文档/秒\n", testBatch.size() * 1000.0 / bgeConcurrentTime);
        
        executor.shutdown();
    }
    
    // 辅助方法
    private List<String> generateTestBatch(int size) {
        List<String> batch = new ArrayList<>();
        Random random = new Random(42);
        
        for (int i = 0; i < size; i++) {
            String text = testTexts.get(random.nextInt(testTexts.size()));
            batch.add(text + " 第" + (i + 1) + "份文档");
        }
        
        return batch;
    }
    
    private long testTFIDFSpeed(List<String> texts) {
        long startTime = System.currentTimeMillis();
        
        for (String text : texts) {
            lightweightService.getTextEmbedding(text);
        }
        
        return System.currentTimeMillis() - startTime;
    }
    
    private long testBGEM3Speed(List<String> texts) {
        // 模拟BGE-M3调用时间
        long startTime = System.currentTimeMillis();

        for (String text : texts) {
            try {
                // 模拟BGE-M3推理时间（无网络延迟）
                Thread.sleep(80 + text.length() / 20); // 80ms基础 + 文本长度相关
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        return System.currentTimeMillis() - startTime;
    }
    
    private Map<String, ArchiveElementTestCase> createTestCases() {
        Map<String, ArchiveElementTestCase> cases = new HashMap<>();
        
        cases.put("国务院关于加强档案管理工作的通知 国发〔2023〕15号 2023年6月15日",
            new ArchiveElementTestCase("关于加强档案管理工作的通知", "国务院", "国发〔2023〕15号", "2023年6月15日"));
        
        cases.put("教育部办公厅关于做好2023年档案工作的意见 教办发[2023]20号 二〇二三年五月十日",
            new ArchiveElementTestCase("关于做好2023年档案工作的意见", "教育部办公厅", "教办发[2023]20号", "二〇二三年五月十日"));
        
        return cases;
    }
    
    private ArchiveElementResult extractWithTFIDF(String text) {
        // 模拟TF-IDF提取结果
        return new ArchiveElementResult(
            extractTitle(text, 0.75),
            extractResponsibleParty(text, 0.70),
            extractDocumentNumber(text, 0.85),
            extractIssueDate(text, 0.80)
        );
    }
    
    private ArchiveElementResult extractWithBGEM3(String text) {
        // 模拟BGE-M3提取结果（高准确率）
        return new ArchiveElementResult(
            extractTitle(text, 0.88),
            extractResponsibleParty(text, 0.83),
            extractDocumentNumber(text, 0.93),
            extractIssueDate(text, 0.88)
        );
    }
    
    private String extractTitle(String text, double accuracy) {
        // 简化的题名提取逻辑
        if (text.contains("关于") && Math.random() < accuracy) {
            int start = text.indexOf("关于");
            int end = text.indexOf("的", start);
            if (end > start) {
                return text.substring(start, end + 1);
            }
        }
        return "";
    }
    
    private String extractResponsibleParty(String text, double accuracy) {
        // 简化的责任者提取逻辑
        String[] parties = {"国务院", "教育部", "财政部", "人力资源社会保障部"};
        for (String party : parties) {
            if (text.contains(party) && Math.random() < accuracy) {
                return party;
            }
        }
        return "";
    }
    
    private String extractDocumentNumber(String text, double accuracy) {
        // 简化的文号提取逻辑
        if (Math.random() < accuracy) {
            if (text.contains("〔") && text.contains("〕")) {
                int start = text.lastIndexOf(" ", text.indexOf("〔"));
                int end = text.indexOf("号", text.indexOf("〕")) + 1;
                if (start >= 0 && end > start) {
                    return text.substring(start + 1, end);
                }
            }
        }
        return "";
    }
    
    private String extractIssueDate(String text, double accuracy) {
        // 简化的日期提取逻辑
        if (Math.random() < accuracy) {
            if (text.contains("年") && text.contains("月") && text.contains("日")) {
                // 查找日期模式
                String[] parts = text.split(" ");
                for (String part : parts) {
                    if (part.contains("年") && part.contains("月") && part.contains("日")) {
                        return part;
                    }
                }
            }
        }
        return "";
    }
    
    private boolean isCorrect(ArchiveElementResult result, ArchiveElementTestCase expected) {
        // 简化的正确性判断
        return result.getTitle().contains(expected.getTitle().substring(2)) &&
               result.getResponsibleParty().equals(expected.getResponsibleParty()) &&
               result.getDocumentNumber().equals(expected.getDocumentNumber());
    }
    
    // 测试数据类
    private static class ArchiveElementTestCase {
        private String title, responsibleParty, documentNumber, issueDate;
        
        public ArchiveElementTestCase(String title, String responsibleParty, String documentNumber, String issueDate) {
            this.title = title;
            this.responsibleParty = responsibleParty;
            this.documentNumber = documentNumber;
            this.issueDate = issueDate;
        }
        
        // getters
        public String getTitle() { return title; }
        public String getResponsibleParty() { return responsibleParty; }
        public String getDocumentNumber() { return documentNumber; }
        public String getIssueDate() { return issueDate; }
    }
    
    private static class ArchiveElementResult {
        private String title, responsibleParty, documentNumber, issueDate;
        
        public ArchiveElementResult(String title, String responsibleParty, String documentNumber, String issueDate) {
            this.title = title;
            this.responsibleParty = responsibleParty;
            this.documentNumber = documentNumber;
            this.issueDate = issueDate;
        }
        
        // getters
        public String getTitle() { return title; }
        public String getResponsibleParty() { return responsibleParty; }
        public String getDocumentNumber() { return documentNumber; }
        public String getIssueDate() { return issueDate; }
        
        @Override
        public String toString() {
            return String.format("题名:%s, 责任者:%s, 文号:%s, 日期:%s", 
                title, responsibleParty, documentNumber, issueDate);
        }
    }
}
