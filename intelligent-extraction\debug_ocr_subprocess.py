#!/usr/bin/env python3
"""
调试OCR子进程问题
"""
import os
import sys
import json
import tempfile
import subprocess
from pathlib import Path

def test_ocr_subprocess():
    """测试OCR子进程"""
    print("=== OCR子进程调试测试 ===")
    
    # 1. 检查worker脚本是否存在
    script_dir = Path(__file__).parent / "src" / "models"
    worker_script = script_dir / "ocr_worker.py"
    
    print(f"Worker脚本路径: {worker_script}")
    print(f"Worker脚本存在: {worker_script.exists()}")
    
    if not worker_script.exists():
        print("❌ Worker脚本不存在，尝试创建...")
        from src.models.ocr_subprocess import OCRSubprocessRunner
        runner = OCRSubprocessRunner()
        print(f"✅ Worker脚本已创建: {worker_script.exists()}")
    
    # 2. 创建测试图像（简单的文本图像）
    test_image_path = Path(__file__).parent / "test_image.png"
    if not test_image_path.exists():
        print("创建测试图像...")
        import numpy as np
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建简单的测试图像
        img = Image.new('RGB', (400, 100), color='white')
        draw = ImageDraw.Draw(img)
        
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            # 使用默认字体
            font = ImageFont.load_default()
        
        draw.text((50, 30), "测试文本 Test Text", fill='black', font=font)
        img.save(test_image_path)
        print(f"✅ 测试图像已创建: {test_image_path}")
    
    # 3. 创建配置文件
    config = {
        'image_path': str(test_image_path),
        'use_angle_cls': True,
        'lang': 'ch',
        'use_cpu': True,  # 先用CPU测试
        'gpu_id': 0,
        'confidence_threshold': 0.5
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', 
                                   delete=False, encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False)
        config_file = f.name
    
    print(f"配置文件: {config_file}")
    
    # 4. 直接测试worker脚本
    print("\n=== 直接测试Worker脚本 ===")
    cmd = [sys.executable, str(worker_script), config_file]
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=60
        )
        
        print(f"返回码: {result.returncode}")
        print(f"标准输出: {result.stdout}")
        print(f"标准错误: {result.stderr}")
        
        if result.returncode == 0:
            try:
                ocr_result = json.loads(result.stdout)
                print(f"✅ OCR结果解析成功: {ocr_result}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
        else:
            print(f"❌ 子进程执行失败")
            
    except subprocess.TimeoutExpired:
        print("❌ 子进程超时")
    except Exception as e:
        print(f"❌ 子进程运行异常: {e}")
    finally:
        # 清理临时文件
        try:
            os.unlink(config_file)
        except:
            pass
    
    # 5. 测试OCRSubprocessRunner
    print("\n=== 测试OCRSubprocessRunner ===")
    try:
        from src.models.ocr_subprocess import OCRSubprocessRunner
        runner = OCRSubprocessRunner()
        result = runner.recognize(test_image_path, use_cpu=True)
        print(f"OCRSubprocessRunner结果: {result}")
    except Exception as e:
        print(f"❌ OCRSubprocessRunner测试失败: {e}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        # 使用命令行指定的图像
        test_image_path = sys.argv[1]
        print(f"🎯 使用指定图像进行测试: {test_image_path}")
        test_ocr_subprocess()
    else:
        # 使用默认的小测试图像
        print("🎯 使用默认小测试图像")
        test_ocr_subprocess()
