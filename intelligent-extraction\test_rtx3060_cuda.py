#!/usr/bin/env python3
"""
RTX 3060 CUDA功能测试
测试PaddleOCR在RTX 3060上的CUDA性能
"""
import os
import sys
import time
import json

# RTX 3060优化配置
os.environ['CUDA_VISIBLE_DEVICES'] = '0'
os.environ['FLAGS_allocator_strategy'] = 'auto_growth'
os.environ['FLAGS_fraction_of_gpu_memory_to_use'] = '0.7'
os.environ['FLAGS_eager_delete_tensor_gb'] = '0.0'
os.environ['FLAGS_fast_eager_deletion_mode'] = 'true'
os.environ['PADDLE_DISABLE_STATIC'] = '1'
os.environ['FLAGS_use_cuda'] = '1'
os.environ['FLAGS_cudnn_deterministic'] = '0'

def check_cuda_environment():
    """检查CUDA环境"""
    print("🔍 检查CUDA环境...")
    
    try:
        import paddle
        print(f"✅ PaddlePaddle版本: {paddle.__version__}")
        
        # 检查CUDA可用性
        if paddle.device.is_compiled_with_cuda():
            print("✅ PaddlePaddle支持CUDA")
        else:
            print("❌ PaddlePaddle不支持CUDA")
            return False
        
        # 检查GPU设备
        gpu_count = paddle.device.cuda.device_count()
        print(f"📊 检测到GPU数量: {gpu_count}")
        
        if gpu_count > 0:
            for i in range(gpu_count):
                gpu_name = paddle.device.cuda.get_device_name(i)
                gpu_memory = paddle.device.cuda.get_device_properties(i).total_memory / (1024**3)
                print(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
        
        return gpu_count > 0
        
    except Exception as e:
        print(f"❌ CUDA环境检查失败: {e}")
        return False

def test_paddleocr_cpu():
    """测试PaddleOCR CPU模式"""
    print("\n🔍 测试PaddleOCR CPU模式...")
    
    try:
        from paddleocr import PaddleOCR
        
        start_time = time.time()
        ocr = PaddleOCR(
            use_textline_orientation=True,
            lang='ch',
            device='cpu'
            # PaddleOCR 3.0.2移除了show_log参数
        )
        init_time = time.time() - start_time
        print(f"✅ CPU模式初始化成功，耗时: {init_time:.2f}s")
        
        return True, ocr, init_time
        
    except Exception as e:
        print(f"❌ CPU模式初始化失败: {e}")
        return False, None, 0

def test_paddleocr_gpu():
    """测试PaddleOCR GPU模式"""
    print("\n🔍 测试PaddleOCR GPU模式...")
    
    try:
        from paddleocr import PaddleOCR
        
        start_time = time.time()
        ocr = PaddleOCR(
            use_textline_orientation=True,
            lang='ch',
            device='gpu:0'  # RTX 3060
            # PaddleOCR 3.0.2移除了show_log参数
        )
        init_time = time.time() - start_time
        print(f"✅ GPU模式初始化成功，耗时: {init_time:.2f}s")
        
        return True, ocr, init_time
        
    except Exception as e:
        print(f"❌ GPU模式初始化失败: {e}")
        return False, None, 0

def test_ocr_performance(ocr, mode_name, test_image="uploads/125ef0ca-61e6-4cf5-9845-e8b9f3bd1358.jpg"):
    """测试OCR性能"""
    print(f"\n🔍 测试{mode_name}OCR性能...")
    
    if not os.path.exists(test_image):
        print(f"❌ 测试图像不存在: {test_image}")
        return False, 0, 0
    
    try:
        # 预热运行
        print("🔥 预热运行...")
        ocr.predict(test_image)
        
        # 正式测试
        print("⚡ 正式测试...")
        start_time = time.time()
        results = ocr.predict(test_image)
        process_time = time.time() - start_time
        
        # 分析结果
        text_blocks = 0
        if hasattr(results, '__len__'):
            text_blocks = len(results)
        elif hasattr(results, 'get'):
            # 字典格式
            rec_texts = results.get('rec_texts', [])
            text_blocks = len(rec_texts) if rec_texts else 0
        
        print(f"✅ {mode_name}OCR处理成功")
        print(f"  📊 处理时间: {process_time:.2f}s")
        print(f"  📝 检测文本块: {text_blocks}个")
        
        return True, process_time, text_blocks
        
    except Exception as e:
        print(f"❌ {mode_name}OCR处理失败: {e}")
        return False, 0, 0

def test_seal_detection_gpu():
    """测试印章检测GPU模式"""
    print("\n🔍 测试印章检测GPU模式...")
    
    try:
        from paddleocr import SealTextDetection
        
        start_time = time.time()
        seal_detector = SealTextDetection(
            model_name="PP-OCRv4_mobile_seal_det",
            device='gpu:0'  # RTX 3060
        )
        init_time = time.time() - start_time
        print(f"✅ 印章检测GPU模式初始化成功，耗时: {init_time:.2f}s")
        
        return True, seal_detector, init_time
        
    except Exception as e:
        print(f"❌ 印章检测GPU模式初始化失败: {e}")
        return False, None, 0

def main():
    """主函数"""
    print("🚀 RTX 3060 CUDA功能测试")
    print("=" * 60)
    
    # 检查CUDA环境
    cuda_available = check_cuda_environment()
    
    if not cuda_available:
        print("\n❌ CUDA环境不可用，无法进行GPU测试")
        return False
    
    # 测试CPU模式（作为基准）
    cpu_success, cpu_ocr, cpu_init_time = test_paddleocr_cpu()
    cpu_process_time = 0
    cpu_text_blocks = 0
    
    if cpu_success:
        cpu_success, cpu_process_time, cpu_text_blocks = test_ocr_performance(cpu_ocr, "CPU")
    
    # 测试GPU模式
    gpu_success, gpu_ocr, gpu_init_time = test_paddleocr_gpu()
    gpu_process_time = 0
    gpu_text_blocks = 0
    
    if gpu_success:
        gpu_success, gpu_process_time, gpu_text_blocks = test_ocr_performance(gpu_ocr, "GPU")
    
    # 测试印章检测GPU模式
    seal_gpu_success, _, seal_init_time = test_seal_detection_gpu()
    
    # 性能对比
    print("\n" + "=" * 60)
    print("📊 性能对比结果:")
    print("-" * 40)
    
    if cpu_success and gpu_success:
        speedup = cpu_process_time / gpu_process_time if gpu_process_time > 0 else 0
        print(f"  CPU初始化时间: {cpu_init_time:.2f}s")
        print(f"  GPU初始化时间: {gpu_init_time:.2f}s")
        print(f"  CPU处理时间: {cpu_process_time:.2f}s")
        print(f"  GPU处理时间: {gpu_process_time:.2f}s")
        print(f"  GPU加速比: {speedup:.2f}x")
        print(f"  文本块检测: CPU={cpu_text_blocks}, GPU={gpu_text_blocks}")
    
    print(f"  印章检测GPU: {'✅' if seal_gpu_success else '❌'}")
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print(f"  CUDA环境: {'✅' if cuda_available else '❌'}")
    print(f"  PaddleOCR CPU: {'✅' if cpu_success else '❌'}")
    print(f"  PaddleOCR GPU: {'✅' if gpu_success else '❌'}")
    print(f"  印章检测GPU: {'✅' if seal_gpu_success else '❌'}")
    
    if gpu_success and seal_gpu_success:
        print("\n🎉 RTX 3060 CUDA功能完全正常！")
        print("💡 建议使用GPU模式以获得更好的性能")
        return True
    elif gpu_success:
        print("\n⚠️ OCR GPU功能正常，但印章检测有问题")
        return False
    else:
        print("\n❌ GPU功能存在问题，建议使用CPU模式")
        return False

if __name__ == "__main__":
    main()
