<template>
  <div class="content-accuracy-report">
    <!-- 报告头部统计 -->
    <el-card class="report-summary" shadow="never">
      <div slot="header" class="card-header">
        <span class="report-title">
          <i class="el-icon-document-checked"></i>
          条目内容正确性检查报告
        </span>
        <div class="report-actions">
          <el-button size="small" @click="exportReport" :loading="exporting">
            <i class="el-icon-download"></i> 导出报告
          </el-button>
          <el-button size="small" type="primary" @click="exportExcelWithErrors" :loading="exportingExcel">
            <i class="el-icon-s-grid"></i> 导出标注Excel
          </el-button>
        </div>
      </div>

      <div class="summary-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value total">{{ reportData.totalRecords }}</div>
              <div class="stat-label">总记录数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value error">{{ reportData.errorRecords }}</div>
              <div class="stat-label">错误记录</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value success">{{ reportData.correctRecords }}</div>
              <div class="stat-label">正确记录</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value rate">{{ reportData.accuracyRate }}%</div>
              <div class="stat-label">准确率</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 字段错误统计 -->
      <div class="field-error-stats">
        <h4>各字段错误统计</h4>
        <el-row :gutter="16">
          <el-col 
            v-for="fieldStat in reportData.fieldStats" 
            :key="fieldStat.field"
            :span="6"
          >
            <div class="field-stat-card">
              <div class="field-name">{{ fieldStat.fieldName }}</div>
              <div class="field-stats">
                <div class="error-count">{{ fieldStat.errorCount }} 错误</div>
                <div class="error-rate">{{ fieldStat.errorRate }}% 错误率</div>
              </div>
              <el-progress 
                :percentage="100 - fieldStat.errorRate" 
                :color="getProgressColor(fieldStat.errorRate)"
                :show-text="false"
                :stroke-width="6"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 错误类型分析 -->
    <el-card class="error-analysis" shadow="never">
      <div slot="header">
        <span>错误类型分析</span>
      </div>

      <div class="error-type-charts">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="chart-container">
              <h5>错误类型分布</h5>
              <div ref="errorTypeChart" class="chart"></div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="chart-container">
              <h5>字段错误对比</h5>
              <div ref="fieldErrorChart" class="chart"></div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 错误类型说明 -->
      <div class="error-type-legend">
        <h5>错误类型说明</h5>
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="legend-item">
              <el-tag type="danger" size="small">完全不匹配</el-tag>
              <p>AI提取结果与Excel录入完全不同</p>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="legend-item">
              <el-tag type="warning" size="small">部分匹配</el-tag>
              <p>内容相似但存在差异，低于设定阈值</p>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="legend-item">
              <el-tag type="info" size="small">格式错误</el-tag>
              <p>内容正确但格式不符合规范</p>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="legend-item">
              <el-tag type="danger" size="small">缺失内容</el-tag>
              <p>AI无法提取到对应内容</p>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 错误明细列表 -->
    <el-card class="error-details" shadow="never">
      <div slot="header" class="card-header">
        <span>错误明细</span>
        <div class="detail-filters">
          <el-select v-model="filterField" placeholder="筛选字段" size="small" clearable>
            <el-option label="全部字段" value="" />
            <el-option 
              v-for="field in availableFields" 
              :key="field.code"
              :label="field.name" 
              :value="field.code" 
            />
          </el-select>
          <el-select v-model="filterErrorType" placeholder="错误类型" size="small" clearable>
            <el-option label="全部类型" value="" />
            <el-option label="完全不匹配" value="mismatch" />
            <el-option label="部分匹配" value="partial" />
            <el-option label="格式错误" value="format" />
            <el-option label="缺失内容" value="missing" />
          </el-select>
        </div>
      </div>

      <el-table 
        :data="filteredErrorDetails" 
        border 
        size="small"
        :max-height="600"
        @row-click="showErrorDetail"
      >
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="rowIndex" label="行号" width="80" />
        <el-table-column prop="field" label="字段" width="100">
          <template slot-scope="scope">
            <el-tag size="mini" type="primary">{{ getFieldName(scope.row.field) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="errorType" label="错误类型" width="120">
          <template slot-scope="scope">
            <el-tag 
              size="mini" 
              :type="getErrorTypeTagType(scope.row.errorType)"
            >
              {{ getErrorTypeName(scope.row.errorType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="excelValue" label="Excel录入值" min-width="150">
          <template slot-scope="scope">
            <div class="cell-content excel-value">{{ scope.row.excelValue || '(空)' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="extractedValue" label="AI提取值" min-width="150">
          <template slot-scope="scope">
            <div class="cell-content extracted-value">{{ scope.row.extractedValue || '(未提取到)' }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="similarity" label="相似度" width="100">
          <template slot-scope="scope">
            <el-progress 
              :percentage="scope.row.similarity" 
              :color="getSimilarityColor(scope.row.similarity)"
              :show-text="true"
              :stroke-width="8"
              text-inside
            />
          </template>
        </el-table-column>
        <el-table-column prop="suggestion" label="修正建议" min-width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.suggestion" class="suggestion-content">
              <i class="el-icon-lightbulb"></i>
              {{ scope.row.suggestion }}
            </div>
            <span v-else class="no-suggestion">无建议</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click.stop="showErrorDetail(scope.row)">
              详情
            </el-button>
            <el-button type="text" size="small" @click.stop="locateInExcel(scope.row)">
              定位
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[20, 50, 100, 200]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalErrors"
        />
      </div>
    </el-card>

    <!-- 错误详情对话框 -->
    <el-dialog
      title="错误详情"
      :visible.sync="errorDetailVisible"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedError" class="error-detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="行号">{{ selectedError.rowIndex }}</el-descriptions-item>
          <el-descriptions-item label="字段">{{ getFieldName(selectedError.field) }}</el-descriptions-item>
          <el-descriptions-item label="错误类型">
            <el-tag :type="getErrorTypeTagType(selectedError.errorType)">
              {{ getErrorTypeName(selectedError.errorType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="相似度">{{ selectedError.similarity }}%</el-descriptions-item>
        </el-descriptions>

        <div class="value-comparison">
          <h4>内容对比</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="comparison-item">
                <h5>Excel录入值</h5>
                <div class="value-box excel-value-box">
                  {{ selectedError.excelValue || '(空)' }}
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="comparison-item">
                <h5>AI提取值</h5>
                <div class="value-box extracted-value-box">
                  {{ selectedError.extractedValue || '(未提取到)' }}
                </div>
              </div>
            </el-col>
          </el-row>
        </div>

        <div v-if="selectedError.suggestion" class="suggestion-section">
          <h4>修正建议</h4>
          <div class="suggestion-box">
            <i class="el-icon-lightbulb"></i>
            {{ selectedError.suggestion }}
          </div>
        </div>

        <div v-if="selectedError.documentImage" class="document-preview">
          <h4>文档预览</h4>
          <div class="image-container">
            <img :src="selectedError.documentImage" alt="文档预览" />
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="errorDetailVisible = false">关闭</el-button>
        <el-button type="primary" @click="acceptSuggestion" v-if="selectedError && selectedError.suggestion">
          采纳建议
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ContentAccuracyReport',
  props: {
    reportData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      exporting: false,
      exportingExcel: false,
      filterField: '',
      filterErrorType: '',
      currentPage: 1,
      pageSize: 20,
      errorDetailVisible: false,
      selectedError: null,
      
      availableFields: [
        { code: 'title', name: '题名' },
        { code: 'responsible', name: '责任者' },
        { code: 'documentNo', name: '文号' },
        { code: 'issueDate', name: '成文日期' }
      ]
    }
  },
  computed: {
    filteredErrorDetails() {
      let filtered = this.reportData.errorDetails || []
      
      if (this.filterField) {
        filtered = filtered.filter(item => item.field === this.filterField)
      }
      
      if (this.filterErrorType) {
        filtered = filtered.filter(item => item.errorType === this.filterErrorType)
      }
      
      // 分页
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return filtered.slice(start, end)
    },
    
    totalErrors() {
      let filtered = this.reportData.errorDetails || []
      
      if (this.filterField) {
        filtered = filtered.filter(item => item.field === this.filterField)
      }
      
      if (this.filterErrorType) {
        filtered = filtered.filter(item => item.errorType === this.filterErrorType)
      }
      
      return filtered.length
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initCharts()
    })
  },
  methods: {
    // 初始化图表
    initCharts() {
      this.initErrorTypeChart()
      this.initFieldErrorChart()
    },
    
    // 初始化错误类型图表
    initErrorTypeChart() {
      const chart = echarts.init(this.$refs.errorTypeChart)
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [
          {
            name: '错误类型',
            type: 'pie',
            radius: '70%',
            data: this.reportData.errorTypeStats || [],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      
      chart.setOption(option)
    },
    
    // 初始化字段错误图表
    initFieldErrorChart() {
      const chart = echarts.init(this.$refs.fieldErrorChart)
      
      const fieldNames = this.reportData.fieldStats?.map(item => item.fieldName) || []
      const errorCounts = this.reportData.fieldStats?.map(item => item.errorCount) || []
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: fieldNames
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '错误数量',
            type: 'bar',
            data: errorCounts,
            itemStyle: {
              color: '#f56c6c'
            }
          }
        ]
      }
      
      chart.setOption(option)
    },
    
    // 获取字段名称
    getFieldName(fieldCode) {
      const field = this.availableFields.find(f => f.code === fieldCode)
      return field ? field.name : fieldCode
    },
    
    // 获取错误类型名称
    getErrorTypeName(errorType) {
      const typeMap = {
        'mismatch': '完全不匹配',
        'partial': '部分匹配',
        'format': '格式错误',
        'missing': '缺失内容'
      }
      return typeMap[errorType] || errorType
    },
    
    // 获取错误类型标签类型
    getErrorTypeTagType(errorType) {
      const typeMap = {
        'mismatch': 'danger',
        'partial': 'warning',
        'format': 'info',
        'missing': 'danger'
      }
      return typeMap[errorType] || 'info'
    },
    
    // 获取进度条颜色
    getProgressColor(errorRate) {
      if (errorRate <= 10) return '#67c23a'
      if (errorRate <= 30) return '#e6a23c'
      return '#f56c6c'
    },
    
    // 获取相似度颜色
    getSimilarityColor(similarity) {
      if (similarity >= 80) return '#67c23a'
      if (similarity >= 60) return '#e6a23c'
      return '#f56c6c'
    },
    
    // 显示错误详情
    showErrorDetail(row) {
      this.selectedError = row
      this.errorDetailVisible = true
    },
    
    // 定位到Excel
    locateInExcel(row) {
      this.$emit('locate-excel', {
        rowIndex: row.rowIndex,
        field: row.field
      })
    },
    
    // 采纳建议
    acceptSuggestion() {
      if (this.selectedError && this.selectedError.suggestion) {
        this.$emit('accept-suggestion', {
          rowIndex: this.selectedError.rowIndex,
          field: this.selectedError.field,
          suggestion: this.selectedError.suggestion
        })
        this.errorDetailVisible = false
      }
    },
    
    // 导出报告
    async exportReport() {
      this.exporting = true
      
      try {
        const response = await this.$api.exportContentAccuracyReport({
          reportData: this.reportData
        })
        
        // 下载文件
        this.downloadFile(response.data, 'content_accuracy_report.pdf')
        this.$message.success('报告导出成功')
      } catch (error) {
        this.$message.error('报告导出失败')
        console.error('Export report error:', error)
      } finally {
        this.exporting = false
      }
    },
    
    // 导出标注Excel
    async exportExcelWithErrors() {
      this.exportingExcel = true
      
      try {
        const response = await this.$api.exportExcelWithErrorAnnotations({
          reportData: this.reportData
        })
        
        // 下载文件
        this.downloadFile(response.data, 'annotated_excel.xlsx')
        this.$message.success('标注Excel导出成功')
      } catch (error) {
        this.$message.error('Excel导出失败')
        console.error('Export Excel error:', error)
      } finally {
        this.exportingExcel = false
      }
    },
    
    // 下载文件
    downloadFile(data, filename) {
      const blob = new Blob([data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.click()
      window.URL.revokeObjectURL(url)
    },
    
    // 分页处理
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
    }
  }
}
</script>

<style scoped>
.content-accuracy-report {
  max-width: 1400px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.report-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.report-actions {
  display: flex;
  gap: 8px;
}

.summary-stats {
  margin-bottom: 24px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-value.total { color: #409EFF; }
.stat-value.error { color: #f56c6c; }
.stat-value.success { color: #67c23a; }
.stat-value.rate { color: #e6a23c; }

.stat-label {
  font-size: 14px;
  color: #666;
}

.field-error-stats h4 {
  margin-bottom: 16px;
  color: #333;
}

.field-stat-card {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fff;
}

.field-name {
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.field-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
}

.error-count {
  color: #f56c6c;
}

.error-rate {
  color: #666;
}

.error-type-charts {
  margin-bottom: 24px;
}

.chart-container h5 {
  text-align: center;
  margin-bottom: 12px;
  color: #333;
}

.chart {
  height: 300px;
}

.error-type-legend h5 {
  margin-bottom: 16px;
  color: #333;
}

.legend-item {
  text-align: center;
  padding: 12px;
}

.legend-item p {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.detail-filters {
  display: flex;
  gap: 12px;
}

.cell-content {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.excel-value {
  color: #333;
}

.extracted-value {
  color: #666;
}

.suggestion-content {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409EFF;
  font-size: 12px;
}

.no-suggestion {
  color: #ccc;
  font-size: 12px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.error-detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.value-comparison {
  margin: 20px 0;
}

.value-comparison h4 {
  margin-bottom: 12px;
  color: #333;
}

.comparison-item h5 {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.value-box {
  padding: 12px;
  border-radius: 4px;
  min-height: 60px;
  word-break: break-all;
}

.excel-value-box {
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
}

.extracted-value-box {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
}

.suggestion-section {
  margin: 20px 0;
}

.suggestion-section h4 {
  margin-bottom: 12px;
  color: #333;
}

.suggestion-box {
  padding: 12px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  color: #409EFF;
}

.document-preview {
  margin: 20px 0;
}

.document-preview h4 {
  margin-bottom: 12px;
  color: #333;
}

.image-container {
  text-align: center;
  max-height: 300px;
  overflow: hidden;
}

.image-container img {
  max-width: 100%;
  max-height: 300px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}
</style>
