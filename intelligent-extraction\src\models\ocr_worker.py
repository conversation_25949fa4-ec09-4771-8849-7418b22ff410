#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR工作进程
独立运行PaddleOCR，避免与PyTorch冲突
"""
import os
import sys
import json
import warnings

# 设置编码
sys.stdout.reconfigure(encoding='utf-8')
sys.stderr.reconfigure(encoding='utf-8')

# 设置环境变量 - 在导入任何库之前
# RTX 3060 单卡配置 (12GB显存，Ampere架构)
os.environ['CUDA_VISIBLE_DEVICES'] = '0'
os.environ['FLAGS_allocator_strategy'] = 'auto_growth'
os.environ['FLAGS_fraction_of_gpu_memory_to_use'] = '0.7'  # RTX 3060有12GB显存，可以使用更多
os.environ['FLAGS_eager_delete_tensor_gb'] = '0.0'
os.environ['FLAGS_fast_eager_deletion_mode'] = 'true'
os.environ['PADDLE_DISABLE_STATIC'] = '1'
# RTX 3060 Ampere架构优化
os.environ['FLAGS_use_cuda'] = '1'
os.environ['FLAGS_cudnn_deterministic'] = '0'  # 提升性能

# 解决OpenCV问题
os.environ['OPENCV_IO_ENABLE_OPENEXR'] = '0'
os.environ['OPENCV_IO_ENABLE_JASPER'] = '0'

warnings.filterwarnings('ignore')

def main():
    """主函数"""
    try:
        if len(sys.argv) != 2:
            print(json.dumps({"error": "Usage: python ocr_worker.py <config_file>"}))
            sys.exit(1)

        config_file = sys.argv[1]

        # 读取配置
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 导入PaddleOCR - 延迟导入避免循环依赖
        try:
            from paddleocr import PaddleOCR
            # 同时尝试导入印章检测功能
            try:
                from paddleocr import SealTextDetection
                SEAL_DETECTION_AVAILABLE = True
            except ImportError:
                SEAL_DETECTION_AVAILABLE = False
                SealTextDetection = None
                print(json.dumps({
                "success": False,
                "error": f"SealTextDetection导入失败: {import_error}",
                "error_type": "ImportError"
            }))
        except Exception as import_error:
            print(json.dumps({
                "success": False,
                "error": f"PaddleOCR导入失败: {import_error}",
                "error_type": "ImportError"
            }))
            sys.exit(1)

        # 初始化OCR - 按照官方文档3.0.2的标准方式
        ocr_kwargs = {
            'use_doc_orientation_classify': False,  # 不使用文档方向分类
            'use_doc_unwarping': False,            # 不使用文本图像矫正
            'use_textline_orientation': config.get('use_angle_cls', False),  # 文本行方向分类
            'lang': config.get('lang', 'ch')
            # 注意：移除硬编码的模型名称，使用默认缓存模型
        }

        # GPU模式下的参数配置 - 使用3.0.2的新参数名称
        if not config.get('use_cpu', True):
            # 使用官方推荐的默认参数
            ocr_kwargs['text_det_thresh'] = config.get('det_db_thresh', 0.3)  # 新参数名
            ocr_kwargs['text_det_box_thresh'] = config.get('det_db_box_thresh', 0.6)  # 新参数名
            ocr_kwargs['text_recognition_batch_size'] = config.get('rec_batch_num', 6)  # 新参数名
            # 禁用可能导致问题的功能
            ocr_kwargs['use_tensorrt'] = False  # 禁用TensorRT避免兼容性问题

        # 设备配置 - PaddleOCR 3.0使用device参数
        if config.get('use_cpu', True):
            ocr_kwargs['device'] = 'cpu'
            print(json.dumps({"debug": "使用CPU模式"}, ensure_ascii=False), file=sys.stderr)
        else:
            # PaddleOCR 3.0的GPU配置方式
            gpu_id = config.get('gpu_id', 0)
            ocr_kwargs['device'] = f'gpu:{gpu_id}'  # 格式: gpu:0, gpu:1 等
            print(json.dumps({
                "debug": "使用GPU模式",
                "gpu_id": gpu_id,
                "device": f"gpu:{gpu_id}"
            }, ensure_ascii=False), file=sys.stderr)

        # 尝试指定本地模型路径避免重复下载
        # 检查是否有本地模型缓存目录
        home_dir = os.path.expanduser("~")
        paddleocr_cache = os.path.join(home_dir, ".paddleocr")
        if os.path.exists(paddleocr_cache):
            print(json.dumps({
                "debug": "找到PaddleOCR缓存目录",
                "cache_dir": paddleocr_cache
            }, ensure_ascii=False), file=sys.stderr)

        # 创建OCR引擎
        try:
            print(json.dumps({
                "debug": "开始初始化OCR引擎",
                "ocr_kwargs": ocr_kwargs
            }, ensure_ascii=False), file=sys.stderr)

            ocr_engine = PaddleOCR(**ocr_kwargs)

            print(json.dumps({
                "debug": "OCR引擎初始化成功"
            }, ensure_ascii=False), file=sys.stderr)
        except Exception as ocr_error:
            print(json.dumps({
                "success": False,
                "error": f"OCR引擎初始化失败: {ocr_error}",
                "error_type": "OCRInitError"
            }))
            sys.exit(1)

        # 处理图像
        image_path = config['image_path']

        # 检查是否启用印章检测
        enable_stamp_processing = False #config.get('enable_stamp_processing', False)
        stamp_confidence_threshold = config.get('stamp_confidence_threshold', 0.8)
        # 调试信息：打印路径和文件存在性
        print(json.dumps({
            "debug": "OCR引擎中的印章处理参数",
            "image_path": image_path,
            "path_exists": os.path.exists(image_path),
            "current_working_dir": os.getcwd(),
            "path_is_absolute": os.path.isabs(image_path),
            "enable_stamp_processing": enable_stamp_processing,
            "stamp_available": SEAL_DETECTION_AVAILABLE
        }, ensure_ascii=False), file=sys.stderr)

        # 印章检测处理（如果启用）
        stamp_results = []
        if enable_stamp_processing and SEAL_DETECTION_AVAILABLE:
            try:
                print(json.dumps({
                    "debug": "开始印章检测",
                    "model_available": SEAL_DETECTION_AVAILABLE
                }, ensure_ascii=False), file=sys.stderr)
                # stamp_info = _process_stamps_if_enabled(img)
                # if stamp_info.get('versions', {}).get('main'):
                #     img = stamp_info['versions']['main']

                # 初始化印章检测模型 - 根据OCR设备配置决定
                device_config = 'cpu' if config.get('use_cpu', True) else 'gpu:0'
                seal_detector = SealTextDetection(
                    model_name="PP-OCRv4_mobile_seal_det",
                    device=device_config  # 与OCR使用相同的设备配置
                )

                # 执行印章检测
                stamp_detection_result = seal_detector.predict(image_path)

                if stamp_detection_result and len(stamp_detection_result) > 0:
                    stamp_data = stamp_detection_result[0]
                    dt_polys = stamp_data.get('dt_polys', [])
                    dt_scores = stamp_data.get('dt_scores', [])

                    # 过滤高置信度的印章区域
                    for i, (poly, score) in enumerate(zip(dt_polys, dt_scores)):
                        if score >= stamp_confidence_threshold:
                            stamp_results.append({
                                'stamp_id': i,
                                'polygon': poly,
                                'confidence': float(score)
                            })

                print(json.dumps({
                    "debug": "印章检测完成",
                    "detected_stamps": len(stamp_results)
                }, ensure_ascii=False), file=sys.stderr)

            except Exception as stamp_error:
                print(json.dumps({
                    "debug": "印章检测失败",
                    "error": str(stamp_error)
                }, ensure_ascii=False), file=sys.stderr)

        try:
            # 添加更详细的调试信息
            print(json.dumps({
                "debug": "开始OCR处理",
                "ocr_engine_type": str(type(ocr_engine)),
                "available_methods": [method for method in dir(ocr_engine) if not method.startswith('_')],
                "image_path": image_path
            }, ensure_ascii=False), file=sys.stderr)

            # PaddleOCR 3.0.2官方推荐使用predict方法
            print(json.dumps({"debug": "使用predict方法"}, ensure_ascii=False), file=sys.stderr)
            results = ocr_engine.predict(image_path)

            print(json.dumps({
                "debug": "OCR处理完成",
                "results_type": str(type(results)),
                "results_length": len(results) if results else 0,
                "device_mode": config.get('use_cpu', True) and "CPU" or "GPU"
            }, ensure_ascii=False), file=sys.stderr)

        except Exception as ocr_process_error:
            import traceback
            print(json.dumps({
                "success": False,
                "error": f"OCR处理失败: {ocr_process_error}",
                "error_type": "OCRProcessError",
                "error_details": str(ocr_process_error),
                "traceback": traceback.format_exc()
            }, ensure_ascii=False))
            sys.exit(1)

        # 处理结果 - 支持PaddleX新格式
        processed_results = []

        print(json.dumps({
            "debug": "开始处理OCR结果",
            "results_type": str(type(results)),
            "results_length": len(results) if results else 0
        }, ensure_ascii=False), file=sys.stderr)

        if results and len(results) > 0:
            ocr_result = results[0]  # 获取第一个结果

            print(json.dumps({
                "debug": "OCR结果类型",
                "type": str(type(ocr_result)),
                "available_attrs": [attr for attr in dir(ocr_result) if not attr.startswith('_')],
                "device_mode": config.get('use_cpu', True) and "CPU" or "GPU",
                "ocr_result_str": str(ocr_result)[:200]  # 显示前200个字符
            }, ensure_ascii=False), file=sys.stderr)

            # 检查是否是字典类型的OCRResult对象
            if hasattr(ocr_result, 'keys') and hasattr(ocr_result, 'get'):
                print(json.dumps({
                    "debug": "检测到字典类型OCRResult",
                    "keys": list(ocr_result.keys()) if hasattr(ocr_result, 'keys') else []
                }, ensure_ascii=False), file=sys.stderr)

                # 尝试获取文本检测和识别结果
                # 可能的键名：dt_polys, rec_texts, rec_scores 等
                dt_polys = ocr_result.get('dt_polys', [])
                rec_texts = ocr_result.get('rec_texts', [])
                rec_scores = ocr_result.get('rec_scores', [])

                print(json.dumps({
                    "debug": "字典内容分析",
                    "dt_polys_count": len(dt_polys) if dt_polys else 0,
                    "rec_texts_count": len(rec_texts) if rec_texts else 0,
                    "rec_scores_count": len(rec_scores) if rec_scores else 0,
                    "dt_polys_type": str(type(dt_polys)),
                    "rec_texts_type": str(type(rec_texts)),
                    "rec_scores_type": str(type(rec_scores))
                }, ensure_ascii=False), file=sys.stderr)

                # 如果有文本和分数数据
                if rec_texts and rec_scores:
                    # 确保dt_polys和文本数量匹配
                    polys_to_use = dt_polys if dt_polys and len(dt_polys) == len(rec_texts) else [None] * len(rec_texts)

                    for i, (text, score) in enumerate(zip(rec_texts, rec_scores)):
                        confidence = float(score)
                        poly = polys_to_use[i] if i < len(polys_to_use) else None

                        print(json.dumps({
                            "debug": f"处理文本块{i+1}",
                            "text": text,
                            "confidence": confidence,
                            "threshold": config.get('confidence_threshold', 0.5)
                        }, ensure_ascii=False), file=sys.stderr)

                        # 应用置信度阈值
                        if confidence >= config.get('confidence_threshold', 0.5):
                            processed_results.append({
                                'text': text,
                                'confidence': confidence,
                                'bbox': poly.tolist() if poly is not None and hasattr(poly, 'tolist') else poly
                            })
                            print(json.dumps({
                                "debug": "添加文本块",
                                "text": text,
                                "confidence": confidence
                            }, ensure_ascii=False), file=sys.stderr)
                        else:
                            print(json.dumps({
                                "debug": "置信度过低，跳过",
                                "text": text,
                                "confidence": confidence
                            }, ensure_ascii=False), file=sys.stderr)
                else:
                    # 如果没有找到预期的键，打印所有可用的键值对进行调试
                    try:
                        all_items = {}
                        if hasattr(ocr_result, 'items'):
                            for k, v in ocr_result.items():
                                # 安全地转换值为字符串，避免序列化错误
                                try:
                                    if isinstance(v, (list, tuple)):
                                        all_items[k] = f"list/tuple with {len(v)} items: {str(v)[:200]}"
                                    elif hasattr(v, '__len__') and not isinstance(v, str):
                                        all_items[k] = f"object with length {len(v)}: {str(type(v))}"
                                    else:
                                        all_items[k] = str(v)[:100]
                                except Exception as e:
                                    all_items[k] = f"<无法序列化: {type(v)}>"

                        print(json.dumps({
                            "debug": "未找到预期的rec_texts/rec_scores，打印所有键值对",
                            "all_items": all_items
                        }, ensure_ascii=False), file=sys.stderr)
                    except Exception as debug_error:
                        print(json.dumps({
                            "debug": "调试信息输出失败",
                            "error": str(debug_error)
                        }, ensure_ascii=False), file=sys.stderr)

            # 尝试传统格式（兼容性）
            elif isinstance(ocr_result, list):
                print(json.dumps({"debug": "使用传统格式"}, ensure_ascii=False), file=sys.stderr)

                for line in ocr_result:
                    if line and len(line) >= 2:
                        bbox = line[0]
                        text_info = line[1]

                        if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = float(text_info[1])

                            if confidence >= config.get('confidence_threshold', 0.5):
                                processed_results.append({
                                    'text': text,
                                    'confidence': confidence,
                                    'bbox': bbox
                                })
            else:
                print(json.dumps({
                    "debug": "未知的OCR结果格式",
                    "type": str(type(ocr_result))
                }, ensure_ascii=False), file=sys.stderr)

        # 返回结果
        result = {
            'success': True,
            'results': processed_results,
            'total_blocks': len(processed_results),
            'stamp_detection': {
                'enabled': enable_stamp_processing,
                'available': SEAL_DETECTION_AVAILABLE,
                'stamps': stamp_results,
                'total_stamps': len(stamp_results)
            }
        }

        print(json.dumps(result, ensure_ascii=False))

    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'error_type': type(e).__name__
        }
        print(json.dumps(error_result, ensure_ascii=False))
        sys.exit(1)

# def _process_stamps_if_enabled(self, img: Image.Image) -> Dict[str, Any]:
#         """处理印章（使用模型池中的模型）"""
#         stamp_config = self.config.get('stamp_processing', {})

#         if not stamp_config.get('enabled', False):
#             return {'enabled': False}

#         # 从模型池获取印章检测器
#         self._init_seal_detector()

#         if not self.seal_detector:
#             return {'enabled': True, 'detector_available': False}

#         try:
#             # 使用模型池中的印章检测模型
#             detection_result = self.seal_detector.detect_seals(
#                 img,
#                 confidence_threshold=stamp_config.get('confidence_threshold', 0.8)
#             )

#             stamp_info = {
#                 'enabled': True,
#                 'detector_available': True,
#                 'detected_stamps': detection_result.detected_stamps,
#                 'stamp_regions': detection_result.stamp_regions,
#                 'detection_time': detection_result.detection_time,
#                 'versions': {}
#             }

#             # 如果检测到印章，创建处理版本
#             if detection_result.detected_stamps > 0 and stamp_config.get('create_multiple_versions', True):
#                 stamp_info['versions'] = self._create_stamp_processed_versions(
#                     img, detection_result.stamp_regions, stamp_config
#                 )

#             return stamp_info

#         except Exception as e:
#             self.logger.error(f"印章处理失败: {e}")
#             return {
#                 'enabled': True,
#                 'detector_available': True,
#                 'error': str(e),
#                 'detected_stamps': 0
#             }

# def _create_stamp_processed_versions(self, img: Image.Image,
#                                     stamp_regions: list,
#                                     stamp_config: dict) -> Dict[str, Image.Image]:
#     """创建印章处理的版本（简化为两个版本）"""
#     versions = {
#         'original': img.copy()  # 保留原图
#     }

#     # 只创建红蓝色移除+增强版本
#     try:
#         versions['redblue_removed'] = self._remove_redblue_channel(img, stamp_regions)
#         # 设置主版本为处理后的版本
#         versions['main'] = versions['redblue_removed']
#     except Exception as e:
#         self.logger.warning(f"红蓝色通道移除失败: {e}")
#         # 失败时使用原图作为主版本
#         versions['main'] = versions['original']

#     return versions

# def _remove_redblue_channel(self, img: Image.Image, stamp_regions: list) -> Image.Image:
#     """移除红蓝色通道，保留底层文字，同时增强对比度"""
#     try:
#         import cv2
#         import numpy as np
#     except ImportError:
#         self.logger.error("OpenCV未安装，无法进行印章处理")
#         return img

#     # PIL转OpenCV
#     img_array = np.array(img)
#     if len(img_array.shape) == 3:
#         img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
#     else:
#         img_bgr = img_array

#     # 转换到HSV颜色空间
#     hsv = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2HSV)

#     # 定义红色范围（HSV）- 红色在HSV中分布在两个区间
#     lower_red1 = np.array([0, 50, 50])
#     upper_red1 = np.array([10, 255, 255])
#     lower_red2 = np.array([160, 50, 50])
#     upper_red2 = np.array([180, 255, 255])

#     # 定义蓝色范围（HSV）
#     lower_blue = np.array([100, 50, 50])
#     upper_blue = np.array([130, 255, 255])

#     # 创建红色和蓝色掩码
#     red_mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
#     red_mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
#     red_mask = cv2.bitwise_or(red_mask1, red_mask2)

#     blue_mask = cv2.inRange(hsv, lower_blue, upper_blue)

#     # 合并红蓝掩码
#     redblue_mask = cv2.bitwise_or(red_mask, blue_mask)

#     # 在印章区域内应用掩码
#     for region in stamp_regions:
#         polygon = np.array(region['polygon'], dtype=np.int32)

#         # 创建区域掩码
#         region_mask = np.zeros(img_bgr.shape[:2], dtype=np.uint8)
#         cv2.fillPoly(region_mask, [polygon], 255)

#         # 只在印章区域内移除红蓝色
#         combined_mask = cv2.bitwise_and(redblue_mask, region_mask)

#         # 将红蓝色区域替换为白色（保留底层文字）
#         img_bgr[combined_mask > 0] = [255, 255, 255]

#     # 转换回PIL并进行对比度增强
#     if len(img_array.shape) == 3:
#         result_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)
#     else:
#         result_rgb = img_bgr

#     result_img = Image.fromarray(result_rgb)

#     # 合并对比度增强逻辑
#     enhancer = ImageEnhance.Contrast(result_img)
#     result_img = enhancer.enhance(1.2)

#     # 锐化处理
#     sharpness_enhancer = ImageEnhance.Sharpness(result_img)
#     result_img = sharpness_enhancer.enhance(1.3)

#     return result_img

if __name__ == "__main__":
    main()
