#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像预处理优化效果测试脚本
专门测试A4扫描文档的预处理优化效果
"""
import asyncio
import aiohttp
import json
import time
import sys
from pathlib import Path
from PIL import Image
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_preprocessing_comparison(image_path: str, server_url: str = "http://localhost:8080"):
    """对比测试启用/禁用图像预处理的效果"""
    
    # 验证文件
    image_file = Path(image_path)
    if not image_file.exists():
        print(f"❌ 文件不存在: {image_path}")
        return False
    
    print(f"📄 测试文档: {image_file.name}")
    print("=" * 70)
    
    # 分析原始图像
    try:
        with Image.open(image_file) as img:
            width, height = img.size
            file_size = image_file.stat().st_size / (1024 * 1024)
            print(f"📊 原始图像: {width}×{height}, {file_size:.2f}MB, {img.format}")
    except Exception as e:
        print(f"⚠️ 图像分析失败: {e}")
    
    # 测试配置
    test_configs = [
        {"name": "禁用预处理", "enable_preprocessing": False},
        {"name": "启用预处理", "enable_preprocessing": True}
    ]
    
    results = {}
    
    async with aiohttp.ClientSession() as session:
        for config in test_configs:
            print(f"\n🧪 测试配置: {config['name']}")
            print("-" * 50)
            
            try:
                # 准备请求数据
                data = aiohttp.FormData()
                data.add_field('file', 
                              open(image_file, 'rb'), 
                              filename=image_file.name)
                data.add_field('custom_keys', '题名,责任者,文号,发文日期')
                data.add_field('confidence_threshold', '0.5')
                data.add_field('enable_preprocessing', str(config['enable_preprocessing']).lower())
                
                start_time = time.time()
                
                async with session.post(f"{server_url}/extract/archive", 
                                      data=data) as response:
                    
                    total_time = time.time() - start_time
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        if result.get('success'):
                            # 提取关键指标
                            processing_time = result.get('processing_time', total_time)
                            elements = result.get('results', {})
                            ocr_info = result.get('ocr_info', {})
                            preprocessing_info = ocr_info.get('image_preprocessing', {})
                            
                            # 统计提取成功率
                            extracted_count = sum(1 for v in elements.values() if v and v != "null")
                            success_rate = extracted_count / len(elements) * 100 if elements else 0
                            
                            results[config['name']] = {
                                'success': True,
                                'processing_time': processing_time,
                                'total_time': total_time,
                                'extracted_elements': extracted_count,
                                'total_elements': len(elements),
                                'success_rate': success_rate,
                                'ocr_blocks': ocr_info.get('total_blocks', 0),
                                'preprocessing_info': preprocessing_info,
                                'elements': elements
                            }
                            
                            print(f"✅ 处理成功")
                            print(f"⏱️ 处理时间: {processing_time:.2f}秒")
                            print(f"📊 提取成功率: {extracted_count}/{len(elements)} ({success_rate:.1f}%)")
                            print(f"📝 OCR文本块: {ocr_info.get('total_blocks', 0)}")
                            
                            if config['enable_preprocessing'] and preprocessing_info:
                                print(f"🔧 图像优化:")
                                print(f"   原始大小: {preprocessing_info.get('original_size_kb', 0):.1f}KB")
                                print(f"   处理后大小: {preprocessing_info.get('processed_size_kb', 0):.1f}KB")
                                print(f"   压缩比: {preprocessing_info.get('size_reduction', 0):.1f}%")
                            
                            print(f"📋 提取结果:")
                            for key, value in elements.items():
                                status = "✅" if value and value != "null" else "❌"
                                print(f"   {status} {key}: {value}")
                        else:
                            results[config['name']] = {
                                'success': False,
                                'error': result.get('error'),
                                'processing_time': total_time
                            }
                            print(f"❌ 提取失败: {result.get('error')}")
                    else:
                        error_text = await response.text()
                        results[config['name']] = {
                            'success': False,
                            'error': f"HTTP {response.status}: {error_text}",
                            'processing_time': total_time
                        }
                        print(f"❌ HTTP错误 {response.status}: {error_text}")
                        
            except Exception as e:
                results[config['name']] = {
                    'success': False,
                    'error': str(e),
                    'processing_time': 0
                }
                print(f"❌ 请求异常: {e}")
    
    # 对比分析
    print(f"\n{'='*70}")
    print("📊 对比分析结果:")
    print("="*70)
    
    if all(r.get('success') for r in results.values()):
        disabled = results["禁用预处理"]
        enabled = results["启用预处理"]
        
        # 性能对比
        time_improvement = (disabled['processing_time'] - enabled['processing_time']) / disabled['processing_time'] * 100
        
        print(f"⏱️ 处理时间对比:")
        print(f"   禁用预处理: {disabled['processing_time']:.2f}秒")
        print(f"   启用预处理: {enabled['processing_time']:.2f}秒")
        print(f"   性能提升: {time_improvement:+.1f}% {'🚀' if time_improvement > 0 else '⚠️'}")
        
        # 精度对比
        print(f"\n📈 提取精度对比:")
        print(f"   禁用预处理: {disabled['success_rate']:.1f}% ({disabled['extracted_elements']}/{disabled['total_elements']})")
        print(f"   启用预处理: {enabled['success_rate']:.1f}% ({enabled['extracted_elements']}/{enabled['total_elements']})")
        
        accuracy_change = enabled['success_rate'] - disabled['success_rate']
        print(f"   精度变化: {accuracy_change:+.1f}% {'📈' if accuracy_change >= 0 else '📉'}")
        
        # OCR效果对比
        print(f"\n📝 OCR效果对比:")
        print(f"   禁用预处理: {disabled['ocr_blocks']}个文本块")
        print(f"   启用预处理: {enabled['ocr_blocks']}个文本块")
        
        # 详细元素对比
        print(f"\n📋 元素提取详细对比:")
        all_keys = set(disabled['elements'].keys()) | set(enabled['elements'].keys())
        for key in all_keys:
            disabled_val = disabled['elements'].get(key, 'N/A')
            enabled_val = enabled['elements'].get(key, 'N/A')
            
            disabled_ok = disabled_val and disabled_val != "null"
            enabled_ok = enabled_val and enabled_val != "null"
            
            if disabled_ok and enabled_ok:
                status = "✅✅" if disabled_val == enabled_val else "✅🔄"
            elif not disabled_ok and enabled_ok:
                status = "❌✅"
            elif disabled_ok and not enabled_ok:
                status = "✅❌"
            else:
                status = "❌❌"
            
            print(f"   {status} {key}:")
            print(f"      禁用: {disabled_val}")
            print(f"      启用: {enabled_val}")
        
        # 总结
        print(f"\n🎯 优化效果总结:")
        if time_improvement > 10:
            print(f"   ⚡ 显著性能提升: {time_improvement:.1f}%")
        elif time_improvement > 0:
            print(f"   📈 轻微性能提升: {time_improvement:.1f}%")
        else:
            print(f"   ⚠️ 性能略有下降: {time_improvement:.1f}%")
        
        if accuracy_change > 0:
            print(f"   📈 提取精度提升: {accuracy_change:.1f}%")
        elif accuracy_change == 0:
            print(f"   ➡️ 提取精度保持不变")
        else:
            print(f"   📉 提取精度下降: {accuracy_change:.1f}%")
        
        return True
    else:
        print("❌ 部分测试失败，无法进行完整对比")
        for name, result in results.items():
            if not result.get('success'):
                print(f"   {name}: {result.get('error')}")
        return False


async def get_preprocessing_stats(server_url: str = "http://localhost:8080"):
    """获取预处理统计信息"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{server_url}/stats/preprocessing") as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get('success'):
                        stats = result.get('stats', {})
                        print(f"\n📊 图像预处理统计:")
                        print(f"   总处理数量: {stats.get('total_processed', 0)}")
                        print(f"   平均处理时间: {stats.get('avg_processing_time', 0):.2f}秒")
                        print(f"   平均压缩比: {stats.get('size_reduction_ratio', 0)*100:.1f}%")
                        print(f"   总处理时间: {stats.get('total_time', 0):.2f}秒")
                        return True
    except Exception as e:
        print(f"⚠️ 获取统计信息失败: {e}")
    return False


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("📄 图像预处理优化效果测试工具")
        print("=" * 50)
        print("用法: python test_preprocessing_optimization.py <图像路径>")
        print("示例: python test_preprocessing_optimization.py C:\\Documents\\scan.jpg")
        print()
        print("💡 此工具将对比测试启用/禁用图像预处理的效果差异")
        return
    
    image_path = sys.argv[1]
    
    print("🧪 开始图像预处理优化效果测试")
    print("=" * 70)
    
    # 运行对比测试
    success = asyncio.run(test_preprocessing_comparison(image_path))
    
    if success:
        # 获取统计信息
        asyncio.run(get_preprocessing_stats())
        print("\n🎉 测试完成！")
    else:
        print("\n❌ 测试失败")


if __name__ == "__main__":
    main()
