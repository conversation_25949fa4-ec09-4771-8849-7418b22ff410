#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试OCR worker脚本
"""

import os
import sys
import json
import tempfile
import subprocess

def test_ocr_worker_direct():
    """直接测试OCR worker"""
    print("=== 直接测试OCR Worker ===")
    
    # 测试图片路径
    test_image = "uploads/6ebdf762-509d-418c-ad3e-0b987e786ac4.jpg"
    #test_image = "uploads/125ef0ca-61e6-4cf5-9845-e8b9f3bd1358.jpg"
    if not os.path.exists(test_image):
        print(f"❌ 测试图片不存在: {test_image}")
        return False
    
    # 创建临时配置文件
    config = {
        "image_path": os.path.abspath(test_image),
        "use_cpu": True,
        "use_angle_cls": True,
        "lang": "ch",
        "confidence_threshold": 0.5
    }
    
    # 写入临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
        config_file = f.name
    
    try:
        # 直接调用OCR worker脚本
        worker_script = "src/models/ocr_worker.py"
        cmd = [sys.executable, worker_script, config_file]
        
        print(f"执行命令: {' '.join(cmd)}")
        print(f"配置文件: {config_file}")
        print(f"图片路径: {config['image_path']}")
        
        # 运行worker脚本
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            cwd=os.getcwd()
        )
        
        print(f"\n--- 返回码: {result.returncode} ---")
        
        if result.stdout:
            print("--- STDOUT ---")
            try:
                # 尝试解析JSON输出
                output_data = json.loads(result.stdout)
                print(json.dumps(output_data, ensure_ascii=False, indent=2))
                
                # 分析结果
                if output_data.get('success'):
                    results = output_data.get('results', [])
                    print(f"\n✅ OCR成功，识别到 {len(results)} 个文本块")
                    for i, item in enumerate(results):
                        print(f"  文本块{i+1}: '{item.get('text', '')}' (置信度: {item.get('confidence', 0):.3f})")
                else:
                    print(f"❌ OCR失败: {output_data.get('error', '未知错误')}")
                    
            except json.JSONDecodeError:
                print("原始输出:")
                print(result.stdout)
        
        if result.stderr:
            print("\n--- STDERR (调试信息) ---")
            # 按行处理stderr，每行可能是一个JSON
            for line in result.stderr.strip().split('\n'):
                if line.strip():
                    try:
                        debug_data = json.loads(line)
                        print(json.dumps(debug_data, ensure_ascii=False, indent=2))
                    except json.JSONDecodeError:
                        print(f"非JSON调试信息: {line}")
        
        return result.returncode == 0
        
    finally:
        # 清理临时文件
        try:
            os.unlink(config_file)
        except:
            pass

if __name__ == "__main__":
    success = test_ocr_worker_direct()
    if success:
        print("\n✅ OCR Worker测试完成")
    else:
        print("\n❌ OCR Worker测试失败")
