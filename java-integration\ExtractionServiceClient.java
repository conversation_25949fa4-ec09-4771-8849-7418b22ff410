package com.toolbox.extraction.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.ResourceAccessException;

import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 信息提取服务客户端
 * 与Python智能提取模块通信
 */
@Service
public class ExtractionServiceClient {
    
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    @Value("${extraction.service.url:http://localhost:8003}")
    private String serviceUrl;
    
    @Value("${extraction.service.timeout:60000}")
    private int timeoutMs;
    
    @Value("${extraction.service.retry.max:3}")
    private int maxRetries;
    
    public ExtractionServiceClient(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 提取档案要素（同步）
     */
    public ExtractionResult extractArchiveElements(File documentFile) {
        return extractArchiveElements(documentFile, null, true);
    }
    
    /**
     * 提取档案要素
     */
    public ExtractionResult extractArchiveElements(File documentFile, 
                                                 List<String> customKeys, 
                                                 boolean sync) {
        try {
            String url = serviceUrl + "/extract/archive";
            
            // 构建请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", new FileSystemResource(documentFile));
            body.add("sync", sync);
            
            if (customKeys != null && !customKeys.isEmpty()) {
                body.add("custom_keys", String.join(",", customKeys));
            }
            
            HttpEntity<MultiValueMap<String, Object>> requestEntity = 
                new HttpEntity<>(body, headers);
            
            // 发送请求
            ResponseEntity<ExtractionResponse> response = restTemplate.exchange(
                url, HttpMethod.POST, requestEntity, ExtractionResponse.class
            );
            
            ExtractionResponse extractionResponse = response.getBody();
            if (extractionResponse == null) {
                throw new ExtractionException("服务响应为空");
            }
            
            if (!extractionResponse.isSuccess()) {
                throw new ExtractionException("提取失败: " + extractionResponse.getError());
            }
            
            // 处理同步/异步结果
            if (sync) {
                return new ExtractionResult(
                    true,
                    extractionResponse.getResults(),
                    extractionResponse.getProcessingTime()
                );
            } else {
                return new ExtractionResult(
                    extractionResponse.getTaskId(),
                    "任务已提交，请使用任务ID查询结果"
                );
            }
            
        } catch (Exception e) {
            throw new ExtractionException("档案要素提取失败", e);
        }
    }
    
    /**
     * 自定义信息提取
     */
    public ExtractionResult extractCustom(File documentFile, 
                                        List<String> keyList, 
                                        Map<String, Object> options,
                                        boolean sync) {
        try {
            String url = serviceUrl + "/extract/upload";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", new FileSystemResource(documentFile));
            body.add("key_list", String.join(",", keyList));
            body.add("sync", sync);
            
            if (options != null && !options.isEmpty()) {
                body.add("options", objectMapper.writeValueAsString(options));
            }
            
            HttpEntity<MultiValueMap<String, Object>> requestEntity = 
                new HttpEntity<>(body, headers);
            
            ResponseEntity<ExtractionResponse> response = restTemplate.exchange(
                url, HttpMethod.POST, requestEntity, ExtractionResponse.class
            );
            
            return processExtractionResponse(response.getBody(), sync);
            
        } catch (Exception e) {
            throw new ExtractionException("自定义信息提取失败", e);
        }
    }
    
    /**
     * 获取任务状态
     */
    public TaskStatus getTaskStatus(String taskId) {
        try {
            String url = serviceUrl + "/task/" + taskId;
            
            ResponseEntity<TaskStatusResponse> response = restTemplate.getForEntity(
                url, TaskStatusResponse.class
            );
            
            TaskStatusResponse statusResponse = response.getBody();
            if (statusResponse == null) {
                throw new ExtractionException("无法获取任务状态");
            }
            
            return new TaskStatus(
                statusResponse.getTaskId(),
                statusResponse.getStatus(),
                statusResponse.getProgress(),
                statusResponse.getError()
            );
            
        } catch (Exception e) {
            throw new ExtractionException("获取任务状态失败", e);
        }
    }
    
    /**
     * 等待任务完成并获取结果
     */
    public ExtractionResult waitForResult(String taskId, int timeoutSeconds) {
        try {
            String url = serviceUrl + "/task/" + taskId + "/result?timeout=" + timeoutSeconds;
            
            ResponseEntity<ExtractionResponse> response = restTemplate.getForEntity(
                url, ExtractionResponse.class
            );
            
            return processExtractionResponse(response.getBody(), true);
            
        } catch (Exception e) {
            throw new ExtractionException("等待任务结果失败", e);
        }
    }
    
    /**
     * 取消任务
     */
    public boolean cancelTask(String taskId) {
        try {
            String url = serviceUrl + "/task/" + taskId;
            
            ResponseEntity<Map> response = restTemplate.exchange(
                url, HttpMethod.DELETE, null, Map.class
            );
            
            Map<String, Object> result = response.getBody();
            return result != null && Boolean.TRUE.equals(result.get("success"));
            
        } catch (Exception e) {
            throw new ExtractionException("取消任务失败", e);
        }
    }
    
    /**
     * 批量提取
     */
    public List<ExtractionResult> batchExtract(List<File> documentFiles, 
                                             List<String> keyList,
                                             boolean sync) {
        try {
            String url = serviceUrl + "/batch/extract";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            
            // 添加文件
            for (File file : documentFiles) {
                body.add("files", new FileSystemResource(file));
            }
            
            body.add("key_list", String.join(",", keyList));
            body.add("sync", sync);
            
            HttpEntity<MultiValueMap<String, Object>> requestEntity = 
                new HttpEntity<>(body, headers);
            
            ResponseEntity<ExtractionResponse[]> response = restTemplate.exchange(
                url, HttpMethod.POST, requestEntity, ExtractionResponse[].class
            );
            
            ExtractionResponse[] responses = response.getBody();
            if (responses == null) {
                throw new ExtractionException("批量提取响应为空");
            }
            
            List<ExtractionResult> results = new ArrayList<>();
            for (ExtractionResponse extractionResponse : responses) {
                results.add(processExtractionResponse(extractionResponse, sync));
            }
            
            return results;
            
        } catch (Exception e) {
            throw new ExtractionException("批量提取失败", e);
        }
    }
    
    /**
     * 获取系统状态
     */
    public SystemStatus getSystemStatus() {
        try {
            String url = serviceUrl + "/status";
            
            ResponseEntity<SystemStatusResponse> response = restTemplate.getForEntity(
                url, SystemStatusResponse.class
            );
            
            SystemStatusResponse statusResponse = response.getBody();
            if (statusResponse == null) {
                throw new ExtractionException("无法获取系统状态");
            }
            
            return new SystemStatus(
                statusResponse.getStatus(),
                statusResponse.getGpuInfo(),
                statusResponse.getModelStatus(),
                statusResponse.getPerformanceStats()
            );
            
        } catch (Exception e) {
            throw new ExtractionException("获取系统状态失败", e);
        }
    }
    
    /**
     * 健康检查
     */
    public boolean isHealthy() {
        try {
            String url = serviceUrl + "/health";
            
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            Map<String, Object> result = response.getBody();
            
            return result != null && "healthy".equals(result.get("status"));
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 异步提取档案要素
     */
    public CompletableFuture<ExtractionResult> extractArchiveElementsAsync(File documentFile) {
        return CompletableFuture.supplyAsync(() -> {
            // 提交异步任务
            ExtractionResult submitResult = extractArchiveElements(documentFile, null, false);
            String taskId = submitResult.getTaskId();
            
            // 轮询等待结果
            return pollForResult(taskId);
        });
    }
    
    /**
     * 轮询等待任务结果
     */
    private ExtractionResult pollForResult(String taskId) {
        int maxAttempts = timeoutMs / 1000; // 每秒检查一次
        
        for (int attempt = 0; attempt < maxAttempts; attempt++) {
            try {
                TaskStatus status = getTaskStatus(taskId);
                
                if ("completed".equals(status.getStatus())) {
                    return waitForResult(taskId, 5);
                } else if ("failed".equals(status.getStatus())) {
                    throw new ExtractionException("任务失败: " + status.getError());
                }
                
                Thread.sleep(1000); // 等待1秒
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new ExtractionException("任务被中断", e);
            }
        }
        
        throw new ExtractionException("任务超时");
    }
    
    /**
     * 处理提取响应
     */
    private ExtractionResult processExtractionResponse(ExtractionResponse response, boolean sync) {
        if (response == null) {
            throw new ExtractionException("服务响应为空");
        }
        
        if (!response.isSuccess()) {
            throw new ExtractionException("提取失败: " + response.getError());
        }
        
        if (sync) {
            return new ExtractionResult(
                true,
                response.getResults(),
                response.getProcessingTime()
            );
        } else {
            return new ExtractionResult(
                response.getTaskId(),
                "任务已提交"
            );
        }
    }
}
