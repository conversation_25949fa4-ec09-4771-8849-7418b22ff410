#!/usr/bin/env python3
"""
调试OCR识别问题
"""
import sys
import os
from pathlib import Path

# 添加项目路径
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

def debug_ocr_issue():
    """调试OCR识别问题"""
    try:
        print("=== 调试OCR识别问题 ===")
        
        # 检查uploads目录中的图像
        uploads_dir = Path("uploads")
        if not uploads_dir.exists():
            print("❌ uploads目录不存在")
            return False
        
        # 找到最新的图像文件
        image_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            image_files.extend(uploads_dir.glob(f"*{ext}"))
        
        if not image_files:
            print("❌ uploads目录中没有找到图像文件")
            return False
        
        # 选择最新的文件
        latest_image = max(image_files, key=lambda x: x.stat().st_mtime)
        print(f"✅ 找到最新图像: {latest_image}")
        
        # 检查图像基本信息
        try:
            import cv2
            img = cv2.imread(str(latest_image))
            if img is None:
                print(f"❌ 无法读取图像: {latest_image}")
                return False
            
            height, width = img.shape[:2]
            print(f"✅ 图像尺寸: {width}x{height}")
            
            # 检查图像是否过小或过大
            if width < 50 or height < 50:
                print("⚠️  图像尺寸过小，可能影响OCR识别")
            elif width > 4000 or height > 4000:
                print("⚠️  图像尺寸过大，可能影响OCR识别")
            
        except Exception as e:
            print(f"❌ 检查图像信息时出错: {e}")
        
        # 测试子进程OCR
        print(f"\n--- 测试子进程OCR ---")
        from src.models.ocr_subprocess import OCRSubprocessRunner
        
        runner = OCRSubprocessRunner(device_id=0)
        
        # 使用不同的置信度阈值测试
        thresholds = [0.1, 0.3, 0.5, 0.7]
        
        for threshold in thresholds:
            print(f"\n测试置信度阈值: {threshold}")
            
            result = runner.recognize(
                image_path=str(latest_image),
                confidence_threshold=threshold,
                use_cpu=True
            )
            
            if result.get('success'):
                results_count = len(result.get('results', []))
                print(f"  识别到 {results_count} 个文本块")
                
                if results_count > 0:
                    print("  文本内容:")
                    for i, item in enumerate(result.get('results', [])[:5]):  # 只显示前5个
                        print(f"    {i+1}. '{item['text']}' (置信度: {item['confidence']:.2f})")
                    
                    if results_count > 5:
                        print(f"    ... 还有 {results_count - 5} 个文本块")
                else:
                    print("  ⚠️  没有识别到文本")
            else:
                print(f"  ❌ OCR失败: {result.get('error')}")
        
        # 测试PPOCRv3Model
        print(f"\n--- 测试PPOCRv3Model ---")
        from src.models.ocr_models import PPOCRv3Model
        
        model = PPOCRv3Model(device_id=0)
        
        result = model.recognize(str(latest_image), confidence_threshold=0.1)
        
        print(f"PPOCRv3Model结果:")
        print(f"  识别到 {len(result.text_blocks)} 个文本块")
        print(f"  处理时间: {result.processing_time:.2f}秒")
        
        if result.text_blocks:
            print("  文本内容:")
            for i, block in enumerate(result.text_blocks[:5]):
                print(f"    {i+1}. '{block.text}' (置信度: {block.confidence:.2f})")
        else:
            print("  ⚠️  没有识别到文本")
        
        print(f"  完整文本: '{result.full_text}'")
        
        # 如果还是没有识别到文本，尝试直接调用worker脚本
        if len(result.text_blocks) == 0:
            print(f"\n--- 直接调用worker脚本调试 ---")
            import subprocess
            import json
            import tempfile
            
            # 创建配置文件
            config = {
                'image_path': str(latest_image.absolute()),
                'use_angle_cls': True,
                'lang': 'ch',
                'use_cpu': True,
                'gpu_id': 0,
                'confidence_threshold': 0.1
            }
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False)
                config_file = f.name
            
            try:
                worker_script = Path("src/models/ocr_worker.py")
                cmd = [sys.executable, str(worker_script), config_file]
                
                print(f"执行命令: {' '.join(cmd)}")
                
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    errors='ignore',
                    timeout=120
                )
                
                print(f"返回码: {result.returncode}")
                print(f"标准输出: {result.stdout}")
                print(f"标准错误: {result.stderr}")
                
            finally:
                try:
                    os.unlink(config_file)
                except:
                    pass
        
        return True
        
    except Exception as e:
        print(f"❌ 调试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始调试OCR识别问题...")
    success = debug_ocr_issue()
    
    if success:
        print("\n✅ 调试完成!")
    else:
        print("\n❌ 调试失败")
