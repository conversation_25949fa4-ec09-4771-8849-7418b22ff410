# 单GPU配置 - 适用于6GB显存环境
device:
  force_mode: "single"  # 强制单GPU模式
  min_memory_per_gpu: 6144  # 6GB

models:
  load_strategy: "dynamic"  # 动态加载，节省显存
  
  ocr:
    model_name: "PPOCRv3"
    memory_usage: 800
    config:
      det_model_dir: null
      rec_model_dir: null
      use_angle_cls: true
      lang: "ch"
  
  structure:
    model_name: "PP-Structure"
    memory_usage: 500
    config:
      layout_model_dir: null
      table_model_dir: null
  
  table:
    model_name: "PP-Table"
    memory_usage: 400
    config:
      table_model_dir: null
  
  embedding:
    model_name: "bge-small-zh-v1.5"  # 小模型，节省显存
    memory_usage: 400
    config:
      model_path: null
      max_length: 512
      batch_size: 4  # 小批量
  
  llm:
    model_name: "Qwen2.5-3B-Instruct"  # 3B模型适配6GB
    memory_usage: 4500
    config:
      model_path: null
      max_length: 2048
      temperature: 0.1
      top_p: 0.9
      do_sample: true

pipeline:
  max_workers: 1  # 单线程，避免显存冲突
  timeout: 300
  enable_cache: true
  cache_size: 50  # 减少缓存

extraction:
  default_options:
    confidence_threshold: 0.7
    max_retries: 2
    enable_multi_strategy: true
  
  archive:
    fusion_rules:
      题名:
        preferred_regions: ["title", "header"]
        position_weights:
          top: 0.8
          center: 0.6
        semantic_keywords: ["题名", "标题", "名称"]
      
      责任者:
        preferred_regions: ["text", "signature"]
        position_weights:
          bottom: 0.8
          right: 0.6
        semantic_keywords: ["单位", "机构", "责任者", "发文"]
      
      文号:
        preferred_regions: ["text", "header"]
        pattern_matching: "[\w\d]+号|[\w\d]+字"
        semantic_keywords: ["文号", "编号", "字号"]
      
      发文日期:
        preferred_regions: ["text", "footer"]
        pattern_matching: "\d{4}[-年]\d{1,2}[-月]\d{1,2}[日]?"
        semantic_keywords: ["日期", "时间", "发文日期"]

system:
  auto_initialize: true
  max_file_size: 52428800  # 50MB
  supported_formats:
    - ".jpg"
    - ".jpeg"
    - ".png"
    - ".bmp"
    - ".tiff"
    - ".pdf"
    - ".doc"
    - ".docx"
  log_level: "INFO"
  enable_performance_monitoring: true
