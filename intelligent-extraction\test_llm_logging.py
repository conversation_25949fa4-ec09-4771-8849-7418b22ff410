#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LLM模块的日志输出
"""
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_llm_logging():
    """测试LLM模块日志"""
    print("=" * 50)
    print("测试LLM模块日志输出")
    print("=" * 50)
    
    # 1. 初始化日志配置
    from src.utils.logger_config import setup_logging
    setup_logging(level="INFO", force_reconfigure=True)
    
    # 2. 创建LLM模型实例（模拟，不实际连接）
    import logging
    logger = logging.getLogger('src.models.llm_models')
    
    print("\n测试不同级别的日志输出:")
    
    # 测试INFO级别
    print("1. 测试INFO级别:")
    logger.info("这是INFO级别的测试消息")
    logger.info("Ollama服务连接成功: http://localhost:11434")
    logger.info("模型 qwen3:4b 已可用")
    
    # 测试WARNING级别
    print("\n2. 测试WARNING级别:")
    logger.warning("这是WARNING级别的测试消息")
    logger.warning("模型 test-model 未安装，尝试拉取...")
    
    # 测试ERROR级别
    print("\n3. 测试ERROR级别:")
    logger.error("这是ERROR级别的测试消息")
    logger.error("文本生成失败: 测试错误")
    
    # 测试DEBUG级别（应该不显示，因为当前级别是INFO）
    print("\n4. 测试DEBUG级别（应该不显示）:")
    logger.debug("这是DEBUG级别的测试消息，应该不会显示")
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("如果您看到了上面的INFO和WARNING消息，说明日志配置正常")
    print("如果只看到ERROR消息，说明存在日志级别问题")
    print("=" * 50)

if __name__ == "__main__":
    test_llm_logging()
