package com.toolbox.extraction.controller;

import com.toolbox.extraction.model.*;
import com.toolbox.extraction.service.ArchiveExtractionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 档案要素提取控制器
 * 为前端和其他系统提供档案信息提取接口
 */
@Slf4j
@RestController
@RequestMapping("/api/archive/extraction")
@Tag(name = "档案要素提取", description = "档案文档信息提取相关接口")
public class ArchiveExtractionController {
    
    @Autowired
    private ArchiveExtractionService extractionService;
    
    /**
     * 提取档案要素
     */
    @PostMapping("/elements")
    @Operation(summary = "提取档案要素", description = "从上传的档案文档中提取题名、责任者、文号、发文日期等要素")
    public ResponseEntity<ApiResponse<ArchiveElements>> extractArchiveElements(
            @Parameter(description = "档案文档文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        try {
            log.info("开始提取档案要素，文件名: {}", file.getOriginalFilename());
            
            ArchiveElements elements = extractionService.extractArchiveElements(file);
            
            // 验证提取结果
            ArchiveValidationResult validation = extractionService.validateArchiveElements(elements);
            
            ApiResponse<ArchiveElements> response = ApiResponse.success(elements);
            
            if (validation.hasWarnings()) {
                response.setWarnings(validation.getWarnings());
            }
            
            if (validation.hasErrors()) {
                response.setErrors(validation.getErrors());
                response.setMessage("档案要素提取完成，但存在验证错误");
            } else {
                response.setMessage("档案要素提取成功");
            }
            
            log.info("档案要素提取完成: {}", elements);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("档案要素提取失败", e);
            return ResponseEntity.ok(ApiResponse.error("档案要素提取失败: " + e.getMessage()));
        }
    }
    
    /**
     * 异步提取档案要素
     */
    @PostMapping("/elements/async")
    @Operation(summary = "异步提取档案要素", description = "异步提取档案要素，返回任务ID")
    public ResponseEntity<ApiResponse<String>> extractArchiveElementsAsync(
            @RequestParam("file") MultipartFile file) {
        
        try {
            log.info("开始异步提取档案要素，文件名: {}", file.getOriginalFilename());
            
            CompletableFuture<ArchiveElements> future = extractionService.extractArchiveElementsAsync(file);
            
            // 这里简化处理，实际应该返回任务ID并提供查询接口
            String taskId = "task_" + System.currentTimeMillis();
            
            // 异步处理结果（实际应该存储到缓存或数据库）
            future.whenComplete((result, throwable) -> {
                if (throwable != null) {
                    log.error("异步档案要素提取失败", throwable);
                } else {
                    log.info("异步档案要素提取完成: {}", result);
                }
            });
            
            return ResponseEntity.ok(ApiResponse.success(taskId, "异步任务已提交"));
            
        } catch (Exception e) {
            log.error("异步档案要素提取提交失败", e);
            return ResponseEntity.ok(ApiResponse.error("异步任务提交失败: " + e.getMessage()));
        }
    }
    
    /**
     * 批量提取档案要素
     */
    @PostMapping("/elements/batch")
    @Operation(summary = "批量提取档案要素", description = "批量提取多个档案文档的要素")
    public ResponseEntity<ApiResponse<List<ArchiveElements>>> batchExtractArchiveElements(
            @RequestParam("files") List<MultipartFile> files) {
        
        try {
            log.info("开始批量提取档案要素，文件数量: {}", files.size());
            
            if (files.size() > 10) {
                return ResponseEntity.ok(ApiResponse.error("批量处理文件数量不能超过10个"));
            }
            
            List<ArchiveElements> elementsList = extractionService.batchExtractArchiveElements(files);
            
            log.info("批量档案要素提取完成，成功数量: {}", elementsList.size());
            return ResponseEntity.ok(ApiResponse.success(elementsList, "批量提取完成"));
            
        } catch (Exception e) {
            log.error("批量档案要素提取失败", e);
            return ResponseEntity.ok(ApiResponse.error("批量提取失败: " + e.getMessage()));
        }
    }
    
    /**
     * 自定义信息提取
     */
    @PostMapping("/custom")
    @Operation(summary = "自定义信息提取", description = "根据指定的关键词列表提取信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> extractCustomInformation(
            @RequestParam("file") MultipartFile file,
            @RequestParam("keyList") String keyListStr,
            @RequestParam(value = "confidenceThreshold", defaultValue = "0.7") Double confidenceThreshold,
            @RequestParam(value = "maxRetries", defaultValue = "2") Integer maxRetries) {
        
        try {
            List<String> keyList = Arrays.asList(keyListStr.split(","));
            
            ExtractionOptions options = new ExtractionOptions();
            options.setConfidenceThreshold(confidenceThreshold);
            options.setMaxRetries(maxRetries);
            
            Map<String, Object> results = extractionService.extractCustomInformation(file, keyList, options);
            
            return ResponseEntity.ok(ApiResponse.success(results, "自定义信息提取成功"));
            
        } catch (Exception e) {
            log.error("自定义信息提取失败", e);
            return ResponseEntity.ok(ApiResponse.error("自定义信息提取失败: " + e.getMessage()));
        }
    }
    
    /**
     * 验证档案要素
     */
    @PostMapping("/validate")
    @Operation(summary = "验证档案要素", description = "验证档案要素的完整性和格式")
    public ResponseEntity<ApiResponse<ArchiveValidationResult>> validateArchiveElements(
            @RequestBody ArchiveElements elements) {
        
        try {
            ArchiveValidationResult validation = extractionService.validateArchiveElements(elements);
            
            String message = validation.isValid() ? "验证通过" : "验证失败";
            return ResponseEntity.ok(ApiResponse.success(validation, message));
            
        } catch (Exception e) {
            log.error("档案要素验证失败", e);
            return ResponseEntity.ok(ApiResponse.error("验证失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取提取服务状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取提取服务状态", description = "获取AI提取服务的运行状态")
    public ResponseEntity<ApiResponse<SystemStatus>> getExtractionServiceStatus() {
        
        try {
            SystemStatus status = extractionService.getExtractionServiceStatus();
            
            String message = status.isHealthy() ? "服务运行正常" : "服务状态异常";
            return ResponseEntity.ok(ApiResponse.success(status, message));
            
        } catch (Exception e) {
            log.error("获取提取服务状态失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取服务状态失败: " + e.getMessage()));
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查档案提取服务是否正常运行")
    public ResponseEntity<ApiResponse<Boolean>> healthCheck() {
        
        try {
            boolean healthy = extractionService.isExtractionServiceHealthy();
            
            String message = healthy ? "服务健康" : "服务不可用";
            return ResponseEntity.ok(ApiResponse.success(healthy, message));
            
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return ResponseEntity.ok(ApiResponse.success(false, "健康检查失败"));
        }
    }
    
    /**
     * 获取支持的文件格式
     */
    @GetMapping("/supported-formats")
    @Operation(summary = "获取支持的文件格式", description = "获取系统支持的档案文档格式列表")
    public ResponseEntity<ApiResponse<List<String>>> getSupportedFormats() {
        
        List<String> formats = Arrays.asList(
            ".jpg", ".jpeg", ".png", ".bmp", ".tiff",
            ".pdf", ".doc", ".docx"
        );
        
        return ResponseEntity.ok(ApiResponse.success(formats, "支持的文件格式"));
    }
}

/**
 * 统一API响应格式
 */
@Data
class ApiResponse<T> {
    private boolean success;
    private String message;
    private T data;
    private Map<String, String> errors;
    private Map<String, String> warnings;
    private long timestamp;
    
    public ApiResponse() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setSuccess(true);
        response.setData(data);
        return response;
    }
    
    public static <T> ApiResponse<T> success(T data, String message) {
        ApiResponse<T> response = success(data);
        response.setMessage(message);
        return response;
    }
    
    public static <T> ApiResponse<T> error(String message) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }
}
