#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
印章处理功能测试脚本
"""
import sys
import os
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.config_manager import ConfigManager
from core.model_pool import get_model_pool
from utils.image_preprocessor import get_image_processor
from PIL import Image
import time

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_stamp_processing_config():
    """测试印章处理配置"""
    print("🔧 测试印章处理配置...")
    
    config_manager = ConfigManager()
    config = config_manager.get_config()
    
    stamp_config = config.get('image_preprocessing', {}).get('stamp_processing', {})
    print(f"印章处理配置: {stamp_config}")
    
    if stamp_config.get('enabled', False):
        print("✅ 印章处理功能已启用")
    else:
        print("❌ 印章处理功能未启用")
    
    return stamp_config

def test_seal_detection_model():
    """测试印章检测模型加载"""
    print("\n🤖 测试印章检测模型...")
    
    try:
        # 创建模型池
        model_pool = get_model_pool()
        model_pool.initialize()
        
        # 获取印章检测模型
        seal_model = model_pool.get_seal_detection_model()
        
        if seal_model:
            print("✅ 印章检测模型获取成功")
            print(f"模型名称: {seal_model.model_name}")
            print(f"模型已加载: {seal_model.is_loaded}")
            return seal_model
        else:
            print("❌ 印章检测模型获取失败")
            return None
            
    except Exception as e:
        print(f"❌ 印章检测模型测试失败: {e}")
        return None

def test_image_processor_with_stamps():
    """测试带印章处理的图像预处理器"""
    print("\n🖼️ 测试图像预处理器...")
    
    try:
        # 启用印章处理的配置
        config = {
            'target_long_side': 800,
            'stamp_processing': {
                'enabled': True,
                'model_name': 'PP-OCRv4_mobile_seal_det',
                'confidence_threshold': 0.8,
                'enhancement_methods': ['redblue_removal'],
                'create_multiple_versions': True,
            }
        }
        
        # 创建模型池
        model_pool = get_model_pool()
        model_pool.initialize()
        
        # 创建图像处理器
        processor = get_image_processor(config, model_pool, force_reinit=True)
        
        print("✅ 图像处理器创建成功")
        print(f"印章检测器初始化状态: {processor._seal_detector_initialized}")
        
        return processor
        
    except Exception as e:
        print(f"❌ 图像处理器测试失败: {e}")
        return None

def test_stamp_processing_with_sample():
    """使用示例图像测试印章处理"""
    print("\n📄 测试印章处理功能...")
    
    # 查找测试图像
    test_images = [
        "test_image.png",
        "test_image.jpg", 
        "sample.jpg",
        "sample.png"
    ]
    
    test_image_path = None
    for img_path in test_images:
        if Path(img_path).exists():
            test_image_path = img_path
            break
    
    if not test_image_path:
        print("❌ 未找到测试图像，跳过印章处理测试")
        print(f"请在当前目录放置以下任一测试图像: {test_images}")
        return
    
    print(f"📸 使用测试图像: {test_image_path}")
    
    try:
        # 启用印章处理的配置
        config = {
            'target_long_side': 800,
            'stamp_processing': {
                'enabled': True,
                'model_name': 'PP-OCRv4_mobile_seal_det',
                'confidence_threshold': 0.8,
                'enhancement_methods': ['redblue_removal'],
                'create_multiple_versions': True,
            }
        }
        
        # 创建模型池和图像处理器
        model_pool = get_model_pool()
        model_pool.initialize()
        processor = get_image_processor(config, model_pool, force_reinit=True)
        
        # 处理图像
        start_time = time.time()
        processed_data, stats = processor.process_image(test_image_path)
        processing_time = time.time() - start_time
        
        print(f"✅ 图像处理完成")
        print(f"处理时间: {processing_time:.2f}秒")
        print(f"处理后大小: {len(processed_data)} bytes")
        
        # 检查是否有印章处理统计
        if 'stamp_processing' in stats:
            stamp_stats = stats['stamp_processing']
            print(f"印章处理统计: {stamp_stats}")
        else:
            print("未检测到印章处理统计信息")
        
        # 保存处理后的图像
        output_path = f"processed_{Path(test_image_path).name}"
        with open(output_path, 'wb') as f:
            f.write(processed_data)
        print(f"处理后图像已保存: {output_path}")
        
    except Exception as e:
        print(f"❌ 印章处理测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始印章处理功能测试")
    print("=" * 50)
    
    setup_logging()
    
    # 1. 测试配置
    stamp_config = test_stamp_processing_config()
    
    # 2. 测试模型加载
    seal_model = test_seal_detection_model()
    
    # 3. 测试图像处理器
    processor = test_image_processor_with_stamps()
    
    # 4. 测试完整流程
    test_stamp_processing_with_sample()
    
    print("\n" + "=" * 50)
    print("🏁 印章处理功能测试完成")

if __name__ == "__main__":
    main()
