"""
LLM模型封装 - 支持Ollama本地部署
"""
import json
import logging
import requests
import time
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass

@dataclass
class LLMResponse:
    """LLM响应结果"""
    content: str
    usage: Dict[str, int]
    model: str
    finish_reason: str
    response_time: float

class OllamaLLMModel:
    """Ollama LLM模型封装"""
    
    def __init__(self,
                 model_name: str = "qwen3:4b",
                 base_url: str = "http://localhost:11434",
                 device_id: int = 0,
                 **kwargs):
        """
        初始化Ollama LLM模型
        
        Args:
            model_name: 模型名称，如 "qwen3:4b", "qwen3:8b"
            base_url: Ollama服务地址
            device_id: GPU设备ID（Ollama会自动管理）
            **kwargs: 其他配置参数
        """
        self.model_name = model_name
        self.base_url = base_url.rstrip('/')
        self.device_id = device_id
        self.logger = logging.getLogger(__name__)
        
        # 默认生成参数
        self.default_params = {
            "temperature": kwargs.get("temperature", 0.1),
            "top_p": kwargs.get("top_p", 0.9),
            "top_k": kwargs.get("top_k", 40),
            "repeat_penalty": kwargs.get("repeat_penalty", 1.1),
            "num_predict": kwargs.get("max_tokens", 2048),
        }

        # Thinking mode控制（针对Qwen3等支持thinking的模型）
        # self.enable_thinking = kwargs.get("enable_thinking", False)
        
        # 连接超时设置
        self.timeout = kwargs.get("timeout", 60)
        
        # 验证连接
        self._check_connection()
        self._ensure_model_available()
    
    def _check_connection(self):
        """检查Ollama服务连接"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            response.raise_for_status()
            self.logger.info(f"Ollama服务连接成功: {self.base_url}")
        except Exception as e:
            self.logger.error(f"Ollama服务连接失败: {e}")
            raise ConnectionError(f"无法连接到Ollama服务 {self.base_url}: {e}")
    
    def _ensure_model_available(self):
        """确保模型可用"""
        try:
            # 获取已安装的模型列表
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            response.raise_for_status()
            
            models_data = response.json()
            available_models = [model['name'] for model in models_data.get('models', [])]
            
            if self.model_name not in available_models:
                self.logger.warning(f"模型 {self.model_name} 未安装，尝试拉取...")
                self._pull_model()
            else:
                self.logger.info(f"模型 {self.model_name} 已可用")
                
        except Exception as e:
            self.logger.error(f"检查模型可用性失败: {e}")
            raise
    
    def _pull_model(self):
        """拉取模型"""
        try:
            self.logger.info(f"开始拉取模型: {self.model_name}")
            
            pull_data = {"name": self.model_name}
            response = requests.post(
                f"{self.base_url}/api/pull",
                json=pull_data,
                timeout=600,  # 拉取模型可能需要较长时间
                stream=True
            )
            response.raise_for_status()
            
            # 处理流式响应
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line)
                        if 'status' in data:
                            self.logger.info(f"拉取进度: {data['status']}")
                        if data.get('status') == 'success':
                            self.logger.info(f"模型 {self.model_name} 拉取成功")
                            return
                    except json.JSONDecodeError:
                        continue
                        
        except Exception as e:
            self.logger.error(f"拉取模型失败: {e}")
            raise RuntimeError(f"无法拉取模型 {self.model_name}: {e}")
    
    def generate(self, 
                 prompt: str, 
                 system_prompt: Optional[str] = None,
                 **kwargs) -> LLMResponse:
        """
        生成文本
        
        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词
            **kwargs: 生成参数覆盖
            
        Returns:
            LLMResponse: 生成结果
        """
        start_time = time.time()
        
        try:
            # 构建完整的提示词
            full_prompt = ""
            if system_prompt:
                full_prompt = f"System: {system_prompt}\n\nUser: {prompt}"
            else:
                full_prompt = prompt

            # 控制thinking mode - 确保关闭以获得直接回答, Ollama服务只需要在promp最后接上/no_think
            enable_thinking = kwargs.get('enable_thinking')
            self.logger.info(f"Thinking mode设置: {enable_thinking}")

            if enable_thinking is False:
                full_prompt = full_prompt + "/no_think"
                self.logger.info("已禁用thinking mode，添加/no_think后缀")

            # 合并生成参数
            params = {**self.default_params, **kwargs}

            # 构建请求数据 - 使用/api/generate格式
            request_data = {
                "model": self.model_name,
                "prompt": full_prompt,
                "stream": False,
                "options": params
            }

            # 发送请求
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=request_data,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            response_time = time.time() - start_time

            # 调试：打印响应结构
            self.logger.debug(f"Ollama响应结构: {list(result.keys())}")

            # 解析/api/generate端点的响应格式
            content = ""
            if 'response' in result:
                # /api/generate端点的标准响应格式
                content = result['response']
            elif 'message' in result and 'content' in result['message']:
                # /api/chat端点的响应格式（备用）
                content = result['message']['content']
            elif 'content' in result:
                # 其他可能的格式
                content = result['content']
            else:
                self.logger.error(f"无法解析响应内容，响应结构: {result}")
                raise ValueError(f"无法解析Ollama响应: {result}")

            return LLMResponse(
                content=content,
                usage={
                    "prompt_tokens": result.get('prompt_eval_count', 0),
                    "completion_tokens": result.get('eval_count', 0),
                    "total_tokens": result.get('prompt_eval_count', 0) + result.get('eval_count', 0)
                },
                model=self.model_name,
                finish_reason=result.get('done_reason', 'stop'),
                response_time=response_time
            )
            
        except Exception as e:
            self.logger.error(f"文本生成失败: {e}")
            raise RuntimeError(f"LLM生成失败: {e}")
    
    def extract_archive_elements(self, 
                                ocr_text: str, 
                                target_fields: List[str],
                                context: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
        """
        提取档案要素
        
        Args:
            ocr_text: OCR识别的文本
            target_fields: 目标字段列表 ["题名", "责任者", "文号", "发文日期"]
            context: 额外上下文信息
            
        Returns:
            Dict[str, str]: 提取结果
        """
        # 构建系统提示词
        system_prompt = """你是一个专业的档案信息提取专家。请从给定的OCR文本中准确提取指定的档案要素。

提取规则：
1. 题名：文档的标题或主题名称，通常在文档顶部
2. 责任者：发文单位、机构或个人，通常在文档末尾或特定位置
3. 文号：文档编号，通常包含"号"、"字"等标识
4. 发文日期：文档的发文或成文日期，格式为YYYY-MM-DD或YYYY年MM月DD日

要求：
- 只提取明确存在的信息，不要推测或编造
- 如果某个字段无法确定，返回空字符串
- 保持原文的准确性，不要修改或美化
- 返回JSON格式，字段名使用中文"""
        
        # 构建用户提示词
        fields_str = "、".join(target_fields)
        user_prompt = f"""请从以下OCR文本中提取档案要素：{fields_str}

OCR文本：
{ocr_text}

请返回JSON格式的结果，例如：
{{
    "题名": "提取到的标题",
    "责任者": "提取到的责任者",
    "文号": "提取到的文号", 
    "发文日期": "提取到的日期"
}}"""
        
        try:
            # 调用LLM生成
            response = self.generate(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.1,  # 降低随机性，提高准确性
                num_predict=1024   # 限制输出长度
            )
            
            # 解析JSON结果
            content = response.content.strip()
            
            # 尝试提取JSON部分
            if '```json' in content:
                json_start = content.find('```json') + 7
                json_end = content.find('```', json_start)
                content = content[json_start:json_end].strip()
            elif '{' in content and '}' in content:
                json_start = content.find('{')
                json_end = content.rfind('}') + 1
                content = content[json_start:json_end]
            
            try:
                result = json.loads(content)
                
                # 确保所有目标字段都存在
                extracted = {}
                for field in target_fields:
                    extracted[field] = result.get(field, "")
                
                self.logger.info(f"档案要素提取成功: {extracted}")
                return extracted
                
            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析失败: {e}, 原始内容: {content}")
                # 返回空结果
                return {field: "" for field in target_fields}
                
        except Exception as e:
            self.logger.error(f"档案要素提取失败: {e}")
            return {field: "" for field in target_fields}
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        try:
            response = requests.post(
                f"{self.base_url}/api/show",
                json={"name": self.model_name},
                timeout=10
            )
            response.raise_for_status()
            
            model_info = response.json()
            return {
                "name": self.model_name,
                "size": model_info.get('size', 0),
                "parameters": model_info.get('details', {}).get('parameter_size', 'unknown'),
                "family": model_info.get('details', {}).get('family', 'unknown'),
                "format": model_info.get('details', {}).get('format', 'unknown'),
                "modified_at": model_info.get('modified_at', ''),
            }
            
        except Exception as e:
            self.logger.error(f"获取模型信息失败: {e}")
            return {"name": self.model_name, "error": str(e)}
    
    def cleanup(self):
        """清理资源"""
        # Ollama模型由服务端管理，客户端无需特殊清理
        self.logger.info(f"Ollama LLM模型 {self.model_name} 清理完成")
    
    def __str__(self) -> str:
        return f"OllamaLLMModel(model={self.model_name}, url={self.base_url})"


class QwenLLMModel(OllamaLLMModel):
    """Qwen模型的特化版本"""
    
    def __init__(self, 
                 model_size: str = "7b",
                 base_url: str = "http://localhost:11434",
                 device_id: int = 0,
                 **kwargs):
        """
        初始化Qwen模型
        
        Args:
            model_size: 模型大小 "0.6b", "1.7b", "4b", "8b", "14b", "32b"
            base_url: Ollama服务地址
            device_id: GPU设备ID
            **kwargs: 其他配置参数
        """
        model_name = f"qwen3:{model_size}"

        # 对于Qwen3，默认关闭thinking mode以获得直接回答
        # kwargs.setdefault("enable_thinking", False)

        super().__init__(model_name, base_url, device_id, **kwargs)

        # Qwen特定的默认参数
        self.default_params.update({
            "temperature": 0.1,
            "top_p": 0.9,
            "repeat_penalty": 1.05,
        })
