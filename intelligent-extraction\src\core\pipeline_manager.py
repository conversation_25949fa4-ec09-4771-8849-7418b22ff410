"""
流水线管理器 - 协调整个信息提取流程
"""
import time
import logging
import threading
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

@dataclass
class ExtractionTask:
    """提取任务"""
    task_id: str
    document_path: str
    key_list: List[str]
    options: Dict[str, Any]
    status: str = "pending"
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None

class PipelineManager:
    """流水线管理器"""
    
    def __init__(self, device_manager, model_manager, config: Dict[str, Any]):
        self.device_manager = device_manager
        self.model_manager = model_manager
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 处理器
        self.processors = {}
        self._initialize_processors()
        
        # 任务管理
        self.active_tasks = {}
        self.task_lock = threading.Lock()
        
        # 线程池
        max_workers = config.get('max_workers', 2)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 性能统计
        self.performance_stats = {
            'total_tasks': 0,
            'successful_tasks': 0,
            'failed_tasks': 0,
            'average_processing_time': 0.0
        }
    
    def _initialize_processors(self):
        """初始化处理器"""
        from ..processors.document_processor import DocumentProcessor
        from ..processors.vector_processor import VectorProcessor
        from ..processors.fusion_processor import FusionProcessor
        from ..processors.extraction_processor import ExtractionProcessor
        
        self.processors = {
            'document': DocumentProcessor(self.device_manager, self.model_manager),
            'vector': VectorProcessor(self.device_manager, self.model_manager),
            'fusion': FusionProcessor(self.device_manager, self.model_manager),
            'extraction': ExtractionProcessor(self.device_manager, self.model_manager)
        }
        
        self.logger.info("处理器初始化完成")
    
    def extract_async(self, document_path: str, key_list: List[str], 
                     options: Optional[Dict[str, Any]] = None) -> str:
        """异步提取"""
        task_id = f"task_{int(time.time() * 1000)}"
        
        if options is None:
            options = {}
        
        task = ExtractionTask(
            task_id=task_id,
            document_path=document_path,
            key_list=key_list,
            options=options
        )
        
        with self.task_lock:
            self.active_tasks[task_id] = task
        
        # 提交到线程池
        future = self.executor.submit(self._process_task, task)
        
        self.logger.info(f"提交异步任务: {task_id}")
        return task_id
    
    def extract_sync(self, document_path: str, key_list: List[str], 
                    options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """同步提取"""
        task_id = self.extract_async(document_path, key_list, options)
        return self.wait_for_result(task_id)
    
    def _process_task(self, task: ExtractionTask) -> Dict[str, Any]:
        """处理单个任务"""
        task.status = "processing"
        task.start_time = time.time()
        
        try:
            self.logger.info(f"开始处理任务: {task.task_id}")
            
            # 根据GPU模式选择处理策略
            if self.device_manager.is_dual_gpu_mode():
                result = self._process_dual_gpu(task)
            else:
                result = self._process_single_gpu(task)
            
            task.result = result
            task.status = "completed"
            task.end_time = time.time()
            
            # 更新统计信息
            self._update_performance_stats(task, success=True)
            
            self.logger.info(f"任务完成: {task.task_id}, 耗时: {task.end_time - task.start_time:.2f}s")
            return result
            
        except Exception as e:
            task.error = str(e)
            task.status = "failed"
            task.end_time = time.time()
            
            self._update_performance_stats(task, success=False)
            
            self.logger.error(f"任务失败: {task.task_id}, 错误: {e}")
            raise
    
    def _process_dual_gpu(self, task: ExtractionTask) -> Dict[str, Any]:
        """双GPU处理流程"""
        self.logger.info(f"使用双GPU模式处理任务: {task.task_id}")
        
        # 阶段1: GPU0进行文档解析
        with self.device_manager.get_device_context('document_processing'):
            # 文档解析
            structured_info = self.processors['document'].process(
                task.document_path, task.options
            )
            
            # 向量构建
            vector_db = self.processors['vector'].build_vectors(
                structured_info, task.options
            )
        
        # 阶段2: GPU1进行LLM推理
        results = {}
        with self.device_manager.get_device_context('llm_inference'):
            for key in task.key_list:
                # 语义检索
                relevant_context = self.processors['vector'].semantic_search(
                    key, vector_db, task.options
                )
                
                # 多模态融合
                fused_info = self.processors['fusion'].fuse_information(
                    key, structured_info, relevant_context, task.options
                )
                
                # LLM提取
                extracted_value = self.processors['extraction'].extract_with_llm(
                    key, fused_info, task.options
                )
                
                results[key] = extracted_value
        
        return {
            'results': results,
            'structured_info': structured_info,
            'processing_mode': 'dual_gpu',
            'task_id': task.task_id
        }
    
    def _process_single_gpu(self, task: ExtractionTask) -> Dict[str, Any]:
        """单GPU处理流程"""
        self.logger.info(f"使用单GPU模式处理任务: {task.task_id}")
        
        with self.device_manager.get_device_context('primary'):
            # 文档解析
            structured_info = self.processors['document'].process(
                task.document_path, task.options
            )
            
            # 向量构建
            vector_db = self.processors['vector'].build_vectors(
                structured_info, task.options
            )
            
            # 逐个提取（避免显存峰值）
            results = {}
            for key in task.key_list:
                # 语义检索
                relevant_context = self.processors['vector'].semantic_search(
                    key, vector_db, task.options
                )
                
                # 多模态融合
                fused_info = self.processors['fusion'].fuse_information(
                    key, structured_info, relevant_context, task.options
                )
                
                # LLM提取
                extracted_value = self.processors['extraction'].extract_with_llm(
                    key, fused_info, task.options
                )
                
                results[key] = extracted_value
                
                # 清理中间结果，释放显存
                if len(task.key_list) > 1:
                    self.device_manager.clear_cache()
        
        return {
            'results': results,
            'structured_info': structured_info,
            'processing_mode': 'single_gpu',
            'task_id': task.task_id
        }
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        with self.task_lock:
            if task_id not in self.active_tasks:
                return {'error': 'Task not found'}
            
            task = self.active_tasks[task_id]
            
            status_info = {
                'task_id': task_id,
                'status': task.status,
                'document_path': task.document_path,
                'key_list': task.key_list
            }
            
            if task.start_time:
                status_info['start_time'] = task.start_time
                if task.end_time:
                    status_info['processing_time'] = task.end_time - task.start_time
                else:
                    status_info['elapsed_time'] = time.time() - task.start_time
            
            if task.error:
                status_info['error'] = task.error
            
            return status_info
    
    def wait_for_result(self, task_id: str, timeout: Optional[float] = None) -> Dict[str, Any]:
        """等待任务结果"""
        start_time = time.time()
        
        while True:
            with self.task_lock:
                if task_id not in self.active_tasks:
                    raise ValueError(f"任务不存在: {task_id}")
                
                task = self.active_tasks[task_id]
                
                if task.status == "completed":
                    return task.result
                elif task.status == "failed":
                    raise RuntimeError(f"任务失败: {task.error}")
            
            # 检查超时
            if timeout and (time.time() - start_time) > timeout:
                raise TimeoutError(f"任务超时: {task_id}")
            
            time.sleep(0.1)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self.task_lock:
            if task_id not in self.active_tasks:
                return False

            task = self.active_tasks[task_id]
            if task.status in ["completed", "failed"]:
                return False

            task.status = "cancelled"
            return True

    def process_multi_page_archive(self, main_image: str, auxiliary_images: List[str],
                                  key_list: List[str], options: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理多页档案（同步）

        Args:
            main_image: 主要图片文件路径
            auxiliary_images: 辅助图片文件路径列表
            key_list: 要提取的关键词列表
            options: 处理选项

        Returns:
            提取结果
        """
        self.logger.info(f"开始处理多页档案，主页面: {main_image}, 辅助页面: {len(auxiliary_images)}个")

        try:
            # 根据GPU模式选择处理策略
            if self.device_manager.is_dual_gpu_mode():
                return self._process_multi_page_dual_gpu(main_image, auxiliary_images, key_list, options)
            else:
                return self._process_multi_page_single_gpu(main_image, auxiliary_images, key_list, options)

        except Exception as e:
            self.logger.error(f"多页档案处理失败: {e}")
            raise

    def submit_multi_page_archive_task(self, image_files: List[str], key_list: List[str],
                                      options: Dict[str, Any]) -> str:
        """
        提交多页档案异步任务

        Args:
            image_files: 图片文件路径列表
            key_list: 要提取的关键词列表
            options: 处理选项

        Returns:
            任务ID
        """
        task_id = f"multi_page_task_{int(time.time() * 1000)}"

        # 创建多页档案任务
        task = ExtractionTask(
            task_id=task_id,
            document_path=image_files[0],  # 主文件
            key_list=key_list,
            options={**options, 'auxiliary_images': image_files[1:], 'is_multi_page': True}
        )

        with self.task_lock:
            self.active_tasks[task_id] = task

        # 提交到线程池
        future = self.executor.submit(self._process_multi_page_task, task)

        self.logger.info(f"提交多页档案异步任务: {task_id}")
        return task_id

    def _process_multi_page_task(self, task: ExtractionTask) -> Dict[str, Any]:
        """处理多页档案任务"""
        task.status = "processing"
        task.start_time = time.time()

        try:
            self.logger.info(f"开始处理多页档案任务: {task.task_id}")

            # 获取辅助图片
            auxiliary_images = task.options.get('auxiliary_images', [])

            # 处理多页档案
            result = self.process_multi_page_archive(
                task.document_path, auxiliary_images, task.key_list, task.options
            )

            task.result = result
            task.status = "completed"
            task.end_time = time.time()

            # 更新统计信息
            self._update_performance_stats(task, success=True)

            self.logger.info(f"多页档案任务完成: {task.task_id}, 耗时: {task.end_time - task.start_time:.2f}s")
            return result

        except Exception as e:
            task.error = str(e)
            task.status = "failed"
            task.end_time = time.time()

            self._update_performance_stats(task, success=False)

            self.logger.error(f"多页档案任务失败: {task.task_id}, 错误: {e}")
            raise

    def _process_multi_page_dual_gpu(self, main_image: str, auxiliary_images: List[str],
                                    key_list: List[str], options: Dict[str, Any]) -> Dict[str, Any]:
        """双GPU模式处理多页档案"""
        self.logger.info(f"使用双GPU模式处理多页档案")

        all_images = [main_image] + auxiliary_images
        all_structured_info = []
        all_vector_dbs = []

        # 阶段1: GPU0进行所有页面的文档解析
        with self.device_manager.get_device_context('document_processing'):
            for i, image_path in enumerate(all_images):
                self.logger.info(f"处理第 {i+1}/{len(all_images)} 页: {image_path}")

                # 文档解析
                structured_info = self.processors['document'].process(
                    image_path, {**options, 'page_index': i, 'is_main_page': (i == 0)}
                )
                all_structured_info.append(structured_info)

                # 向量构建
                page_vectors = self.processors['vector'].build_vectors(
                    structured_info, {**options, 'page_index': i}
                )
                all_vector_dbs.append(page_vectors)

        # 阶段2: GPU1进行LLM推理（基于所有页面的信息）
        results = {}
        with self.device_manager.get_device_context('llm_inference'):
            for key in key_list:
                # 在所有页面中搜索相关信息
                all_relevant_contexts = []
                for i, vector_db in enumerate(all_vector_dbs):
                    relevant_context = self.processors['vector'].semantic_search(
                        key, vector_db, {**options, 'page_index': i}
                    )
                    all_relevant_contexts.append(relevant_context)

                # 合并所有页面的结构化信息和相关上下文
                combined_structured_info = self._combine_structured_info(all_structured_info)
                combined_relevant_context = self._combine_relevant_contexts(all_relevant_contexts)

                # 多模态融合（使用现有的fuse_information方法）
                fused_info = self.processors['fusion'].fuse_information(
                    key, combined_structured_info, combined_relevant_context,
                    {**options, 'is_multi_page': True, 'page_count': len(all_images)}
                )

                # LLM提取
                extracted_value = self.processors['extraction'].extract_with_llm(
                    key, fused_info, {**options, 'is_multi_page': True}
                )

                results[key] = extracted_value

        return {
            'results': results,
            'structured_info': all_structured_info,
            'processing_mode': 'dual_gpu_multi_page',
            'page_count': len(all_images),
            'processed_files': [Path(img).name for img in all_images]
        }

    def _process_multi_page_single_gpu(self, main_image: str, auxiliary_images: List[str],
                                      key_list: List[str], options: Dict[str, Any]) -> Dict[str, Any]:
        """单GPU模式处理多页档案"""
        self.logger.info(f"使用单GPU模式处理多页档案")

        all_images = [main_image] + auxiliary_images
        all_structured_info = []
        all_vector_dbs = []

        with self.device_manager.get_device_context('primary'):
            # 逐页处理文档解析和向量构建
            for i, image_path in enumerate(all_images):
                self.logger.info(f"处理第 {i+1}/{len(all_images)} 页: {image_path}")

                # 文档解析
                structured_info = self.processors['document'].process(
                    image_path, {**options, 'page_index': i, 'is_main_page': (i == 0)}
                )
                all_structured_info.append(structured_info)

                # 向量构建
                page_vectors = self.processors['vector'].build_vectors(
                    structured_info, {**options, 'page_index': i}
                )
                all_vector_dbs.append(page_vectors)

                # 清理中间结果，释放显存
                if i < len(all_images) - 1:  # 不是最后一页
                    self.device_manager.clear_cache()

            # 基于所有页面信息进行LLM提取
            results = {}
            for key in key_list:
                # 在所有页面中搜索相关信息
                all_relevant_contexts = []
                for i, vector_db in enumerate(all_vector_dbs):
                    relevant_context = self.processors['vector'].semantic_search(
                        key, vector_db, {**options, 'page_index': i}
                    )
                    all_relevant_contexts.append(relevant_context)

                # 合并所有页面的结构化信息和相关上下文
                combined_structured_info = self._combine_structured_info(all_structured_info)
                combined_relevant_context = self._combine_relevant_contexts(all_relevant_contexts)

                # 多模态融合（使用现有的fuse_information方法）
                fused_info = self.processors['fusion'].fuse_information(
                    key, combined_structured_info, combined_relevant_context,
                    {**options, 'is_multi_page': True, 'page_count': len(all_images)}
                )

                # LLM提取
                extracted_value = self.processors['extraction'].extract_with_llm(
                    key, fused_info, {**options, 'is_multi_page': True}
                )

                results[key] = extracted_value

                # 清理中间结果，释放显存
                if len(key_list) > 1:
                    self.device_manager.clear_cache()

        return {
            'results': results,
            'structured_info': all_structured_info,
            'processing_mode': 'single_gpu_multi_page',
            'page_count': len(all_images),
            'processed_files': [Path(img).name for img in all_images]
        }

    def _combine_structured_info(self, all_structured_info: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合并多页的结构化信息"""
        if not all_structured_info:
            return {}

        # 如果只有一页，直接返回
        if len(all_structured_info) == 1:
            return all_structured_info[0]

        # 合并多页信息
        combined = {
            'pages': all_structured_info,
            'page_count': len(all_structured_info),
            'is_multi_page': True
        }

        # 合并文本内容（如果存在）
        all_texts = []
        for page_info in all_structured_info:
            if 'text' in page_info:
                all_texts.append(page_info['text'])

        if all_texts:
            combined['combined_text'] = '\n\n'.join(all_texts)

        # 合并OCR结果（如果存在）
        all_ocr_results = []
        for page_info in all_structured_info:
            if 'ocr_result' in page_info:
                all_ocr_results.extend(page_info['ocr_result'])

        if all_ocr_results:
            combined['combined_ocr_result'] = all_ocr_results

        return combined

    def _combine_relevant_contexts(self, all_relevant_contexts: List[Any]) -> Any:
        """合并多页的相关上下文"""
        if not all_relevant_contexts:
            return None

        # 如果只有一页，直接返回
        if len(all_relevant_contexts) == 1:
            return all_relevant_contexts[0]

        # 简单合并策略：将所有上下文组合
        # 具体的合并逻辑取决于相关上下文的数据结构
        return {
            'multi_page_contexts': all_relevant_contexts,
            'page_count': len(all_relevant_contexts),
            'is_multi_page': True
        }

    def _update_performance_stats(self, task: ExtractionTask, success: bool):
        """更新性能统计"""
        self.performance_stats['total_tasks'] += 1
        
        if success:
            self.performance_stats['successful_tasks'] += 1
        else:
            self.performance_stats['failed_tasks'] += 1
        
        if task.start_time and task.end_time:
            processing_time = task.end_time - task.start_time
            total_time = self.performance_stats['average_processing_time'] * (self.performance_stats['total_tasks'] - 1)
            self.performance_stats['average_processing_time'] = (total_time + processing_time) / self.performance_stats['total_tasks']
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = self.performance_stats.copy()
        stats['success_rate'] = (stats['successful_tasks'] / max(1, stats['total_tasks'])) * 100
        stats['active_tasks'] = len(self.active_tasks)
        stats['memory_usage'] = self.device_manager.monitor_memory_usage()
        return stats
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("开始清理流水线资源...")
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        # 清理处理器
        for processor in self.processors.values():
            if hasattr(processor, 'cleanup'):
                processor.cleanup()
        
        self.logger.info("流水线资源清理完成")
    
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except:
            pass
