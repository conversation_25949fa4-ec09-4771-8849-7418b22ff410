package com.deeppaas.vector.model;

import lombok.Data;
import java.awt.Rectangle;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 档案卷向量信息
 */
@Data
public class ArchiveVolumeVectors {
    private String volumeId;
    private List<VectorizedTextSegment> textSegments;
    private Map<String, Integer> vectorIndex; // segmentId -> index映射
    private LocalDateTime createdTime;
}


