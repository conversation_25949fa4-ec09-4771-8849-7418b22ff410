#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速单文档测试脚本
用于快速验证真实扫描文档的处理效果
"""
import asyncio
import aiohttp
import json
import time
import sys
from pathlib import Path
from PIL import Image

async def quick_test(image_path: str):
    """快速测试单个文档"""
    
    # 验证文件
    image_file = Path(image_path)
    if not image_file.exists():
        print(f"❌ 文件不存在: {image_path}")
        return False
    
    print(f"📄 测试文档: {image_file.name}")
    print("=" * 50)
    
    # 快速分析图像
    try:
        with Image.open(image_file) as img:
            width, height = img.size
            file_size = image_file.stat().st_size / (1024 * 1024)
            print(f"📊 图像信息: {width}×{height}, {file_size:.1f}MB, {img.format}")
    except Exception as e:
        print(f"⚠️ 图像分析失败: {e}")
    
    # 发送请求
    print(f"\n🚀 开始处理...")
    
    async with aiohttp.ClientSession() as session:
        try:
            data = aiohttp.FormData()
            data.add_field('file', 
                          open(image_file, 'rb'), 
                          filename=image_file.name)
            data.add_field('custom_keys', '题名,责任者,文号,发文日期')
            
            start_time = time.time()
            
            async with session.post("http://localhost:8080/extract/archive", 
                                  data=data) as response:
                
                total_time = time.time() - start_time
                
                if response.status == 200:
                    result = await response.json()
                    
                    print(f"✅ 处理完成 ({total_time:.1f}秒)")
                    
                    if result.get('success'):
                        elements = result.get('results', {})
                        
                        print(f"\n📋 提取结果:")
                        for key, value in elements.items():
                            status = "✅" if value and value != "null" else "❌"
                            print(f"   {status} {key}: {value}")
                        
                        # OCR统计
                        ocr_info = result.get('ocr_info', {})
                        if ocr_info:
                            print(f"\n📝 OCR统计: {ocr_info.get('total_blocks', 0)}个文本块")
                        
                        return True
                    else:
                        print(f"❌ 提取失败: {result.get('error')}")
                        return False
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP {response.status}: {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False

def main():
    if len(sys.argv) != 2:
        print("用法: python quick_test.py <图像路径>")
        print("示例: python quick_test.py C:\\Documents\\scan.jpg")
        return
    
    image_path = sys.argv[1]
    asyncio.run(quick_test(image_path))

if __name__ == "__main__":
    main()
