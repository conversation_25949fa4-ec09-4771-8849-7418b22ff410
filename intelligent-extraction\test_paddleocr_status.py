#!/usr/bin/env python3
"""
测试PaddleOCR和SealTextDetection的状态
"""
import os
import sys

def test_paddleocr_import():
    """测试PaddleOCR导入"""
    print("🔍 测试PaddleOCR导入...")
    try:
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR导入成功")
        return True
    except Exception as e:
        print(f"❌ PaddleOCR导入失败: {e}")
        return False

def test_seal_detection_import():
    """测试SealTextDetection导入"""
    print("🔍 测试SealTextDetection导入...")
    try:
        from paddleocr import SealTextDetection
        print("✅ SealTextDetection导入成功")
        return True
    except Exception as e:
        print(f"❌ SealTextDetection导入失败: {e}")
        return False

def test_paddleocr_init():
    """测试PaddleOCR初始化"""
    print("🔍 测试PaddleOCR初始化...")
    try:
        from paddleocr import PaddleOCR
        ocr = PaddleOCR(
            use_textline_orientation=True,
            lang='ch',
            device='cpu',
            show_log=False
        )
        print("✅ PaddleOCR初始化成功")
        return True, ocr
    except Exception as e:
        print(f"❌ PaddleOCR初始化失败: {e}")
        return False, None

def test_seal_detection_init():
    """测试SealTextDetection初始化"""
    print("🔍 测试SealTextDetection初始化...")
    try:
        from paddleocr import SealTextDetection
        seal_detector = SealTextDetection(
            model_name="PP-OCRv4_mobile_seal_det",
            device='cpu'
        )
        print("✅ SealTextDetection初始化成功")
        return True, seal_detector
    except Exception as e:
        print(f"❌ SealTextDetection初始化失败: {e}")
        return False, None

def test_ocr_with_image():
    """测试OCR处理图像"""
    print("🔍 测试OCR处理图像...")
    
    # 查找测试图像
    test_images = ["Image_00002.jpg", "test_medium_image.jpg"]
    test_image = None
    for img in test_images:
        if os.path.exists(img):
            test_image = img
            break
    
    if not test_image:
        print("❌ 找不到测试图像")
        return False
    
    try:
        success, ocr = test_paddleocr_init()
        if not success:
            return False
        
        print(f"📄 使用测试图像: {test_image}")
        results = ocr.predict(test_image)
        
        print(f"✅ OCR处理成功，结果类型: {type(results)}")
        if hasattr(results, '__len__'):
            print(f"📊 结果数量: {len(results)}")
        
        return True
    except Exception as e:
        print(f"❌ OCR处理失败: {e}")
        return False

def main():
    """主函数"""
    print("🔬 PaddleOCR状态检查")
    print("=" * 50)
    
    # 检查导入
    paddleocr_import = test_paddleocr_import()
    seal_import = test_seal_detection_import()
    
    print("\n" + "-" * 30)
    
    # 检查初始化
    if paddleocr_import:
        paddleocr_init, _ = test_paddleocr_init()
    else:
        paddleocr_init = False
    
    if seal_import:
        seal_init, _ = test_seal_detection_init()
    else:
        seal_init = False
    
    print("\n" + "-" * 30)
    
    # 检查OCR处理
    if paddleocr_init:
        ocr_processing = test_ocr_with_image()
    else:
        ocr_processing = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 检查结果总结:")
    print(f"  PaddleOCR导入: {'✅' if paddleocr_import else '❌'}")
    print(f"  SealTextDetection导入: {'✅' if seal_import else '❌'}")
    print(f"  PaddleOCR初始化: {'✅' if paddleocr_init else '❌'}")
    print(f"  SealTextDetection初始化: {'✅' if seal_init else '❌'}")
    print(f"  OCR图像处理: {'✅' if ocr_processing else '❌'}")
    
    if all([paddleocr_import, seal_import, paddleocr_init, seal_init, ocr_processing]):
        print("\n🎉 所有功能正常！")
        return True
    else:
        print("\n⚠️ 存在问题，需要修复")
        return False

if __name__ == "__main__":
    main()
