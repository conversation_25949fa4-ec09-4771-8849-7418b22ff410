<template>
  <div class="compliance-check-main">
    <!-- 检查配置区域 -->
    <el-card class="check-config-section" shadow="never">
      <div slot="header" class="section-header">
        <span>合规检查配置</span>
        <el-button type="primary" size="small" @click="startCheck" :loading="checking">
          <i class="el-icon-play"></i> 开始检查
        </el-button>
      </div>

      <!-- 规则选择 -->
      <div class="rule-selection">
        <h4>检查规则</h4>
        <el-checkbox-group v-model="selectedRules" @change="onRuleSelectionChange">
          <!-- 原有规则 -->
          <el-checkbox 
            v-for="rule in existingRules" 
            :key="rule.code"
            :label="rule.code"
            class="rule-checkbox"
          >
            <div class="rule-content">
              <span class="rule-name">{{ rule.name }}</span>
              <span class="rule-description">{{ rule.description }}</span>
            </div>
          </el-checkbox>
          
          <!-- 新增：条目内容正确性规则 -->
          <el-checkbox 
            label="CONTENT_ACCURACY"
            class="rule-checkbox content-accuracy-rule"
          >
            <div class="rule-content">
              <span class="rule-name">
                条目内容正确性
                <el-tag type="success" size="mini">AI智能</el-tag>
              </span>
              <span class="rule-description">支持题名、责任者、文号、成文日期</span>
            </div>
          </el-checkbox>
        </el-checkbox-group>
      </div>

      <!-- 条目内容正确性配置 -->
      <div v-if="selectedRules.includes('CONTENT_ACCURACY')" class="content-accuracy-config">
        <ContentAccuracyRuleConfig
          :rule-config="contentAccuracyConfig"
          :excel-columns="excelColumns"
          @config-change="onContentAccuracyConfigChange"
          @config-save="onContentAccuracyConfigSave"
        />
      </div>

      <!-- 文件上传区域 -->
      <div class="file-upload-section">
        <h4>上传文件</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="upload-item">
              <label>Excel数据文件</label>
              <el-upload
                ref="excelUpload"
                :auto-upload="false"
                :on-change="onExcelFileChange"
                :before-remove="beforeExcelRemove"
                accept=".xlsx,.xls"
                :limit="1"
              >
                <el-button size="small" type="primary">
                  <i class="el-icon-upload"></i> 选择Excel文件
                </el-button>
                <div slot="tip" class="el-upload__tip">
                  支持.xlsx、.xls格式，文件大小不超过10MB
                </div>
              </el-upload>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="upload-item">
              <label>档案文档文件</label>
              <el-upload
                ref="documentsUpload"
                :auto-upload="false"
                :on-change="onDocumentFilesChange"
                :before-remove="beforeDocumentRemove"
                accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                multiple
                :limit="100"
              >
                <el-button size="small" type="primary">
                  <i class="el-icon-folder-add"></i> 选择档案文件
                </el-button>
                <div slot="tip" class="el-upload__tip">
                  支持图片、PDF、Word格式，最多100个文件
                </div>
              </el-upload>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 检查进度 -->
    <el-card v-if="checkProgress.visible" class="check-progress-section" shadow="never">
      <div slot="header">
        <span>检查进度</span>
        <el-button type="text" @click="cancelCheck" v-if="checking">取消检查</el-button>
      </div>

      <div class="progress-content">
        <div class="overall-progress">
          <h4>总体进度</h4>
          <el-progress 
            :percentage="checkProgress.overall" 
            :status="checkProgress.status"
            :stroke-width="12"
          />
          <p class="progress-text">{{ checkProgress.message }}</p>
        </div>

        <!-- 条目内容正确性检查进度 -->
        <div v-if="checkProgress.contentAccuracy" class="content-accuracy-progress">
          <h4>内容正确性检查</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="progress-item">
                <span class="progress-label">文档解析</span>
                <el-progress 
                  :percentage="checkProgress.contentAccuracy.parsing" 
                  :show-text="false"
                  :stroke-width="6"
                />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="progress-item">
                <span class="progress-label">AI提取</span>
                <el-progress 
                  :percentage="checkProgress.contentAccuracy.extraction" 
                  :show-text="false"
                  :stroke-width="6"
                />
              </div>
            </el-col>
            <el-col :span="8">
              <div class="progress-item">
                <span class="progress-label">内容比对</span>
                <el-progress 
                  :percentage="checkProgress.contentAccuracy.comparison" 
                  :show-text="false"
                  :stroke-width="6"
                />
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 实时日志 -->
        <div class="check-logs">
          <h4>检查日志</h4>
          <div class="log-container">
            <div 
              v-for="(log, index) in checkLogs" 
              :key="index"
              class="log-item"
              :class="log.level"
            >
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 检查结果 -->
    <div v-if="checkResults.length > 0" class="check-results-section">
      <!-- 结果概览 -->
      <el-card class="results-overview" shadow="never">
        <div slot="header" class="section-header">
          <span>检查结果概览</span>
          <div class="result-actions">
            <el-button size="small" @click="exportAllReports" :loading="exportingAll">
              <i class="el-icon-download"></i> 导出全部报告
            </el-button>
            <el-button size="small" type="primary" @click="exportAnnotatedExcel" :loading="exportingExcel">
              <i class="el-icon-s-grid"></i> 导出标注Excel
            </el-button>
          </div>
        </div>

        <el-row :gutter="20">
          <el-col 
            v-for="result in checkResults" 
            :key="result.ruleCode"
            :span="6"
          >
            <div class="result-summary-card" @click="showRuleResult(result)">
              <div class="rule-info">
                <h4>{{ result.ruleName }}</h4>
                <el-tag v-if="result.ruleCode === 'CONTENT_ACCURACY'" type="success" size="mini">
                  AI智能
                </el-tag>
              </div>
              <div class="error-stats">
                <div class="error-count">{{ result.errorCount }} 错误</div>
                <div class="total-count">共 {{ result.totalCount }} 条</div>
              </div>
              <el-progress 
                :percentage="result.passRate" 
                :color="getPassRateColor(result.passRate)"
                :show-text="false"
                :stroke-width="6"
              />
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 详细结果展示 -->
      <div class="detailed-results">
        <el-tabs v-model="activeResultTab" type="card">
          <el-tab-pane 
            v-for="result in checkResults" 
            :key="result.ruleCode"
            :label="result.ruleName" 
            :name="result.ruleCode"
          >
            <!-- 条目内容正确性结果 -->
            <ContentAccuracyReport
              v-if="result.ruleCode === 'CONTENT_ACCURACY'"
              :report-data="result.reportData"
              @locate-excel="locateInExcel"
              @accept-suggestion="acceptSuggestion"
            />
            
            <!-- 其他规则结果 -->
            <div v-else class="other-rule-result">
              <!-- 这里放置其他规则的结果组件 -->
              <h3>{{ result.ruleName }} 检查结果</h3>
              <p>错误数量: {{ result.errorCount }}</p>
              <p>通过率: {{ result.passRate }}%</p>
              <!-- 具体的错误列表等 -->
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- Excel预览对话框 -->
    <el-dialog
      title="Excel预览"
      :visible.sync="excelPreviewVisible"
      width="90%"
      :close-on-click-modal="false"
    >
      <div class="excel-preview-content">
        <!-- 这里可以集成Excel预览组件 -->
        <div class="excel-viewer">
          <p>Excel文件预览功能</p>
          <p>定位到: 第{{ locatedRow }}行, {{ locatedField }}字段</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ContentAccuracyRuleConfig from './ContentAccuracyRuleConfig.vue'
import ContentAccuracyReport from './ContentAccuracyReport.vue'

export default {
  name: 'ComplianceCheckMain',
  components: {
    ContentAccuracyRuleConfig,
    ContentAccuracyReport
  },
  data() {
    return {
      // 规则选择
      selectedRules: [],
      
      // 现有规则（示例）
      existingRules: [
        {
          code: 'FIELD_REQUIRED',
          name: '必填字段检查',
          description: '检查必填字段是否为空'
        },
        {
          code: 'DATE_FORMAT',
          name: '日期格式检查',
          description: '检查日期字段格式是否正确'
        },
        {
          code: 'DATA_LENGTH',
          name: '数据长度检查',
          description: '检查字段长度是否符合要求'
        }
      ],
      
      // 条目内容正确性配置
      contentAccuracyConfig: {
        selectedFields: [],
        fieldMapping: {}
      },
      
      // 文件相关
      excelFile: null,
      documentFiles: [],
      excelColumns: [],
      
      // 检查状态
      checking: false,
      checkProgress: {
        visible: false,
        overall: 0,
        status: '',
        message: '',
        contentAccuracy: null
      },
      checkLogs: [],
      
      // 检查结果
      checkResults: [],
      activeResultTab: '',
      
      // 导出状态
      exportingAll: false,
      exportingExcel: false,
      
      // Excel预览
      excelPreviewVisible: false,
      locatedRow: 0,
      locatedField: ''
    }
  },
  methods: {
    // 规则选择变化
    onRuleSelectionChange() {
      // 如果选择了内容正确性规则，初始化配置
      if (this.selectedRules.includes('CONTENT_ACCURACY') && !this.contentAccuracyConfig.selectedFields) {
        this.contentAccuracyConfig = {
          selectedFields: ['title', 'responsible'],
          fieldMapping: {},
          advancedConfig: {}
        }
      }
    },
    
    // 内容正确性配置变化
    onContentAccuracyConfigChange(config) {
      this.contentAccuracyConfig = {
        selectedFields: config.selectedFields || [],
        fieldMapping: config.fieldMapping || {}
      }
    },
    
    // 保存内容正确性配置
    onContentAccuracyConfigSave(configData) {
      // 保存配置到后端
      this.saveRuleConfiguration(configData)
    },
    
    // Excel文件变化
    onExcelFileChange(file) {
      this.excelFile = file
      this.parseExcelColumns(file)
    },
    
    // 解析Excel列
    async parseExcelColumns(file) {
      try {
        const formData = new FormData()
        formData.append('file', file.raw)
        
        const response = await this.$api.parseExcelColumns(formData)
        this.excelColumns = response.data.columns || []
      } catch (error) {
        this.$message.error('解析Excel列失败')
        console.error('Parse Excel columns error:', error)
      }
    },
    
    // 档案文件变化
    onDocumentFilesChange(file, fileList) {
      this.documentFiles = fileList
    },
    
    // 移除Excel文件前
    beforeExcelRemove() {
      this.excelFile = null
      this.excelColumns = []
      return true
    },
    
    // 移除档案文件前
    beforeDocumentRemove() {
      return true
    },
    
    // 开始检查
    async startCheck() {
      // 验证配置
      if (this.selectedRules.length === 0) {
        this.$message.warning('请至少选择一个检查规则')
        return
      }
      
      if (!this.excelFile) {
        this.$message.warning('请上传Excel数据文件')
        return
      }
      
      if (this.selectedRules.includes('CONTENT_ACCURACY') && this.documentFiles.length === 0) {
        this.$message.warning('选择了内容正确性检查，请上传档案文档文件')
        return
      }
      
      this.checking = true
      this.checkProgress.visible = true
      this.checkProgress.overall = 0
      this.checkProgress.status = ''
      this.checkProgress.message = '准备开始检查...'
      this.checkLogs = []
      
      try {
        // 构建检查请求
        const checkRequest = {
          rules: this.selectedRules,
          excelFile: this.excelFile,
          documentFiles: this.documentFiles,
          configurations: {
            CONTENT_ACCURACY: this.contentAccuracyConfig
          }
        }
        
        // 启动检查
        const response = await this.$api.startComplianceCheck(checkRequest)
        const taskId = response.data.taskId
        
        // 轮询检查进度
        this.pollCheckProgress(taskId)
        
      } catch (error) {
        this.$message.error('启动检查失败')
        console.error('Start check error:', error)
        this.checking = false
        this.checkProgress.visible = false
      }
    },
    
    // 轮询检查进度
    async pollCheckProgress(taskId) {
      const pollInterval = setInterval(async () => {
        try {
          const response = await this.$api.getCheckProgress(taskId)
          const progress = response.data
          
          this.checkProgress.overall = progress.overall
          this.checkProgress.status = progress.status
          this.checkProgress.message = progress.message
          
          if (progress.contentAccuracy) {
            this.checkProgress.contentAccuracy = progress.contentAccuracy
          }
          
          // 添加新日志
          if (progress.logs) {
            this.checkLogs.push(...progress.logs)
          }
          
          // 检查是否完成
          if (progress.status === 'completed') {
            clearInterval(pollInterval)
            this.checking = false
            this.checkProgress.message = '检查完成'
            
            // 获取检查结果
            this.loadCheckResults(taskId)
          } else if (progress.status === 'failed') {
            clearInterval(pollInterval)
            this.checking = false
            this.checkProgress.status = 'exception'
            this.checkProgress.message = '检查失败'
            this.$message.error('检查过程中发生错误')
          }
          
        } catch (error) {
          clearInterval(pollInterval)
          this.checking = false
          this.$message.error('获取检查进度失败')
          console.error('Poll progress error:', error)
        }
      }, 2000) // 每2秒轮询一次
    },
    
    // 加载检查结果
    async loadCheckResults(taskId) {
      try {
        const response = await this.$api.getCheckResults(taskId)
        this.checkResults = response.data.results || []
        
        if (this.checkResults.length > 0) {
          this.activeResultTab = this.checkResults[0].ruleCode
        }
        
        this.$message.success('检查完成')
      } catch (error) {
        this.$message.error('获取检查结果失败')
        console.error('Load results error:', error)
      }
    },
    
    // 取消检查
    async cancelCheck() {
      try {
        await this.$api.cancelCheck()
        this.checking = false
        this.checkProgress.visible = false
        this.$message.info('检查已取消')
      } catch (error) {
        this.$message.error('取消检查失败')
        console.error('Cancel check error:', error)
      }
    },
    
    // 显示规则结果
    showRuleResult(result) {
      this.activeResultTab = result.ruleCode
    },
    
    // 定位到Excel
    locateInExcel(location) {
      this.locatedRow = location.rowIndex
      this.locatedField = location.field
      this.excelPreviewVisible = true
    },
    
    // 采纳建议
    async acceptSuggestion(suggestion) {
      try {
        await this.$api.acceptSuggestion(suggestion)
        this.$message.success('建议已采纳')
        // 刷新结果
        // this.refreshResults()
      } catch (error) {
        this.$message.error('采纳建议失败')
        console.error('Accept suggestion error:', error)
      }
    },
    
    // 导出全部报告
    async exportAllReports() {
      this.exportingAll = true
      
      try {
        const response = await this.$api.exportAllReports({
          results: this.checkResults
        })
        
        this.downloadFile(response.data, 'compliance_check_reports.zip')
        this.$message.success('报告导出成功')
      } catch (error) {
        this.$message.error('导出报告失败')
        console.error('Export reports error:', error)
      } finally {
        this.exportingAll = false
      }
    },
    
    // 导出标注Excel
    async exportAnnotatedExcel() {
      this.exportingExcel = true
      
      try {
        const response = await this.$api.exportAnnotatedExcel({
          results: this.checkResults
        })
        
        this.downloadFile(response.data, 'annotated_excel.xlsx')
        this.$message.success('标注Excel导出成功')
      } catch (error) {
        this.$message.error('导出Excel失败')
        console.error('Export Excel error:', error)
      } finally {
        this.exportingExcel = false
      }
    },
    
    // 下载文件
    downloadFile(data, filename) {
      const blob = new Blob([data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.click()
      window.URL.revokeObjectURL(url)
    },
    
    // 保存规则配置
    async saveRuleConfiguration(configData) {
      try {
        await this.$api.saveRuleConfiguration(configData)
        this.$message.success('配置保存成功')
      } catch (error) {
        this.$message.error('配置保存失败')
        console.error('Save configuration error:', error)
      }
    },
    
    // 获取通过率颜色
    getPassRateColor(rate) {
      if (rate >= 90) return '#67c23a'
      if (rate >= 70) return '#e6a23c'
      return '#f56c6c'
    },
    
    // 格式化时间
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString()
    }
  }
}
</script>

<style scoped>
.compliance-check-main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rule-selection {
  margin-bottom: 24px;
}

.rule-selection h4 {
  margin-bottom: 16px;
  color: #333;
}

.rule-checkbox {
  display: block;
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  transition: all 0.3s;
}

.rule-checkbox:hover {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.rule-checkbox.content-accuracy-rule {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.rule-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.rule-name {
  font-weight: 500;
  color: #333;
}

.rule-description {
  font-size: 12px;
  color: #666;
}

.content-accuracy-config {
  margin: 24px 0;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.file-upload-section {
  margin-top: 24px;
}

.file-upload-section h4 {
  margin-bottom: 16px;
  color: #333;
}

.upload-item {
  margin-bottom: 16px;
}

.upload-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.progress-content {
  max-height: 500px;
  overflow-y: auto;
}

.overall-progress {
  margin-bottom: 24px;
}

.overall-progress h4 {
  margin-bottom: 12px;
  color: #333;
}

.progress-text {
  margin-top: 8px;
  color: #666;
  text-align: center;
}

.content-accuracy-progress {
  margin-bottom: 24px;
}

.content-accuracy-progress h4 {
  margin-bottom: 12px;
  color: #333;
}

.progress-item {
  text-align: center;
}

.progress-label {
  display: block;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.check-logs h4 {
  margin-bottom: 12px;
  color: #333;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
}

.log-item {
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.log-item.info { color: #409EFF; }
.log-item.success { color: #67c23a; }
.log-item.warning { color: #e6a23c; }
.log-item.error { color: #f56c6c; }

.log-time {
  color: #999;
  margin-right: 8px;
}

.check-results-section {
  margin-top: 24px;
}

.result-actions {
  display: flex;
  gap: 8px;
}

.result-summary-card {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.result-summary-card:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.rule-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.rule-info h4 {
  margin: 0;
  color: #333;
  font-size: 14px;
}

.error-stats {
  margin-bottom: 12px;
}

.error-count {
  font-size: 18px;
  font-weight: 600;
  color: #f56c6c;
}

.total-count {
  font-size: 12px;
  color: #666;
}

.detailed-results {
  margin-top: 20px;
}

.other-rule-result {
  padding: 20px;
  text-align: center;
  color: #666;
}

.excel-preview-content {
  height: 600px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.excel-viewer {
  padding: 20px;
  text-align: center;
  color: #666;
}
</style>
