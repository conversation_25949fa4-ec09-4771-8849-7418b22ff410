package com.deeppaas.ollama;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.*;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Ollama Embedding服务
 * 使用BGE-Large-ZH模型进行中文文本向量化
 */
@Service
@Slf4j
public class OllamaEmbeddingService {
    
    @Value("${ollama.base.url:http://localhost:11434}")
    private String ollamaBaseUrl;
    
    @Value("${ollama.embedding.model:bge-m3}")
    private String embeddingModel;
    
    @Value("${ollama.embedding.timeout:30000}")
    private int timeout;
    
    @Value("${ollama.embedding.batch_size:8}")
    private int batchSize;
    
    @Value("${ollama.embedding.enable_cache:true}")
    private boolean enableCache;
    
    @Value("${ollama.embedding.cache_size:1000}")
    private int cacheSize;
    
    private final RestTemplate restTemplate;
    private final Map<String, float[]> embeddingCache;
    
    public OllamaEmbeddingService() {
        this.restTemplate = new RestTemplate();
        this.embeddingCache = new ConcurrentHashMap<>();
    }
    
    @PostConstruct
    public void initialize() {
        try {
            // 检查Ollama服务状态
            checkOllamaStatus();
            
            // 检查模型是否可用
            checkModelAvailability();
            
            // 预热模型
            warmupModel();
            
            log.info("Ollama Embedding服务初始化完成 - 模型: {}, 缓存: {}", embeddingModel, enableCache);
            
        } catch (Exception e) {
            log.error("Ollama Embedding服务初始化失败", e);
        }
    }
    
    /**
     * 获取单个文本的embedding
     */
    public float[] getTextEmbedding(String text) {
        if (text == null || text.trim().isEmpty()) {
            return new float[1024]; // BGE-M3默认1024维向量
        }
        
        // 检查缓存
        if (enableCache) {
            String cacheKey = generateCacheKey(text);
            if (embeddingCache.containsKey(cacheKey)) {
                log.debug("命中embedding缓存: {}", text.substring(0, Math.min(20, text.length())));
                return embeddingCache.get(cacheKey);
            }
        }
        
        try {
            float[] embedding = callOllamaEmbedding(text);
            
            // 缓存结果
            if (enableCache && embedding != null) {
                cacheEmbedding(text, embedding);
            }
            
            return embedding;
            
        } catch (Exception e) {
            log.error("Ollama embedding调用失败: {}", text.substring(0, Math.min(50, text.length())), e);
            return new float[1024]; // 返回零向量作为降级
        }
    }
    
    /**
     * 批量获取文本embedding
     */
    public Map<String, float[]> getBatchEmbeddings(List<String> texts) {
        Map<String, float[]> results = new HashMap<>();
        
        if (texts == null || texts.isEmpty()) {
            return results;
        }
        
        // 检查缓存
        List<String> uncachedTexts = new ArrayList<>();
        if (enableCache) {
            for (String text : texts) {
                String cacheKey = generateCacheKey(text);
                if (embeddingCache.containsKey(cacheKey)) {
                    results.put(text, embeddingCache.get(cacheKey));
                } else {
                    uncachedTexts.add(text);
                }
            }
        } else {
            uncachedTexts.addAll(texts);
        }
        
        if (uncachedTexts.isEmpty()) {
            return results;
        }
        
        // 分批处理
        for (int i = 0; i < uncachedTexts.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, uncachedTexts.size());
            List<String> batch = uncachedTexts.subList(i, endIndex);
            
            try {
                Map<String, float[]> batchResults = processBatch(batch);
                results.putAll(batchResults);
                
                // 缓存批处理结果
                if (enableCache) {
                    for (Map.Entry<String, float[]> entry : batchResults.entrySet()) {
                        cacheEmbedding(entry.getKey(), entry.getValue());
                    }
                }
                
            } catch (Exception e) {
                log.error("批处理embedding失败", e);
                // 降级到单个处理
                for (String text : batch) {
                    results.put(text, getTextEmbedding(text));
                }
            }
        }
        
        return results;
    }
    
    /**
     * 调用Ollama API获取embedding
     */
    private float[] callOllamaEmbedding(String text) {
        try {
            String url = ollamaBaseUrl + "/api/embeddings";
            
            // 构建请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", embeddingModel);
            requestBody.put("prompt", text);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            // 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                
                if (responseBody.containsKey("embedding")) {
                    List<Double> embeddingList = (List<Double>) responseBody.get("embedding");
                    return convertToFloatArray(embeddingList);
                }
            }
            
            throw new RuntimeException("Invalid response from Ollama");
            
        } catch (Exception e) {
            log.error("Ollama API调用失败", e);
            throw e;
        }
    }
    
    /**
     * 批处理文本
     */
    private Map<String, float[]> processBatch(List<String> batch) {
        Map<String, float[]> results = new HashMap<>();
        
        // Ollama目前不支持真正的批处理，所以并发处理
        for (String text : batch) {
            try {
                float[] embedding = callOllamaEmbedding(text);
                results.put(text, embedding);
                
                // 添加小延迟避免过载
                Thread.sleep(10);
                
            } catch (Exception e) {
                log.warn("批处理中单个文本失败: {}", text.substring(0, Math.min(30, text.length())), e);
                results.put(text, new float[1024]);
            }
        }
        
        return results;
    }
    
    /**
     * 检查Ollama服务状态
     */
    private void checkOllamaStatus() {
        try {
            String url = ollamaBaseUrl + "/api/version";
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("Ollama服务连接成功: {}", ollamaBaseUrl);
            } else {
                throw new RuntimeException("Ollama服务不可用");
            }
            
        } catch (Exception e) {
            log.error("Ollama服务检查失败: {}", ollamaBaseUrl, e);
            throw new RuntimeException("无法连接到Ollama服务", e);
        }
    }
    
    /**
     * 检查模型可用性
     */
    private void checkModelAvailability() {
        try {
            String url = ollamaBaseUrl + "/api/tags";
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                List<Map<String, Object>> models = (List<Map<String, Object>>) responseBody.get("models");
                
                boolean modelFound = false;
                if (models != null) {
                    for (Map<String, Object> model : models) {
                        String modelName = (String) model.get("name");
                        if (modelName != null && modelName.contains(embeddingModel)) {
                            modelFound = true;
                            log.info("找到embedding模型: {}", modelName);
                            break;
                        }
                    }
                }
                
                if (!modelFound) {
                    log.warn("未找到指定的embedding模型: {}，请运行: ollama pull {}", embeddingModel, embeddingModel);
                }
            }
            
        } catch (Exception e) {
            log.warn("检查模型可用性失败", e);
        }
    }
    
    /**
     * 预热模型
     */
    private void warmupModel() {
        try {
            log.info("开始预热embedding模型...");
            
            String warmupText = "这是一个用于预热BGE-M3模型的中文测试文本";
            float[] embedding = callOllamaEmbedding(warmupText);
            
            if (embedding != null && embedding.length > 0) {
                log.info("模型预热完成，向量维度: {}", embedding.length);
            } else {
                log.warn("模型预热失败，返回空向量");
            }
            
        } catch (Exception e) {
            log.warn("模型预热失败", e);
        }
    }
    
    /**
     * 缓存embedding结果
     */
    private void cacheEmbedding(String text, float[] embedding) {
        if (embeddingCache.size() >= cacheSize) {
            // 简单的LRU清理：移除一些旧的缓存
            Iterator<String> iterator = embeddingCache.keySet().iterator();
            for (int i = 0; i < cacheSize / 4 && iterator.hasNext(); i++) {
                iterator.next();
                iterator.remove();
            }
        }
        
        String cacheKey = generateCacheKey(text);
        embeddingCache.put(cacheKey, embedding);
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(String text) {
        return String.valueOf(text.hashCode());
    }
    
    /**
     * 转换为float数组
     */
    private float[] convertToFloatArray(List<Double> doubleList) {
        if (doubleList == null) {
            return new float[1024];
        }
        
        float[] result = new float[doubleList.size()];
        for (int i = 0; i < doubleList.size(); i++) {
            result[i] = doubleList.get(i).floatValue();
        }
        
        return result;
    }
    
    /**
     * 清理缓存
     */
    public void clearCache() {
        embeddingCache.clear();
        log.info("Embedding缓存已清理");
    }
    
    /**
     * 获取服务状态
     */
    public Map<String, Object> getServiceStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("ollamaUrl", ollamaBaseUrl);
        status.put("embeddingModel", embeddingModel);
        status.put("cacheEnabled", enableCache);
        status.put("cacheSize", embeddingCache.size());
        status.put("maxCacheSize", cacheSize);
        status.put("batchSize", batchSize);
        
        try {
            checkOllamaStatus();
            status.put("ollamaStatus", "ONLINE");
        } catch (Exception e) {
            status.put("ollamaStatus", "OFFLINE");
            status.put("error", e.getMessage());
        }
        
        return status;
    }
    
    /**
     * 获取缓存统计
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", embeddingCache.size());
        stats.put("maxCacheSize", cacheSize);
        stats.put("cacheEnabled", enableCache);
        stats.put("estimatedMemoryUsage", embeddingCache.size() * 1024 * 4 / 1024 / 1024 + " MB");
        
        return stats;
    }
}
