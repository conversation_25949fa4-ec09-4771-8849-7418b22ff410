# 智能信息提取模块依赖
# py3.9_copy 环境 - 经过依赖冲突解决的最终版本

# ================================
# 核心深度学习框架
# ================================
torch==1.10.1+cu113  # GPU设备管理、模型加载
transformers==4.21.0  # Qwen模型支持
tokenizers==0.12.1  # 与transformers兼容的版本

# ================================
# PaddlePaddle生态
# ================================
paddlepaddle-gpu==2.5.2  # 主框架，CUDA 11.3兼容
paddleocr==3.0.2  # OCR文字识别最新版
# 注意：PaddlePaddle可能存在libpaddle.pyd冲突，需要重新安装

# ================================
# Web API服务
# ================================
fastapi==0.104.1  # 支持pydantic 2.x的版本
uvicorn==0.18.3  # ASGI服务器
pydantic==2.11.7  # 数据验证（已升级到2.x）

# ================================
# 配置和序列化
# ================================
PyYAML==6.0.2  # 配置文件解析

# ================================
# 图像处理
# ================================
opencv-python==4.8.1.78  # 解决循环导入问题的稳定版本
Pillow==11.2.1  # 图像格式支持（已更新）
numpy==1.26.2  # 数值计算

# ================================
# 文本处理
# ================================
jieba==0.39  # 中文分词

# ================================
# 机器学习
# ================================
scikit-learn==1.6.1  # 机器学习工具

# ================================
# 系统和工具
# ================================
typing-extensions>=4.0.0  # 类型注解扩展

# ================================
# 开发和测试 (可选)
# ================================
pytest>=7.0.0
pytest-asyncio>=0.20.0

# ================================
# 环境测试结果 (2024-12-19)
# ================================
# ✅ PyTorch 1.10.1+cu113 - CUDA可用，2个GPU
# ✅ Transformers 4.21.0 + Tokenizers 0.12.1 - 兼容正常
# ❌ PaddlePaddle 2.5.2 - 与PyTorch冲突 (generic_type已注册)
# ✅ PaddleOCR 3.0.2 - 可以独立使用
# ✅ FastAPI 0.104.1 + Pydantic 2.11.7 - 兼容正常
# ❌ OpenCV 4.8.1.78 - 循环导入问题
# ✅ 其他组件正常

# ================================
# 已知问题和解决方案
# ================================
# 1. PaddlePaddle与PyTorch冲突：
#    - 原因：两者都注册了相同的GPU设备属性类型
#    - 解决：在不同进程中使用，或使用PaddleOCR的独立模式
#
# 2. OpenCV循环导入：
#    - 原因：多个OpenCV包版本冲突
#    - 解决：清理所有OpenCV包后重新安装单一版本
#
# 3. NumPy版本兼容性：
#    - 使用 numpy<2.0 确保与旧版PyTorch兼容

# ================================
# 推荐使用方式
# ================================
# 1. 文档OCR处理：使用PaddleOCR独立进程
# 2. 文本理解：使用PyTorch + Transformers
# 3. Web API：使用FastAPI + Pydantic 2.x
# 4. 图像预处理：临时解决OpenCV问题或使用PIL
