package com.toolbox.compliance.service;

import com.toolbox.compliance.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel错误标注服务
 * 在Excel文件中标注内容正确性检查的错误
 */
@Slf4j
@Service
public class ExcelErrorAnnotationService {
    
    // 错误类型对应的颜色
    private static final Map<String, Short> ERROR_TYPE_COLORS = new HashMap<>();
    
    static {
        ERROR_TYPE_COLORS.put("mismatch", IndexedColors.RED.getIndex());           // 完全不匹配 - 红色
        ERROR_TYPE_COLORS.put("partial", IndexedColors.ORANGE.getIndex());         // 部分匹配 - 橙色
        ERROR_TYPE_COLORS.put("format", IndexedColors.YELLOW.getIndex());          // 格式错误 - 黄色
        ERROR_TYPE_COLORS.put("missing", IndexedColors.ROSE.getIndex());           // 缺失内容 - 玫瑰色
    }
    
    /**
     * 为Excel文件添加错误标注
     */
    public byte[] annotateExcelWithErrors(String originalExcelPath, 
                                        List<ContentAccuracyError> errors,
                                        Map<String, String> fieldColumnMapping) throws IOException {
        
        log.info("开始为Excel文件添加错误标注，错误数量: {}", errors.size());
        
        try (FileInputStream fis = new FileInputStream(originalExcelPath);
             XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
            
            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            
            // 创建错误样式
            Map<String, CellStyle> errorStyles = createErrorStyles(workbook);
            
            // 创建注释样式
            CreationHelper factory = workbook.getCreationHelper();
            Drawing<?> drawing = sheet.createDrawingPatriarch();
            
            // 添加错误标注
            annotateErrors(sheet, errors, fieldColumnMapping, errorStyles, factory, drawing);
            
            // 添加图例
            addErrorLegend(sheet, errorStyles);
            
            // 添加统计信息
            addErrorStatistics(sheet, errors);
            
            // 输出到字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            workbook.write(baos);
            
            log.info("Excel错误标注完成");
            return baos.toByteArray();
        }
    }
    
    /**
     * 创建错误样式
     */
    private Map<String, CellStyle> createErrorStyles(Workbook workbook) {
        Map<String, CellStyle> styles = new HashMap<>();
        
        for (Map.Entry<String, Short> entry : ERROR_TYPE_COLORS.entrySet()) {
            String errorType = entry.getKey();
            Short colorIndex = entry.getValue();
            
            CellStyle style = workbook.createCellStyle();
            
            // 设置背景色
            style.setFillForegroundColor(colorIndex);
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            
            // 设置边框
            style.setBorderTop(BorderStyle.THIN);
            style.setBorderBottom(BorderStyle.THIN);
            style.setBorderLeft(BorderStyle.THIN);
            style.setBorderRight(BorderStyle.THIN);
            
            // 设置字体
            Font font = workbook.createFont();
            font.setBold(true);
            font.setColor(IndexedColors.WHITE.getIndex());
            style.setFont(font);
            
            styles.put(errorType, style);
        }
        
        return styles;
    }
    
    /**
     * 标注错误
     */
    private void annotateErrors(Sheet sheet, 
                              List<ContentAccuracyError> errors,
                              Map<String, String> fieldColumnMapping,
                              Map<String, CellStyle> errorStyles,
                              CreationHelper factory,
                              Drawing<?> drawing) {
        
        // 获取列名到列索引的映射
        Map<String, Integer> columnIndexMap = getColumnIndexMap(sheet, fieldColumnMapping);
        
        for (ContentAccuracyError error : errors) {
            try {
                // 获取行和列
                int rowIndex = error.getRowIndex() - 1; // Excel行号从1开始，POI从0开始
                String fieldCode = error.getField();
                
                // 获取列索引
                Integer columnIndex = columnIndexMap.get(fieldCode);
                if (columnIndex == null) {
                    log.warn("未找到字段 {} 对应的列", fieldCode);
                    continue;
                }
                
                // 获取单元格
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    row = sheet.createRow(rowIndex);
                }
                
                Cell cell = row.getCell(columnIndex);
                if (cell == null) {
                    cell = row.createCell(columnIndex);
                }
                
                // 应用错误样式
                CellStyle errorStyle = errorStyles.get(error.getErrorType());
                if (errorStyle != null) {
                    cell.setCellStyle(errorStyle);
                }
                
                // 添加注释
                addCellComment(cell, error, factory, drawing);
                
            } catch (Exception e) {
                log.error("标注错误失败: {}", error, e);
            }
        }
    }
    
    /**
     * 获取列名到列索引的映射
     */
    private Map<String, Integer> getColumnIndexMap(Sheet sheet, Map<String, String> fieldColumnMapping) {
        Map<String, Integer> columnIndexMap = new HashMap<>();
        
        // 获取表头行
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            return columnIndexMap;
        }
        
        // 遍历表头，建立列名到索引的映射
        for (Cell cell : headerRow) {
            String columnName = cell.getStringCellValue();
            int columnIndex = cell.getColumnIndex();
            
            // 查找对应的字段
            for (Map.Entry<String, String> entry : fieldColumnMapping.entrySet()) {
                String fieldCode = entry.getKey();
                String excelColumnName = entry.getValue();
                
                if (columnName.equals(excelColumnName)) {
                    columnIndexMap.put(fieldCode, columnIndex);
                    break;
                }
            }
        }
        
        return columnIndexMap;
    }
    
    /**
     * 添加单元格注释
     */
    private void addCellComment(Cell cell, 
                              ContentAccuracyError error, 
                              CreationHelper factory, 
                              Drawing<?> drawing) {
        
        // 创建注释
        ClientAnchor anchor = factory.createClientAnchor();
        anchor.setCol1(cell.getColumnIndex());
        anchor.setCol2(cell.getColumnIndex() + 3);
        anchor.setRow1(cell.getRowIndex());
        anchor.setRow2(cell.getRowIndex() + 5);
        
        Comment comment = drawing.createCellComment(anchor);
        
        // 设置注释内容
        StringBuilder commentText = new StringBuilder();
        commentText.append("❌ 内容正确性错误\n");
        commentText.append("错误类型: ").append(getErrorTypeName(error.getErrorType())).append("\n");
        commentText.append("Excel录入值: ").append(error.getExcelValue() != null ? error.getExcelValue() : "(空)").append("\n");
        commentText.append("AI提取值: ").append(error.getExtractedValue() != null ? error.getExtractedValue() : "(未提取到)").append("\n");
        commentText.append("相似度: ").append(error.getSimilarity()).append("%\n");
        
        if (error.getSuggestion() != null && !error.getSuggestion().isEmpty()) {
            commentText.append("修正建议: ").append(error.getSuggestion());
        }
        
        RichTextString richText = factory.createRichTextString(commentText.toString());
        comment.setString(richText);
        comment.setAuthor("AI智能检查");
        
        cell.setCellComment(comment);
    }
    
    /**
     * 添加错误图例
     */
    private void addErrorLegend(Sheet sheet, Map<String, CellStyle> errorStyles) {
        // 找到合适的位置添加图例（右侧空白区域）
        int legendStartRow = 1;
        int legendStartCol = sheet.getRow(0).getLastCellNum() + 2;
        
        // 添加图例标题
        Row titleRow = sheet.getRow(legendStartRow);
        if (titleRow == null) {
            titleRow = sheet.createRow(legendStartRow);
        }
        
        Cell titleCell = titleRow.createCell(legendStartCol);
        titleCell.setCellValue("错误类型图例");
        
        // 创建标题样式
        CellStyle titleStyle = sheet.getWorkbook().createCellStyle();
        Font titleFont = sheet.getWorkbook().createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 12);
        titleStyle.setFont(titleFont);
        titleCell.setCellStyle(titleStyle);
        
        // 添加各种错误类型的图例
        int currentRow = legendStartRow + 2;
        for (Map.Entry<String, CellStyle> entry : errorStyles.entrySet()) {
            String errorType = entry.getKey();
            CellStyle style = entry.getValue();
            
            Row row = sheet.getRow(currentRow);
            if (row == null) {
                row = sheet.createRow(currentRow);
            }
            
            // 颜色示例单元格
            Cell colorCell = row.createCell(legendStartCol);
            colorCell.setCellValue("■");
            colorCell.setCellStyle(style);
            
            // 错误类型说明
            Cell descCell = row.createCell(legendStartCol + 1);
            descCell.setCellValue(getErrorTypeName(errorType));
            
            currentRow++;
        }
    }
    
    /**
     * 添加错误统计信息
     */
    private void addErrorStatistics(Sheet sheet, List<ContentAccuracyError> errors) {
        // 统计各类错误数量
        Map<String, Integer> errorCounts = new HashMap<>();
        for (ContentAccuracyError error : errors) {
            errorCounts.merge(error.getErrorType(), 1, Integer::sum);
        }
        
        // 找到合适的位置添加统计信息
        int statsStartRow = 1;
        int statsStartCol = sheet.getRow(0).getLastCellNum() + 6;
        
        // 添加统计标题
        Row titleRow = sheet.getRow(statsStartRow);
        if (titleRow == null) {
            titleRow = sheet.createRow(statsStartRow);
        }
        
        Cell titleCell = titleRow.createCell(statsStartCol);
        titleCell.setCellValue("错误统计");
        
        // 创建标题样式
        CellStyle titleStyle = sheet.getWorkbook().createCellStyle();
        Font titleFont = sheet.getWorkbook().createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 12);
        titleStyle.setFont(titleFont);
        titleCell.setCellStyle(titleStyle);
        
        // 添加统计数据
        int currentRow = statsStartRow + 2;
        
        // 总错误数
        Row totalRow = sheet.getRow(currentRow);
        if (totalRow == null) {
            totalRow = sheet.createRow(currentRow);
        }
        totalRow.createCell(statsStartCol).setCellValue("总错误数:");
        totalRow.createCell(statsStartCol + 1).setCellValue(errors.size());
        currentRow++;
        
        // 各类错误统计
        for (Map.Entry<String, Integer> entry : errorCounts.entrySet()) {
            Row row = sheet.getRow(currentRow);
            if (row == null) {
                row = sheet.createRow(currentRow);
            }
            
            row.createCell(statsStartCol).setCellValue(getErrorTypeName(entry.getKey()) + ":");
            row.createCell(statsStartCol + 1).setCellValue(entry.getValue());
            currentRow++;
        }
    }
    
    /**
     * 获取错误类型名称
     */
    private String getErrorTypeName(String errorType) {
        switch (errorType) {
            case "mismatch":
                return "完全不匹配";
            case "partial":
                return "部分匹配";
            case "format":
                return "格式错误";
            case "missing":
                return "缺失内容";
            default:
                return errorType;
        }
    }
    
    /**
     * 创建带错误标注的新Excel文件
     */
    public byte[] createAnnotatedExcel(List<ExcelRowData> originalData,
                                     List<ContentAccuracyError> errors,
                                     Map<String, String> fieldColumnMapping) throws IOException {
        
        log.info("创建带错误标注的新Excel文件");
        
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("内容正确性检查结果");
            
            // 创建错误样式
            Map<String, CellStyle> errorStyles = createErrorStyles(workbook);
            
            // 创建表头
            createHeader(sheet, fieldColumnMapping);
            
            // 填充数据并标注错误
            fillDataWithAnnotations(sheet, originalData, errors, fieldColumnMapping, errorStyles);
            
            // 添加图例和统计
            addErrorLegend(sheet, errorStyles);
            addErrorStatistics(sheet, errors);
            
            // 自动调整列宽
            for (int i = 0; i < fieldColumnMapping.size(); i++) {
                sheet.autoSizeColumn(i);
            }
            
            // 输出到字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            workbook.write(baos);
            
            return baos.toByteArray();
        }
    }
    
    /**
     * 创建表头
     */
    private void createHeader(Sheet sheet, Map<String, String> fieldColumnMapping) {
        Row headerRow = sheet.createRow(0);
        
        // 创建表头样式
        CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        Font headerFont = sheet.getWorkbook().createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        
        int columnIndex = 0;
        for (String columnName : fieldColumnMapping.values()) {
            Cell cell = headerRow.createCell(columnIndex++);
            cell.setCellValue(columnName);
            cell.setCellStyle(headerStyle);
        }
    }
    
    /**
     * 填充数据并标注错误
     */
    private void fillDataWithAnnotations(Sheet sheet,
                                       List<ExcelRowData> originalData,
                                       List<ContentAccuracyError> errors,
                                       Map<String, String> fieldColumnMapping,
                                       Map<String, CellStyle> errorStyles) {
        
        // 创建错误映射，便于快速查找
        Map<String, ContentAccuracyError> errorMap = new HashMap<>();
        for (ContentAccuracyError error : errors) {
            String key = error.getRowIndex() + "_" + error.getField();
            errorMap.put(key, error);
        }
        
        // 填充数据
        for (int i = 0; i < originalData.size(); i++) {
            ExcelRowData rowData = originalData.get(i);
            Row row = sheet.createRow(i + 1); // +1 因为第0行是表头
            
            int columnIndex = 0;
            for (Map.Entry<String, String> entry : fieldColumnMapping.entrySet()) {
                String fieldCode = entry.getKey();
                String columnName = entry.getValue();
                
                Cell cell = row.createCell(columnIndex++);
                
                // 设置单元格值
                Object value = rowData.getValue(fieldCode);
                if (value != null) {
                    cell.setCellValue(value.toString());
                }
                
                // 检查是否有错误
                String errorKey = (i + 2) + "_" + fieldCode; // +2 因为Excel行号从1开始，且有表头
                ContentAccuracyError error = errorMap.get(errorKey);
                
                if (error != null) {
                    // 应用错误样式
                    CellStyle errorStyle = errorStyles.get(error.getErrorType());
                    if (errorStyle != null) {
                        cell.setCellStyle(errorStyle);
                    }
                    
                    // 添加注释
                    CreationHelper factory = sheet.getWorkbook().getCreationHelper();
                    Drawing<?> drawing = sheet.createDrawingPatriarch();
                    addCellComment(cell, error, factory, drawing);
                }
            }
        }
    }
}
