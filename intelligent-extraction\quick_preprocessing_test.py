#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速图像预处理测试脚本
用于快速验证图像预处理功能是否正常工作
"""
import sys
import time
from pathlib import Path
from PIL import Image
import logging

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.utils.image_preprocessor import get_image_processor
from src.core.config_manager import ConfigManager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_image_preprocessing(image_path: str):
    """测试图像预处理功能"""
    
    # 验证文件
    image_file = Path(image_path)
    if not image_file.exists():
        print(f"❌ 文件不存在: {image_path}")
        return False
    
    print(f"📄 测试文档: {image_file.name}")
    print("=" * 60)
    
    try:
        # 分析原始图像
        with Image.open(image_file) as img:
            width, height = img.size
            file_size = image_file.stat().st_size
            print(f"📊 原始图像: {width}×{height}, {file_size/1024:.1f}KB, {img.format}")
        
        # 初始化配置和预处理器
        config_manager = ConfigManager()
        preprocessing_config = config_manager.config.get('image_preprocessing', {})
        
        print(f"🔧 预处理配置:")
        print(f"   启用状态: {preprocessing_config.get('enabled', False)}")
        print(f"   目标长边: {preprocessing_config.get('target_long_side', 1600)}px")
        print(f"   JPEG质量: {preprocessing_config.get('jpeg_quality', 90)}")
        print(f"   对比度增强: {preprocessing_config.get('enhance_contrast', 1.15)}")
        print(f"   锐度增强: {preprocessing_config.get('enhance_sharpness', 1.05)}")
        
        # 创建预处理器
        processor = get_image_processor(preprocessing_config)
        
        # 读取图像数据
        with open(image_file, 'rb') as f:
            image_data = f.read()
        
        print(f"\n🚀 开始图像预处理...")
        start_time = time.time()
        
        # 执行预处理
        processed_data, stats = processor.process_image(image_data, output_format='JPEG')
        
        processing_time = time.time() - start_time
        
        print(f"✅ 预处理完成，耗时: {processing_time:.2f}秒")
        print(f"📊 处理统计:")
        print(f"   原始尺寸: {stats.get('original_size', 'N/A')}")
        print(f"   处理后尺寸: {stats.get('processed_size', 'N/A')}")
        print(f"   原始文件大小: {stats.get('original_file_size', 0)/1024:.1f}KB")
        print(f"   处理后文件大小: {stats.get('processed_file_size', 0)/1024:.1f}KB")
        print(f"   文件大小压缩: {stats.get('file_size_reduction', 0)*100:.1f}%")
        print(f"   处理时间: {stats.get('processing_time', 0):.2f}秒")
        
        # 保存处理后的图像用于检查
        output_path = image_file.parent / f"processed_{image_file.stem}.jpg"
        with open(output_path, 'wb') as f:
            f.write(processed_data)
        
        print(f"💾 处理后图像已保存: {output_path}")
        
        # 验证处理后图像
        try:
            with Image.open(output_path) as processed_img:
                proc_width, proc_height = processed_img.size
                proc_file_size = output_path.stat().st_size
                print(f"📊 处理后图像验证: {proc_width}×{proc_height}, {proc_file_size/1024:.1f}KB, {processed_img.format}")
                
                # 计算实际压缩比
                actual_compression = (1 - proc_file_size / file_size) * 100
                print(f"📈 实际压缩效果: {actual_compression:.1f}%")
                
        except Exception as e:
            print(f"⚠️ 处理后图像验证失败: {e}")
        
        # 获取处理器统计信息
        processor_stats = processor.get_stats()
        print(f"\n📊 处理器统计:")
        print(f"   总处理数量: {processor_stats.get('total_processed', 0)}")
        print(f"   平均处理时间: {processor_stats.get('avg_processing_time', 0):.2f}秒")
        print(f"   平均压缩比: {processor_stats.get('size_reduction_ratio', 0)*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 预处理测试失败: {e}")
        logger.exception("预处理测试异常")
        return False


def test_batch_processing(folder_path: str):
    """测试批量处理"""
    folder = Path(folder_path)
    if not folder.exists() or not folder.is_dir():
        print(f"❌ 文件夹不存在: {folder_path}")
        return False
    
    # 查找图像文件
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_files = [f for f in folder.iterdir() 
                   if f.suffix.lower() in image_extensions]
    
    if not image_files:
        print(f"❌ 文件夹中没有找到图像文件: {folder_path}")
        return False
    
    print(f"📁 批量处理测试: {len(image_files)}个文件")
    print("=" * 60)
    
    success_count = 0
    total_time = 0
    
    for i, image_file in enumerate(image_files[:5], 1):  # 限制最多5个文件
        print(f"\n📄 [{i}/{min(5, len(image_files))}] {image_file.name}")
        print("-" * 40)
        
        start_time = time.time()
        if test_image_preprocessing(str(image_file)):
            success_count += 1
        
        file_time = time.time() - start_time
        total_time += file_time
        print(f"⏱️ 单文件总耗时: {file_time:.2f}秒")
    
    print(f"\n🎯 批量处理结果:")
    print(f"   成功处理: {success_count}/{min(5, len(image_files))}")
    print(f"   总耗时: {total_time:.2f}秒")
    print(f"   平均耗时: {total_time/min(5, len(image_files)):.2f}秒/文件")
    
    return success_count > 0


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("🧪 快速图像预处理测试工具")
        print("=" * 50)
        print("用法:")
        print("  单文件测试: python quick_preprocessing_test.py <图像路径>")
        print("  批量测试:   python quick_preprocessing_test.py <文件夹路径>")
        print()
        print("示例:")
        print("  python quick_preprocessing_test.py C:\\Documents\\scan.jpg")
        print("  python quick_preprocessing_test.py C:\\Documents\\scans\\")
        print()
        print("💡 此工具用于快速验证图像预处理功能是否正常工作")
        return
    
    path = sys.argv[1]
    path_obj = Path(path)
    
    print("🧪 开始快速图像预处理测试")
    print("=" * 60)
    
    if path_obj.is_file():
        # 单文件测试
        success = test_image_preprocessing(path)
        if success:
            print("\n🎉 单文件测试完成！")
        else:
            print("\n❌ 单文件测试失败")
    elif path_obj.is_dir():
        # 批量测试
        success = test_batch_processing(path)
        if success:
            print("\n🎉 批量测试完成！")
        else:
            print("\n❌ 批量测试失败")
    else:
        print(f"❌ 路径不存在: {path}")


if __name__ == "__main__":
    main()
