#!/usr/bin/env python3
"""
测试Ollama模型功能
位置: intelligent-extraction\src\test\test_ollama_models.py
"""
import sys
import os
import logging
from pathlib import Path

# 添加intelligent-extraction目录到路径
current_dir = Path(__file__).parent  # src/test
src_dir = current_dir.parent         # src
project_dir = src_dir.parent         # intelligent-extraction
sys.path.insert(0, str(project_dir))

# 导入模型相关模块
try:
    from src.models import (
        create_llm_model,
        create_embedding_model,
        check_model_compatibility,
        MODEL_PRESETS
    )
    print("✅ 模型模块导入成功")
except ImportError as e:
    print(f"❌ 模型模块导入失败: {e}")
    print(f"当前路径: {sys.path}")
    print(f"项目目录: {project_dir}")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_model_compatibility():
    """测试模型兼容性"""
    print("=== 模型兼容性检查 ===")
    
    compatibility = check_model_compatibility()
    
    print(f"Ollama服务: {'✅' if compatibility['ollama_service'] else '❌'}")
    print(f"PaddleOCR: {'✅' if compatibility['paddleocr'] else '❌'}")
    print(f"GPU可用: {'✅' if compatibility['gpu_available'] else '❌'}")
    
    if compatibility['gpu_available']:
        print(f"GPU数量: {compatibility.get('gpu_count', 0)}")
        for i, name in enumerate(compatibility.get('gpu_names', [])):
            print(f"  GPU {i}: {name}")
    
    if compatibility['errors']:
        print("错误信息:")
        for error in compatibility['errors']:
            print(f"  ❌ {error}")
    
    return compatibility

def test_llm_model():
    """测试LLM模型"""
    print("\n=== LLM模型测试 ===")
    
    try:
        # 创建Qwen模型
        print("正在创建Qwen 4B模型...")
        llm = create_llm_model("qwen", model_size="4b")
        print(f"✅ LLM模型创建成功: {llm}")
        
        # 获取模型信息
        print("获取模型信息...")
        model_info = llm.get_model_info()
        print(f"模型信息: {model_info}")
        
        # 测试简单生成
        print("\n测试文本生成...")
        response = llm.generate(
            prompt="你好，请简单介绍一下你自己，不超过50字。",
            temperature=0.1
        )
        print(f"生成结果: {response.content}")
        print(f"响应时间: {response.response_time:.2f}s")
        print(f"Token使用: {response.usage}")
        
        # 测试档案要素提取
        print("\n测试档案要素提取...")
        ocr_text = """上海市人民政府关于印发《上海市深入实施以人为本的新型城镇化战略五年行动计划的实施方案》的通知
印发日期：2024-12-30
发布日期：2024-12-31
沪府发〔2024〕12号
各区人民政府，市政府各委、办、局，各有关单位：
现将《上海市深入实施以人为本的新型城镇化战略五年行动计划的实施方案》印发给你们，请认真按照执行。
上海市人民政府
2024年12月30日
上海市深入实施以人为本的新型城镇化战略五年行动计划的实施方案
为贯彻落实国务院印发的《深入实施以人为本的新型城镇化战略五年行动计划》，扎实推进我市以人为本的新型城镇化战略，结合实际，制定本实施方案。
 一、主要目标
到2028年底，全市城镇化水平保持全国领先，城镇化质量走在全国前列，率先走出一条以人为本、以质为先、城乡融合、区域协同的超大城市新型城镇化道路。人民城市服务保障水平有力提升，农业转移人口融入城市水平进一步提高，均衡可及、便利共享、提质增效的基本公共服务体系进一步健全。超大城市综合承载能力显著增强，市政公用设施提档升级、环境基础设施提级扩能、公共服务设施提质增效同步推进。中心城市辐射带动作用持续放大，深度融合的通勤圈、产业圈、生活圈更好形成，上海大都市圈引领长三角世界级城市群更高水平参与国际竞争。新型城镇化体制机制加快健全完善，城乡融合发展体制机制和政策体系深化健全，城市功能、产业升级、人口集聚良性互动的机制初步形成，人人参与、人人负责、人人奉献、人人共享的城市治理共同体加快构建。
二、实施以人为核心提升城镇化质量行
（一）优化户籍和居住证政策。持续做好人口综合服务和管理，健全覆盖全人群、全生命周期的人口服务体系。完善居住证积分制度，结合经济社会发展需要动态调整积分指标，对居住证持证人在新城、南北转型地区、崇明世界级生态岛等重点区域工作并居住的，给予居住证积分专项加分。赋予重点区域管理机构人才引进重点机构推荐权，推荐额度向重点区域倾斜。支持重点区域制定紧缺急需技能人才职业目录；研究优化在沪稳定居住的城市运行保障服务人员的落户政策。
（二）健全常住地提供基本公共服务制度。优化基本公共服务资源布局，完善家门口服务体系，建立健全按照常住人口配置公共服务资源制度，构建“15分钟社区生活圈”。完善基本公共服务标准体系，结合经济社会发展需要动态调整基本公共服务实施标准，稳步将符合条件的来沪人员纳入基本公共服务保障范围。指导人口集中导入区动态调整优化事业编制资源布局结构，保障基本公共服务发展需要。
（三）完善就业服务体系。面向农业转移人口开展大规模、广覆盖、多形式的职业技能培训，努力提升农业转移人口职业技能、就业创业和融入城市能力。支持各类企业对农业转移人口广泛开展岗位技能提升培训、企业新型学徒制培训等。优化落实定向培训补贴政策，对我市用人单位委托培训实施机构对农民工开展定向培训的，按照培训后学员在我市用人单位就业情况给予定向培训补贴。实施养老护理、家政服务等领域技能人才培养三年行动计划，强化职业培训、完善评价制度、优化补贴政策。持续深入开展“乐业上海优+”“春风行动”“春暖农民工”系列活动，充分发挥已建成“15分钟就业服务圈”站点作用，不断提升就业服务满意度和精准度。
（四）保障随迁子女受教育权利。健全与学龄人口变化相匹配的教育资源保障机制，保障符合条件的来沪人员随迁子女享受各级各类教育的基本权利。全面实施基础教育扩优提质行动计划，促进义务教育优质均衡，加快扩大优质普通高中教育资源供给，办好每一所家门口学校
（五）完善多元化住房保障和供应体系。优化“一张床、一间房、一套房”的多层次租赁住房供应体系，不断满足农业转移人口住房需求。鼓励通过收购、转化用途、盘活闲置存量等方式，加大保障性租赁住房建设筹措供应力度。加快发展新时代城市建设者管理者之家，面向从事城市建设、运行和生活服务保障行业的农业转移人口等群体，五年建设筹措新时代城市建设者管理者之家床位12万张以上。不断完善多主体供给、多渠道保障、租购并举的住房制度，更好满足刚性住房需求和多样化改善性住房需求。探索推动灵活就业人员参加住房公积金制度。
（六）扩大社会保障覆盖面。落实灵活就业人员参加职工基本养老和医疗保险政策，全面取消在就业地参保的户籍限制。引导农业转移人口按照规定参加企业职工基本保险。有序推进居住证积分达到标准分值人员的配偶和在我市就读的子女等参加城乡居民基本医疗保险政策，稳定和扩大异地就医直接结算定点医疗机构数量。深入推进新就业形态人员职业伤害保障试点。
        """ 
        result = llm.extract_archive_elements(
            ocr_text=ocr_text,
            target_fields=["题名", "责任者", "文号", "发文日期"]
        )
        
        print("档案要素提取结果:")
        for field, value in result.items():
            print(f"  {field}: {value}")
        
        # 清理
        llm.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ LLM模型测试失败: {e}")
        logger.exception("LLM模型测试详细错误:")
        return False

def test_embedding_model():
    """测试嵌入模型"""
    print("\n=== 嵌入模型测试 ===")
    
    try:
        # 创建Qwen3嵌入模型
        print("正在创建Qwen3-Embedding-4B模型...")
        embedding = create_embedding_model("qwen", model_size="4b")
        print(f"✅ 嵌入模型创建成功: {embedding}")
        
        # 获取模型信息
        print("获取模型信息...")
        model_info = embedding.get_model_info()
        print(f"模型信息: {model_info}")
        
        # 测试文本编码
        print("\n测试文本编码...")
        texts = [
            "档案管理工作通知",
            "关于加强文件整理的规定",
            "办公室管理制度"
        ]
        
        embeddings = embedding.encode(texts)
        print(f"编码结果形状: {embeddings.shape}")
        print(f"嵌入维度: {embeddings.shape[1]}")
        
        # 测试相似度计算
        print("\n测试相似度计算...")
        similarity = embedding.similarity(texts[0], texts[1])
        print(f"'{texts[0]}' 与 '{texts[1]}' 的相似度: {similarity:.4f}")
        
        # 测试语义搜索
        print("\n测试语义搜索...")
        query = "档案管理"
        documents = [
            "档案管理工作规范",
            "文件整理标准",
            "办公用品采购",
            "档案数字化建设",
            "会议记录模板"
        ]
        
        search_results = embedding.semantic_search(query, documents, top_k=3)
        print(f"查询: '{query}'")
        print("搜索结果:")
        for i, result in enumerate(search_results):
            print(f"  {i+1}. {result['document']} (相似度: {result['similarity']:.4f})")
        
        # 清理
        embedding.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ 嵌入模型测试失败: {e}")
        logger.exception("嵌入模型测试详细错误:")
        return False

def test_model_presets():
    """测试模型预设"""
    print("\n=== 模型预设测试 ===")
    
    print("可用预设:")
    for preset_name, config in MODEL_PRESETS.items():
        print(f"  {preset_name}: {config}")
    
    # 测试从预设创建模型
    try:
        from src.models import create_model_from_preset
        
        # 测试LLM预设
        print("\n测试LLM预设...")
        llm = create_model_from_preset("qwen_3b")
        print(f"✅ 从预设创建LLM成功: {llm}")
        llm.cleanup()
        
        # 测试嵌入预设
        print("\n测试嵌入预设...")
        embedding = create_model_from_preset("qwen_embedding_4b")
        print(f"✅ 从预设创建嵌入模型成功: {embedding}")
        embedding.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ 模型预设测试失败: {e}")
        logger.exception("模型预设测试详细错误:")
        return False

def test_ollama_connection():
    """测试Ollama连接"""
    print("\n=== Ollama连接测试 ===")
    
    try:
        import requests
        
        # 测试基本连接
        print("测试Ollama服务连接...")
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        response.raise_for_status()
        
        models_data = response.json()
        available_models = [model['name'] for model in models_data.get('models', [])]
        
        print(f"✅ Ollama服务连接成功")
        print(f"已安装模型数量: {len(available_models)}")
        
        if available_models:
            print("已安装的模型:")
            for model in available_models[:5]:  # 只显示前5个
                print(f"  - {model}")
            if len(available_models) > 5:
                print(f"  ... 还有 {len(available_models) - 5} 个模型")
        else:
            print("⚠️ 未发现已安装的模型")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Ollama服务")
        print("请确保Ollama已启动: ollama serve")
        return False
    except Exception as e:
        print(f"❌ Ollama连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("Ollama模型功能测试")
    print("=" * 50)
    print(f"项目目录: {project_dir}")
    print(f"当前工作目录: {os.getcwd()}")

    # 兼容性检查
    test_model_compatibility()
    
    # Ollama连接测试
    ollama_ok = test_ollama_connection()
    
    if not ollama_ok:
        print("\n❌ Ollama服务不可用，跳过模型测试")
        print("请启动Ollama服务: ollama serve")
        return
    
    # 测试结果
    test_results = []
    
    # 测试LLM模型
    test_results.append(("LLM模型", test_llm_model()))
    
    # 测试嵌入模型
    test_results.append(("嵌入模型", test_embedding_model()))
    
    # 注释掉模型预设测试，避免自动下载新模型
    # test_results.append(("模型预设", test_model_presets()))
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(test_results)} 个测试通过")
    
    if passed == len(test_results):
        print("🎉 所有测试通过！Ollama模型功能正常。")
    else:
        print("⚠️ 部分测试失败，请检查配置和依赖。")

if __name__ == "__main__":
    main()
