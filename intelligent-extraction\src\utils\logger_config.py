#!/usr/bin/env python3
"""
统一日志配置模块
确保整个项目使用一致的日志格式和级别
"""
import logging
import sys
from pathlib import Path
from typing import Optional

def setup_logging(
    level: str = "INFO",
    format_string: Optional[str] = None,
    log_file: Optional[str] = None,
    force_reconfigure: bool = True
):
    """
    设置统一的日志配置
    
    Args:
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_string: 自定义日志格式
        log_file: 日志文件路径（可选）
        force_reconfigure: 是否强制重新配置日志
    """
    
    # 默认日志格式
    if format_string is None:
        format_string = (
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    # 转换日志级别
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # 如果需要强制重新配置，清除现有的处理器
    if force_reconfigure:
        # 获取根日志器
        root_logger = logging.getLogger()
        
        # 清除所有现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 重新配置
        handlers = []
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_formatter = logging.Formatter(format_string)
        console_handler.setFormatter(console_formatter)
        handlers.append(console_handler)
        
        # 文件处理器（如果指定了日志文件）
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(numeric_level)
            file_formatter = logging.Formatter(format_string)
            file_handler.setFormatter(file_formatter)
            handlers.append(file_handler)
        
        # 配置根日志器
        logging.basicConfig(
            level=numeric_level,
            format=format_string,
            handlers=handlers,
            force=True  # Python 3.8+ 支持force参数
        )
    else:
        # 简单配置
        logging.basicConfig(
            level=numeric_level,
            format=format_string
        )
    
    # 设置第三方库的日志级别，避免过多输出
    _configure_third_party_loggers()
    
    # 记录配置完成
    logger = logging.getLogger(__name__)
    logger.info(f"日志配置完成 - 级别: {level}, 格式: 已设置")
    
    return logging.getLogger()

def _configure_third_party_loggers():
    """配置第三方库的日志级别"""
    
    # PaddlePaddle相关
    logging.getLogger('paddle').setLevel(logging.WARNING)
    logging.getLogger('paddleocr').setLevel(logging.WARNING)
    
    # HTTP相关
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    
    # FastAPI/Uvicorn相关
    logging.getLogger('uvicorn').setLevel(logging.INFO)
    logging.getLogger('uvicorn.access').setLevel(logging.WARNING)
    
    # 其他常见库
    logging.getLogger('PIL').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)

def get_logger(name: str) -> logging.Logger:
    """
    获取配置好的日志器
    
    Args:
        name: 日志器名称，通常使用 __name__
        
    Returns:
        配置好的日志器实例
    """
    return logging.getLogger(name)

def set_log_level(level: str):
    """
    动态设置日志级别
    
    Args:
        level: 新的日志级别
    """
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # 设置根日志器级别
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # 设置所有处理器的级别
    for handler in root_logger.handlers:
        handler.setLevel(numeric_level)
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志级别已更新为: {level}")

def create_project_logger(
    project_name: str = "intelligent-extraction",
    log_dir: Optional[str] = None
) -> logging.Logger:
    """
    为项目创建专用的日志器
    
    Args:
        project_name: 项目名称
        log_dir: 日志目录
        
    Returns:
        项目专用日志器
    """
    
    # 确定日志文件路径
    if log_dir is None:
        log_dir = Path(__file__).parent.parent.parent / "logs"
    else:
        log_dir = Path(log_dir)
    
    log_file = log_dir / f"{project_name}.log"
    
    # 设置日志配置
    setup_logging(
        level="INFO",
        log_file=str(log_file),
        force_reconfigure=True
    )
    
    return logging.getLogger(project_name)

# 自动配置（当模块被导入时）
def auto_configure():
    """自动配置日志（模块导入时调用）"""
    try:
        # 检查是否已经配置过
        root_logger = logging.getLogger()
        if not root_logger.handlers:
            setup_logging(level="INFO", force_reconfigure=True)
    except Exception as e:
        # 如果自动配置失败，使用最基本的配置
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        print(f"日志自动配置失败，使用基本配置: {e}")

# 模块导入时自动配置
auto_configure()
