"""
模型管理器 - 智能加载和管理各种模型
"""
import torch
import logging
import threading
from typing import Dict, Any, Optional, List
from contextlib import contextmanager
from dataclasses import dataclass
from enum import Enum

class ModelStatus(Enum):
    """模型状态"""
    UNLOADED = "unloaded"
    LOADING = "loading"
    LOADED = "loaded"
    ERROR = "error"

@dataclass
class ModelInfo:
    """模型信息"""
    name: str
    model_type: str
    device_id: int
    memory_usage: int  # MB
    status: ModelStatus
    model_instance: Optional[Any] = None
    load_time: Optional[float] = None

class ModelManager:
    """模型管理器"""
    
    def __init__(self, device_manager, config: Dict[str, Any]):
        self.device_manager = device_manager
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 模型注册表
        self.model_registry = {}
        self.loaded_models = {}
        self.model_locks = {}
        
        # 加载策略
        self.load_strategy = config.get('load_strategy', 'lazy')  # lazy, eager, dynamic
        
        self._register_models()
        
        if self.load_strategy == 'eager':
            self._eager_load_models()
    
    def _register_models(self):
        """注册所有可用模型"""
        models_config = self.config.get('models', {})
        self.logger.info(f"开始注册模型，配置: {list(models_config.keys())}")

        # OCR模型
        if 'ocr' in models_config:
            ocr_config = models_config['ocr']
            model_name = ocr_config.get('model_name', 'PPOCRv3')
            # 统一使用小写加下划线的注册名
            self._register_model('ppocrv3', 'ocr', ocr_config)
            self.logger.info(f"已注册OCR模型: ppocrv3 ({model_name})")
        else:
            self.logger.warning("配置中未找到OCR模型配置")

        # 结构分析模型
        if 'structure' in models_config:
            structure_config = models_config['structure']
            self._register_model('pp_structure', 'structure', structure_config)

        # 表格识别模型
        if 'table' in models_config:
            table_config = models_config['table']
            self._register_model('pp_table', 'table', table_config)

        # 嵌入模型
        if 'embedding' in models_config:
            embedding_config = models_config['embedding']
            model_name = embedding_config.get('model_name', 'bge-m3')
            # 根据配置的模型名称决定注册名
            if 'qwen' in model_name.lower():
                self._register_model('qwen_embedding', 'embedding', embedding_config)
            else:
                self._register_model('bge_embedding', 'embedding', embedding_config)

        # LLM模型
        if 'llm' in models_config:
            llm_config = models_config['llm']
            model_name = llm_config.get('model_name', 'qwen3:4b')
            # 根据配置的模型名称决定注册名
            if 'qwen' in model_name.lower():
                self._register_model('qwen_llm', 'llm', llm_config)
            else:
                self._register_model('default_llm', 'llm', llm_config)

        # 印章检测模型（从image_preprocessing配置中获取）
        image_preprocessing_config = self.config.get('image_preprocessing', {})
        if 'stamp_processing' in image_preprocessing_config:
            stamp_config = image_preprocessing_config['stamp_processing']
            if stamp_config.get('enabled', False):
                self._register_model('seal_detection', 'seal_detection', stamp_config)
                self.logger.info("已注册印章检测模型: seal_detection")
            else:
                self.logger.info("印章检测功能未启用，跳过注册")
    
    def _register_model(self, model_name: str, model_type: str, model_config: Dict[str, Any]):
        """注册单个模型"""
        # 根据模型类型确定设备分配
        if model_type == 'llm':
            device_id = self.device_manager.device_allocation['llm_inference']
        else:
            device_id = self.device_manager.device_allocation['document_processing']
        
        model_info = ModelInfo(
            name=model_name,
            model_type=model_type,
            device_id=device_id,
            memory_usage=model_config.get('memory_usage', 1000),  # 默认1GB
            status=ModelStatus.UNLOADED
        )
        
        self.model_registry[model_name] = model_info
        self.model_locks[model_name] = threading.Lock()
        
        self.logger.info(f"注册模型: {model_name} ({model_type}) -> GPU:{device_id}")
    
    def _eager_load_models(self):
        """预加载所有模型"""
        self.logger.info("开始预加载所有模型...")
        
        for model_name in self.model_registry:
            try:
                self.load_model(model_name)
            except Exception as e:
                self.logger.error(f"预加载模型 {model_name} 失败: {e}")
    
    def load_model(self, model_name: str) -> Any:
        """加载指定模型"""
        if model_name not in self.model_registry:
            raise ValueError(f"未注册的模型: {model_name}")
        
        model_info = self.model_registry[model_name]
        
        # 如果已加载，直接返回
        if model_info.status == ModelStatus.LOADED:
            return model_info.model_instance
        
        # 使用锁确保线程安全
        with self.model_locks[model_name]:
            # 双重检查
            if model_info.status == ModelStatus.LOADED:
                return model_info.model_instance
            
            if model_info.status == ModelStatus.LOADING:
                # 等待其他线程加载完成
                while model_info.status == ModelStatus.LOADING:
                    threading.Event().wait(0.1)
                return model_info.model_instance
            
            try:
                model_info.status = ModelStatus.LOADING
                self.logger.info(f"开始加载模型: {model_name}")
                
                # 检查显存是否足够
                self._check_memory_availability(model_info)
                
                # 加载模型
                model_instance = self._load_model_instance(model_info)
                
                # 更新模型信息
                model_info.model_instance = model_instance
                model_info.status = ModelStatus.LOADED
                self.loaded_models[model_name] = model_info
                
                self.logger.info(f"模型加载成功: {model_name}")
                return model_instance
                
            except Exception as e:
                model_info.status = ModelStatus.ERROR
                self.logger.error(f"模型加载失败: {model_name}, 错误: {e}")
                raise
    
    def _check_memory_availability(self, model_info: ModelInfo):
        """检查显存是否足够"""
        device_id = model_info.device_id
        memory_info = self.device_manager.get_memory_info(device_id)
        
        required_memory = model_info.memory_usage
        available_memory = memory_info['free']
        
        if available_memory < required_memory:
            # 尝试清理缓存
            self.device_manager.clear_cache(device_id)
            memory_info = self.device_manager.get_memory_info(device_id)
            available_memory = memory_info['free']
            
            if available_memory < required_memory:
                raise RuntimeError(
                    f"显存不足: 需要 {required_memory}MB, 可用 {available_memory}MB"
                )
    
    def _load_model_instance(self, model_info: ModelInfo) -> Any:
        """加载模型实例"""
        model_type = model_info.model_type
        device_id = model_info.device_id
        
        with torch.cuda.device(device_id):
            if model_type == 'ocr':
                return self._load_ocr_model(model_info)
            elif model_type == 'structure':
                return self._load_structure_model(model_info)
            elif model_type == 'table':
                return self._load_table_model(model_info)
            elif model_type == 'embedding':
                return self._load_embedding_model(model_info)
            elif model_type == 'llm':
                return self._load_llm_model(model_info)
            elif model_type == 'seal_detection':
                return self._load_seal_detection_model(model_info)
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")
    
    def _load_ocr_model(self, model_info: ModelInfo):
        """加载OCR模型"""
        from ..models.ocr_models import PPOCRv3Model

        self.logger.info(f"使用子进程模式加载OCR模型（避免与PyTorch冲突）")
        return PPOCRv3Model(
            device_id=model_info.device_id
        )
    
    def _load_structure_model(self, model_info: ModelInfo):
        """加载结构分析模型"""
        from ..models.structure_models import PPStructureModel
        return PPStructureModel(device_id=model_info.device_id)
    
    def _load_table_model(self, model_info: ModelInfo):
        """加载表格识别模型"""
        from ..models.structure_models import PPTableModel
        return PPTableModel(device_id=model_info.device_id)
    
    def _load_embedding_model(self, model_info: ModelInfo):
        """加载嵌入模型"""
        if model_info.name == 'qwen_embedding':
            from ..models.embedding_models import QwenEmbeddingModel
            return QwenEmbeddingModel(device_id=model_info.device_id)
        elif model_info.name == 'bge_embedding':
            from ..models.embedding_models import BGEEmbeddingModel
            return BGEEmbeddingModel(device_id=model_info.device_id)
        else:
            # 默认使用BGE模型
            from ..models.embedding_models import BGEEmbeddingModel
            return BGEEmbeddingModel(device_id=model_info.device_id)

    def _load_llm_model(self, model_info: ModelInfo):
        """加载LLM模型"""
        if model_info.name == 'qwen_llm':
            from ..models.llm_models import QwenLLMModel
            return QwenLLMModel(device_id=model_info.device_id)
        else:
            # 默认使用Qwen模型
            from ..models.llm_models import QwenLLMModel
            return QwenLLMModel(device_id=model_info.device_id)

    def _load_seal_detection_model(self, model_info: ModelInfo):
        """加载印章检测模型"""
        from ..models.seal_models import create_seal_detection_model

        # 获取印章检测配置（从image_preprocessing配置中获取）
        stamp_config = self.config.get('image_preprocessing', {}).get('stamp_processing', {})
        model_name = stamp_config.get('model_name', 'PP-OCRv4_mobile_seal_det')
        confidence_threshold = stamp_config.get('confidence_threshold', 0.8)

        return create_seal_detection_model(
            model_name=model_name,
            confidence_threshold=confidence_threshold
        )

    def unload_model(self, model_name: str):
        """卸载指定模型"""
        if model_name not in self.loaded_models:
            return
        
        with self.model_locks[model_name]:
            model_info = self.loaded_models[model_name]
            
            # 释放模型
            if hasattr(model_info.model_instance, 'cleanup'):
                model_info.model_instance.cleanup()
            
            del model_info.model_instance
            model_info.model_instance = None
            model_info.status = ModelStatus.UNLOADED
            
            # 从已加载列表中移除
            del self.loaded_models[model_name]
            
            # 清理GPU缓存
            self.device_manager.clear_cache(model_info.device_id)
            
            self.logger.info(f"模型已卸载: {model_name}")
    
    @contextmanager
    def model_context(self, model_names: List[str]):
        """模型上下文管理器"""
        loaded_models = {}
        
        try:
            # 加载所需模型
            for model_name in model_names:
                loaded_models[model_name] = self.load_model(model_name)
            
            yield loaded_models
            
        finally:
            # 根据策略决定是否卸载
            if self.load_strategy == 'dynamic':
                for model_name in model_names:
                    if model_name in self.loaded_models:
                        self.unload_model(model_name)
    
    def get_model(self, model_name: str) -> Any:
        """获取模型实例"""
        return self.load_model(model_name)

    def get_ocr_model(self) -> Any:
        """获取OCR模型"""
        self.logger.info(f"请求OCR模型，已注册模型: {list(self.model_registry.keys())}")
        if 'ppocrv3' not in self.model_registry:
            self.logger.error(f"OCR模型未注册，可用模型: {list(self.model_registry.keys())}")
            raise RuntimeError("OCR模型未注册")
        return self.load_model('ppocrv3')

    def get_embedding_model(self) -> Any:
        """获取嵌入模型"""
        # 动态选择可用的嵌入模型
        if 'qwen_embedding' in self.model_registry:
            return self.load_model('qwen_embedding')
        elif 'bge_embedding' in self.model_registry:
            return self.load_model('bge_embedding')
        else:
            raise RuntimeError("没有可用的嵌入模型")

    def get_llm_model(self) -> Any:
        """获取LLM模型"""
        # 动态选择可用的LLM模型
        if 'qwen_llm' in self.model_registry:
            return self.load_model('qwen_llm')
        elif 'default_llm' in self.model_registry:
            return self.load_model('default_llm')
        else:
            raise RuntimeError("没有可用的LLM模型")

    def get_structure_model(self) -> Any:
        """获取结构分析模型"""
        return self.load_model('pp_structure')

    def get_table_model(self) -> Any:
        """获取表格识别模型"""
        return self.load_model('pp_table')

    def get_seal_detection_model(self) -> Any:
        """获取印章检测模型"""
        if 'seal_detection' not in self.model_registry:
            self.logger.warning("印章检测模型未注册，可能功能未启用")
            return None
        return self.load_model('seal_detection')

    def get_model_status(self) -> Dict[str, Any]:
        """获取所有模型状态"""
        status = {}
        
        for model_name, model_info in self.model_registry.items():
            status[model_name] = {
                'type': model_info.model_type,
                'device_id': model_info.device_id,
                'status': model_info.status.value,
                'memory_usage': model_info.memory_usage,
                'loaded': model_info.status == ModelStatus.LOADED
            }
        
        return status
    
    def cleanup(self):
        """清理所有模型"""
        self.logger.info("开始清理所有模型...")
        
        for model_name in list(self.loaded_models.keys()):
            self.unload_model(model_name)
        
        # 清理所有GPU缓存
        self.device_manager.clear_cache()
        
        self.logger.info("模型清理完成")
    


    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except:
            pass
