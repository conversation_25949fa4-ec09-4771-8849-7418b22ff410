#!/usr/bin/env python3
"""
完整Pipeline测试 - 测试从图片上传到档案要素提取的完整流程
位置: intelligent-extraction/src/test/test_complete_pipeline.py
"""
import os
import sys
import time
import json
import logging
import requests
import threading
from pathlib import Path
from typing import Dict, Any, List, Optional

# 添加项目路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent
project_dir = src_dir.parent
sys.path.insert(0, str(project_dir))

# 导入服务模块
from src.api.web_service import run_web_service, create_web_service

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PipelineTestSuite:
    """完整Pipeline测试套件"""
    
    def __init__(self, 
                 base_url: str = "http://localhost:8080",
                 test_images_dir: str = "test_images",
                 config_path: Optional[str] = None):
        """
        初始化测试套件
        
        Args:
            base_url: FastAPI服务地址
            test_images_dir: 测试图片目录
            config_path: 配置文件路径
        """
        self.base_url = base_url.rstrip('/')
        self.test_images_dir = Path(test_images_dir)
        self.config_path = config_path
        
        # 测试结果
        self.test_results = []
        self.service_thread = None
        self.service_instance = None
        
        # 创建测试图片目录
        self.test_images_dir.mkdir(exist_ok=True)
        
        logger.info(f"Pipeline测试套件初始化完成")
        logger.info(f"服务地址: {self.base_url}")
        logger.info(f"测试图片目录: {self.test_images_dir}")
    
    def start_service(self, timeout: int = 30) -> bool:
        """启动FastAPI服务"""
        logger.info("正在启动FastAPI服务...")
        
        try:
            # 创建服务实例
            self.service_instance = create_web_service(self.config_path)
            
            # 在后台线程启动服务
            def run_service():
                try:
                    self.service_instance.run(host="127.0.0.1", port=8080, log_level="info")
                except Exception as e:
                    logger.error(f"服务启动失败: {e}")
            
            self.service_thread = threading.Thread(target=run_service, daemon=True)
            self.service_thread.start()
            
            # 等待服务启动
            for i in range(timeout):
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=5)
                    if response.status_code == 200:
                        logger.info(f"✅ FastAPI服务启动成功 (耗时: {i+1}s)")
                        return True
                except requests.exceptions.RequestException:
                    pass
                
                time.sleep(1)
                logger.info(f"等待服务启动... ({i+1}/{timeout}s)")
            
            logger.error("❌ FastAPI服务启动超时")
            return False
            
        except Exception as e:
            logger.error(f"❌ 启动服务时发生错误: {e}")
            return False
    
    def stop_service(self):
        """停止FastAPI服务"""
        if self.service_instance:
            try:
                self.service_instance.cleanup()
                logger.info("✅ FastAPI服务已停止")
            except Exception as e:
                logger.error(f"停止服务时发生错误: {e}")
    
    def test_health_check(self) -> Dict[str, Any]:
        """测试健康检查接口"""
        logger.info("=== 测试健康检查接口 ===")
        
        test_result = {
            "test_name": "health_check",
            "success": False,
            "response_time": 0,
            "error": None
        }
        
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/health", timeout=10)
            response_time = time.time() - start_time
            
            test_result["response_time"] = response_time
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    test_result["success"] = True
                    logger.info(f"✅ 健康检查通过 (响应时间: {response_time:.2f}s)")
                else:
                    test_result["error"] = f"健康状态异常: {data}"
            else:
                test_result["error"] = f"HTTP状态码: {response.status_code}"
                
        except Exception as e:
            test_result["error"] = str(e)
            logger.error(f"❌ 健康检查失败: {e}")
        
        self.test_results.append(test_result)
        return test_result
    
    def test_system_status(self) -> Dict[str, Any]:
        """测试系统状态接口"""
        logger.info("=== 测试系统状态接口 ===")
        
        test_result = {
            "test_name": "system_status",
            "success": False,
            "response_time": 0,
            "gpu_info": None,
            "model_status": None,
            "error": None
        }
        
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/status", timeout=30)
            response_time = time.time() - start_time
            
            test_result["response_time"] = response_time
            
            if response.status_code == 200:
                data = response.json()
                test_result["gpu_info"] = data.get("gpu_info")
                test_result["model_status"] = data.get("model_status")
                test_result["success"] = True
                
                logger.info(f"✅ 系统状态获取成功 (响应时间: {response_time:.2f}s)")
                logger.info(f"GPU模式: {data.get('gpu_info', {}).get('mode', 'unknown')}")
                logger.info(f"GPU数量: {data.get('gpu_info', {}).get('gpu_count', 0)}")
                
                # 显示模型状态
                model_status = data.get("model_status", {})
                for model_name, status in model_status.items():
                    loaded = "✅" if status.get("loaded", False) else "❌"
                    logger.info(f"模型 {model_name}: {loaded}")
                    
            else:
                test_result["error"] = f"HTTP状态码: {response.status_code}"
                
        except Exception as e:
            test_result["error"] = str(e)
            logger.error(f"❌ 系统状态获取失败: {e}")
        
        self.test_results.append(test_result)
        return test_result
    
    def test_archive_extraction(self, image_path: str, expected_fields: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """测试档案要素提取（单张图片）"""
        logger.info(f"=== 测试档案要素提取: {image_path} ===")

        test_result = {
            "test_name": "archive_extraction",
            "image_path": image_path,
            "success": False,
            "response_time": 0,
            "extracted_fields": None,
            "accuracy": None,
            "error": None
        }

        try:
            if not os.path.exists(image_path):
                test_result["error"] = f"测试图片不存在: {image_path}"
                logger.error(test_result["error"])
                self.test_results.append(test_result)
                return test_result

            # 准备文件上传
            with open(image_path, 'rb') as f:
                files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}

                start_time = time.time()
                response = requests.post(
                    f"{self.base_url}/extract/archive",
                    files=files,
                    data={'sync': 'true'},
                    timeout=60
                )
                response_time = time.time() - start_time

            test_result["response_time"] = response_time

            if response.status_code == 200:
                data = response.json()

                if data.get("success"):
                    extracted_fields = data.get("results", {})
                    test_result["extracted_fields"] = extracted_fields
                    test_result["success"] = True

                    logger.info(f"✅ 档案要素提取成功 (响应时间: {response_time:.2f}s)")
                    logger.info("提取结果:")
                    for field, value in extracted_fields.items():
                        logger.info(f"  {field}: {value}")

                    # 计算准确性（如果提供了期望结果）
                    if expected_fields:
                        accuracy = self._calculate_accuracy(extracted_fields, expected_fields)
                        test_result["accuracy"] = accuracy
                        logger.info(f"准确率: {accuracy:.2%}")

                else:
                    test_result["error"] = data.get("error", "提取失败")

            else:
                test_result["error"] = f"HTTP状态码: {response.status_code}, 响应: {response.text}"

        except Exception as e:
            test_result["error"] = str(e)
            logger.error(f"❌ 档案要素提取失败: {e}")

        self.test_results.append(test_result)
        return test_result

    def test_archive_folder_extraction(self, folder_path: str, expected_fields: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """测试档案文件夹要素提取（多页档案）"""
        logger.info(f"=== 测试档案文件夹要素提取: {folder_path} ===")

        test_result = {
            "test_name": "archive_folder_extraction",
            "folder_path": folder_path,
            "success": False,
            "response_time": 0,
            "extracted_fields": None,
            "page_count": 0,
            "accuracy": None,
            "error": None
        }

        try:
            if not os.path.exists(folder_path):
                test_result["error"] = f"测试文件夹不存在: {folder_path}"
                logger.error(test_result["error"])
                self.test_results.append(test_result)
                return test_result

            if not os.path.isdir(folder_path):
                test_result["error"] = f"路径不是文件夹: {folder_path}"
                logger.error(test_result["error"])
                self.test_results.append(test_result)
                return test_result

            # 查找图片文件
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
            image_files = []

            for ext in image_extensions:
                image_files.extend(Path(folder_path).glob(f"*{ext}"))
                image_files.extend(Path(folder_path).glob(f"*{ext.upper()}"))

            if not image_files:
                test_result["error"] = f"文件夹中未找到图片文件: {folder_path}"
                logger.error(test_result["error"])
                self.test_results.append(test_result)
                return test_result

            # 按文件名排序
            image_files.sort(key=lambda x: x.name)
            test_result["page_count"] = len(image_files)

            logger.info(f"找到 {len(image_files)} 个图片文件")

            # 准备文件上传
            files = []
            for img_path in image_files:
                with open(img_path, 'rb') as f:
                    files.append(('files', (img_path.name, f.read(), 'image/jpeg')))

            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/extract/archive_folder",
                files=files,
                data={'sync': 'true'},
                timeout=300  # 多页档案需要更长时间
            )
            response_time = time.time() - start_time

            test_result["response_time"] = response_time

            if response.status_code == 200:
                data = response.json()

                if data.get("success"):
                    extracted_fields = data.get("results", {})
                    test_result["extracted_fields"] = extracted_fields
                    test_result["success"] = True

                    logger.info(f"✅ 档案文件夹要素提取成功 (响应时间: {response_time:.2f}s)")
                    logger.info(f"处理页数: {len(image_files)}")
                    logger.info("提取结果:")
                    for field, value in extracted_fields.items():
                        logger.info(f"  {field}: {value}")

                    # 计算准确性（如果提供了期望结果）
                    if expected_fields:
                        accuracy = self._calculate_accuracy(extracted_fields, expected_fields)
                        test_result["accuracy"] = accuracy
                        logger.info(f"准确率: {accuracy:.2%}")

                else:
                    test_result["error"] = data.get("error", "提取失败")

            else:
                test_result["error"] = f"HTTP状态码: {response.status_code}, 响应: {response.text}"

        except Exception as e:
            test_result["error"] = str(e)
            logger.error(f"❌ 档案文件夹要素提取失败: {e}")

        self.test_results.append(test_result)
        return test_result
    
    def _calculate_accuracy(self, extracted: Dict[str, str], expected: Dict[str, str]) -> float:
        """计算提取准确率"""
        if not expected:
            return 0.0
        
        correct = 0
        total = len(expected)
        
        for field, expected_value in expected.items():
            extracted_value = extracted.get(field, "")
            
            # 简单的字符串匹配（可以根据需要改进）
            if expected_value.strip() and extracted_value.strip():
                if expected_value.strip() in extracted_value.strip() or \
                   extracted_value.strip() in expected_value.strip():
                    correct += 1
            elif not expected_value.strip() and not extracted_value.strip():
                correct += 1
        
        return correct / total if total > 0 else 0.0
    
    def run_complete_test(self, test_images: List[Dict[str, Any]]) -> Dict[str, Any]:
        """运行完整测试"""
        logger.info("🚀 开始完整Pipeline测试")
        logger.info("=" * 60)
        
        # 启动服务
        if not self.start_service():
            return {"success": False, "error": "服务启动失败"}
        
        try:
            # 1. 健康检查
            self.test_health_check()
            
            # 2. 系统状态
            self.test_system_status()
            
            # 3. 档案要素提取测试
            for test_item in test_images:
                if test_item.get("type") == "folder":
                    # 档案文件夹测试
                    folder_path = test_item["path"]
                    expected_fields = test_item.get("expected_fields")
                    self.test_archive_folder_extraction(folder_path, expected_fields)
                else:
                    # 单张图片测试
                    image_path = test_item["path"]
                    expected_fields = test_item.get("expected_fields")
                    self.test_archive_extraction(image_path, expected_fields)
            
            # 生成测试报告
            return self._generate_test_report()
            
        finally:
            # 停止服务
            self.stop_service()
    
    def _generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 测试报告")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        
        report = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": passed_tests / total_tests if total_tests > 0 else 0,
            "test_details": self.test_results
        }
        
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试: {passed_tests}")
        logger.info(f"失败测试: {total_tests - passed_tests}")
        logger.info(f"成功率: {report['success_rate']:.2%}")
        
        # 详细结果
        for result in self.test_results:
            status = "✅ 通过" if result["success"] else "❌ 失败"
            response_time = result.get("response_time", 0)
            logger.info(f"{result['test_name']}: {status} (响应时间: {response_time:.2f}s)")
            
            if not result["success"] and result.get("error"):
                logger.info(f"  错误: {result['error']}")
        
        return report


def create_sample_test_images():
    """创建示例测试配置"""
    return [
        {
            "type": "image",
            "path": "test_images/archive_doc_1.jpg",
            "description": "标准公文档案（单张图片）",
            "expected_fields": {
                "题名": "关于加强档案管理工作的通知",
                "责任者": "某某单位",
                "文号": "某字〔2024〕001号",
                "发文日期": "2024-01-15"
            }
        },
        {
            "type": "image",
            "path": "test_images/archive_doc_2.jpg",
            "description": "复杂格式档案（单张图片）",
            "expected_fields": {
                "题名": "",  # 待用户提供真实数据后填写
                "责任者": "",
                "文号": "",
                "发文日期": ""
            }
        },
        {
            "type": "folder",
            "path": "test_images/archive_folder_1",
            "description": "多页档案文件夹测试",
            "expected_fields": {
                "题名": "",  # 待用户提供真实数据后填写
                "责任者": "",
                "文号": "",
                "发文日期": ""
            }
        }
    ]


def main():
    """主测试函数"""
    print("🧪 Intelligent-Extraction 完整Pipeline测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = PipelineTestSuite(
        base_url="http://localhost:8080",
        test_images_dir="test_images"
    )
    
    # 示例测试图片配置
    test_images = create_sample_test_images()
    
    print("\n📋 测试计划:")
    print("1. FastAPI服务启动测试")
    print("2. 健康检查接口测试")
    print("3. 系统状态接口测试")
    print("4. 档案要素提取接口测试（单张图片）")
    print("5. 档案文件夹要素提取接口测试（多页档案）")
    print("\n⚠️  请确保:")
    print("- Ollama服务已启动 (ollama serve)")
    print("- 已下载Qwen3:4b和Qwen3-Embedding-4B模型")
    print("- 测试图片已放置在test_images目录中")
    print("- 多页档案测试文件夹已放置在test_images目录中")
    
    input("\n按Enter键开始测试...")
    
    try:
        # 运行完整测试
        report = test_suite.run_complete_test(test_images)
        
        # 保存测试报告
        report_file = f"test_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 测试报告已保存到: {report_file}")
        
        if report.get("success_rate", 0) >= 0.8:
            print("🎉 测试基本通过！Pipeline工作正常。")
        else:
            print("⚠️ 测试存在问题，请检查错误信息。")
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
