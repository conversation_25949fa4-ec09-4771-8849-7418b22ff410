"""
结构分析模型封装 - 基于PaddleOCR的结构分析和表格识别
"""
import os
import logging
import numpy as np
from typing import List, Dict, Any, Tuple, Optional, Union
from dataclasses import dataclass
from pathlib import Path


@dataclass
class StructureResult:
    """结构分析结果"""
    layout_type: str  # 布局类型：text, title, figure, table等
    bbox: List[int]   # 边界框 [x1, y1, x2, y2]
    confidence: float
    content: Optional[str] = None


@dataclass
class TableResult:
    """表格识别结果"""
    html: str         # 表格HTML
    cells: List[Dict] # 单元格信息
    bbox: List[int]   # 表格边界框
    confidence: float


class PPStructureModel:
    """PaddleOCR结构分析模型封装"""
    
    def __init__(self, 
                 use_gpu: bool = True,
                 gpu_id: int = 0,
                 device_id: int = 0,
                 **kwargs):
        """
        初始化PP-Structure模型
        
        Args:
            use_gpu: 是否使用GPU
            gpu_id: GPU设备ID
            device_id: 设备ID（兼容性参数）
            **kwargs: 其他参数
        """
        self.use_gpu = use_gpu
        self.gpu_id = gpu_id if gpu_id is not None else device_id
        self.device_id = device_id
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.layout_model_dir = kwargs.get("layout_model_dir", None)
        self.table_model_dir = kwargs.get("table_model_dir", None)
        
        # 初始化结构分析引擎
        self._init_structure_engine()
    
    def _init_structure_engine(self):
        """初始化结构分析引擎"""
        try:
            # 延迟导入PaddleOCR，避免与PyTorch冲突
            from paddleocr import PPStructure
            
            # 设置GPU
            if self.use_gpu:
                os.environ['CUDA_VISIBLE_DEVICES'] = str(self.gpu_id)
            
            # 初始化PP-Structure
            structure_kwargs = {
                'use_gpu': self.use_gpu,
                'show_log': False,  # 减少日志输出
                'lang': 'ch',
                'layout': True,
                'table': True,
                'ocr': True
            }
            
            # 添加自定义模型路径
            if self.layout_model_dir:
                structure_kwargs['layout_model_dir'] = self.layout_model_dir
            if self.table_model_dir:
                structure_kwargs['table_model_dir'] = self.table_model_dir
            
            self.structure_engine = PPStructure(**structure_kwargs)
            self.logger.info(f"PP-Structure初始化成功: gpu={self.use_gpu}")
            
        except Exception as e:
            self.logger.error(f"PP-Structure初始化失败: {e}")
            # 创建一个简单的fallback实现
            self.structure_engine = None
            self.logger.warning("使用fallback结构分析实现")
    
    def analyze(self, 
                image: Union[str, np.ndarray, Path],
                confidence_threshold: float = 0.5) -> List[StructureResult]:
        """
        分析图像结构
        
        Args:
            image: 图像路径或numpy数组
            confidence_threshold: 置信度阈值
            
        Returns:
            List[StructureResult]: 结构分析结果
        """
        try:
            if self.structure_engine is None:
                # Fallback实现
                return self._fallback_analyze(image)
            
            # 预处理图像
            img_array = self._preprocess_image(image)
            
            # 执行结构分析
            structure_results = self.structure_engine(img_array)
            
            # 处理结果
            results = []
            for result in structure_results:
                if 'type' in result and 'bbox' in result:
                    layout_type = result['type']
                    bbox = result['bbox']
                    confidence = result.get('confidence', 1.0)
                    content = result.get('res', None)
                    
                    if confidence >= confidence_threshold:
                        results.append(StructureResult(
                            layout_type=layout_type,
                            bbox=bbox,
                            confidence=confidence,
                            content=str(content) if content else None
                        ))
            
            self.logger.info(f"结构分析完成: {len(results)}个区域")
            return results
            
        except Exception as e:
            self.logger.error(f"结构分析失败: {e}")
            return self._fallback_analyze(image)
    
    def _fallback_analyze(self, image: Union[str, np.ndarray, Path]) -> List[StructureResult]:
        """Fallback结构分析实现"""
        try:
            # 简单的fallback：将整个图像作为文本区域
            img_array = self._preprocess_image(image)
            height, width = img_array.shape[:2]
            
            return [StructureResult(
                layout_type="text",
                bbox=[0, 0, width, height],
                confidence=0.8,
                content=None
            )]
        except Exception as e:
            self.logger.error(f"Fallback结构分析失败: {e}")
            return []
    
    def _preprocess_image(self, image: Union[str, np.ndarray, Path]) -> np.ndarray:
        """预处理图像"""
        try:
            import cv2
            
            if isinstance(image, (str, Path)):
                image_path = str(image)
                if not os.path.exists(image_path):
                    raise FileNotFoundError(f"图像文件不存在: {image_path}")
                
                img_array = cv2.imread(image_path)
                if img_array is None:
                    raise ValueError(f"无法读取图像文件: {image_path}")
            
            elif isinstance(image, np.ndarray):
                img_array = image.copy()
            else:
                raise ValueError(f"不支持的图像类型: {type(image)}")
            
            return img_array
            
        except Exception as e:
            self.logger.error(f"图像预处理失败: {e}")
            raise
    
    def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self, 'structure_engine') and self.structure_engine:
                del self.structure_engine
            self.logger.info("PP-Structure模型清理完成")
        except Exception as e:
            self.logger.error(f"PP-Structure清理失败: {e}")


class PPTableModel:
    """PaddleOCR表格识别模型封装"""
    
    def __init__(self, 
                 use_gpu: bool = True,
                 gpu_id: int = 0,
                 device_id: int = 0,
                 **kwargs):
        """
        初始化PP-Table模型
        
        Args:
            use_gpu: 是否使用GPU
            gpu_id: GPU设备ID
            device_id: 设备ID（兼容性参数）
            **kwargs: 其他参数
        """
        self.use_gpu = use_gpu
        self.gpu_id = gpu_id if gpu_id is not None else device_id
        self.device_id = device_id
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.table_model_dir = kwargs.get("table_model_dir", None)
        
        # 初始化表格识别引擎
        self._init_table_engine()
    
    def _init_table_engine(self):
        """初始化表格识别引擎"""
        try:
            # 延迟导入PaddleOCR
            from paddleocr import PPStructure
            
            # 设置GPU
            if self.use_gpu:
                os.environ['CUDA_VISIBLE_DEVICES'] = str(self.gpu_id)
            
            # 初始化表格识别
            table_kwargs = {
                'use_gpu': self.use_gpu,
                'show_log': False,
                'lang': 'ch',
                'layout': False,  # 只做表格识别
                'table': True,
                'ocr': True
            }
            
            if self.table_model_dir:
                table_kwargs['table_model_dir'] = self.table_model_dir
            
            self.table_engine = PPStructure(**table_kwargs)
            self.logger.info(f"PP-Table初始化成功: gpu={self.use_gpu}")
            
        except Exception as e:
            self.logger.error(f"PP-Table初始化失败: {e}")
            self.table_engine = None
            self.logger.warning("使用fallback表格识别实现")
    
    def recognize(self, 
                  image: Union[str, np.ndarray, Path],
                  confidence_threshold: float = 0.5) -> List[TableResult]:
        """
        识别表格
        
        Args:
            image: 图像路径或numpy数组
            confidence_threshold: 置信度阈值
            
        Returns:
            List[TableResult]: 表格识别结果
        """
        try:
            if self.table_engine is None:
                return self._fallback_recognize(image)
            
            # 预处理图像
            img_array = self._preprocess_image(image)
            
            # 执行表格识别
            table_results = self.table_engine(img_array)
            
            # 处理结果
            results = []
            for result in table_results:
                if result.get('type') == 'table':
                    bbox = result.get('bbox', [0, 0, 100, 100])
                    confidence = result.get('confidence', 1.0)
                    table_res = result.get('res', {})
                    
                    if confidence >= confidence_threshold:
                        html = table_res.get('html', '')
                        cells = table_res.get('cells', [])
                        
                        results.append(TableResult(
                            html=html,
                            cells=cells,
                            bbox=bbox,
                            confidence=confidence
                        ))
            
            self.logger.info(f"表格识别完成: {len(results)}个表格")
            return results
            
        except Exception as e:
            self.logger.error(f"表格识别失败: {e}")
            return self._fallback_recognize(image)
    
    def _fallback_recognize(self, image: Union[str, np.ndarray, Path]) -> List[TableResult]:
        """Fallback表格识别实现"""
        try:
            # 简单的fallback：返回空表格结果
            return []
        except Exception as e:
            self.logger.error(f"Fallback表格识别失败: {e}")
            return []
    
    def _preprocess_image(self, image: Union[str, np.ndarray, Path]) -> np.ndarray:
        """预处理图像"""
        try:
            import cv2
            
            if isinstance(image, (str, Path)):
                image_path = str(image)
                if not os.path.exists(image_path):
                    raise FileNotFoundError(f"图像文件不存在: {image_path}")
                
                img_array = cv2.imread(image_path)
                if img_array is None:
                    raise ValueError(f"无法读取图像文件: {image_path}")
            
            elif isinstance(image, np.ndarray):
                img_array = image.copy()
            else:
                raise ValueError(f"不支持的图像类型: {type(image)}")
            
            return img_array
            
        except Exception as e:
            self.logger.error(f"图像预处理失败: {e}")
            raise
    
    def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self, 'table_engine') and self.table_engine:
                del self.table_engine
            self.logger.info("PP-Table模型清理完成")
        except Exception as e:
            self.logger.error(f"PP-Table清理失败: {e}")
