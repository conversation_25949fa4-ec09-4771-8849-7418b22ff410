"""
融合处理器 - 负责多模态信息融合
"""
import logging
from typing import Dict, Any, List


class FusionProcessor:
    """融合处理器"""
    
    def __init__(self, device_manager, model_manager):
        self.device_manager = device_manager
        self.model_manager = model_manager
        self.logger = logging.getLogger(__name__)
        
    def fuse_information(self, key: str, structured_info: Dict[str, Any], 
                        relevant_context: List[Dict[str, Any]], options: Dict[str, Any]) -> Dict[str, Any]:
        """
        融合结构化信息和相关上下文
        
        Args:
            key: 提取的关键词
            structured_info: 结构化文档信息
            relevant_context: 相关上下文列表
            options: 融合选项
            
        Returns:
            融合后的信息
        """
        try:
            self.logger.info(f"开始信息融合，关键词: {key}")
            
            # 处理多页档案情况
            if options.get('is_multi_page', False):
                return self._fuse_multi_page_information(key, structured_info, relevant_context, options)
            
            # 单页档案融合
            return self._fuse_single_page_information(key, structured_info, relevant_context, options)
            
        except Exception as e:
            self.logger.error(f"信息融合失败: {e}")
            raise
    
    def _fuse_single_page_information(self, key: str, structured_info: Dict[str, Any], 
                                    relevant_context: List[Dict[str, Any]], options: Dict[str, Any]) -> Dict[str, Any]:
        """融合单页信息"""
        # 提取基础信息
        document_text = structured_info.get('text', '')
        document_path = structured_info.get('document_path', '')
        
        # 提取相关上下文文本
        context_texts = []
        context_scores = []
        for ctx in relevant_context:
            if isinstance(ctx, dict):
                context_texts.append(ctx.get('text', ''))
                context_scores.append(ctx.get('similarity', 0.0))
        
        # 构建融合信息
        fused_info = {
            'key': key,
            'document_path': document_path,
            'full_text': document_text,
            'relevant_contexts': context_texts,
            'context_scores': context_scores,
            'fusion_strategy': 'single_page',
            'options': options
        }
        
        # 添加OCR结果（如果存在）
        if 'ocr_result' in structured_info:
            fused_info['ocr_result'] = structured_info['ocr_result']
        
        return fused_info
    
    def _fuse_multi_page_information(self, key: str, structured_info: Dict[str, Any], 
                                   relevant_context: Any, options: Dict[str, Any]) -> Dict[str, Any]:
        """融合多页信息"""
        self.logger.info(f"融合多页档案信息，关键词: {key}")
        
        # 处理多页结构化信息
        if 'pages' in structured_info:
            # 多页信息已经合并
            all_texts = []
            all_ocr_results = []
            
            for page_info in structured_info['pages']:
                if 'text' in page_info:
                    all_texts.append(page_info['text'])
                if 'ocr_result' in page_info:
                    all_ocr_results.append(page_info['ocr_result'])
            
            combined_text = structured_info.get('combined_text', '\n\n'.join(all_texts))
            combined_ocr = structured_info.get('combined_ocr_result', all_ocr_results)
        else:
            # 单页信息，按单页处理
            combined_text = structured_info.get('text', '')
            combined_ocr = structured_info.get('ocr_result', [])
        
        # 处理多页相关上下文
        all_context_texts = []
        all_context_scores = []
        
        if isinstance(relevant_context, dict) and 'multi_page_contexts' in relevant_context:
            # 多页上下文
            for page_contexts in relevant_context['multi_page_contexts']:
                if isinstance(page_contexts, list):
                    for ctx in page_contexts:
                        if isinstance(ctx, dict):
                            all_context_texts.append(ctx.get('text', ''))
                            all_context_scores.append(ctx.get('similarity', 0.0))
        elif isinstance(relevant_context, list):
            # 单页上下文列表
            for ctx in relevant_context:
                if isinstance(ctx, dict):
                    all_context_texts.append(ctx.get('text', ''))
                    all_context_scores.append(ctx.get('similarity', 0.0))
        
        # 构建多页融合信息
        fused_info = {
            'key': key,
            'full_text': combined_text,
            'relevant_contexts': all_context_texts,
            'context_scores': all_context_scores,
            'fusion_strategy': 'multi_page',
            'page_count': options.get('page_count', 1),
            'is_multi_page': True,
            'options': options
        }
        
        # 添加OCR结果
        if combined_ocr:
            fused_info['ocr_result'] = combined_ocr
        
        # 添加页面信息（如果存在）
        if 'pages' in structured_info:
            fused_info['page_info'] = structured_info['pages']
        
        self.logger.info(f"多页信息融合完成，页数: {options.get('page_count', 1)}, 上下文数: {len(all_context_texts)}")
        return fused_info
    
    def enhance_context_relevance(self, fused_info: Dict[str, Any], key: str) -> Dict[str, Any]:
        """增强上下文相关性"""
        try:
            # 根据关键词过滤和排序上下文
            contexts = fused_info.get('relevant_contexts', [])
            scores = fused_info.get('context_scores', [])
            
            if not contexts or not scores:
                return fused_info
            
            # 按相似度排序
            context_pairs = list(zip(contexts, scores))
            context_pairs.sort(key=lambda x: x[1], reverse=True)
            
            # 取前N个最相关的上下文
            max_contexts = 5
            top_contexts = context_pairs[:max_contexts]
            
            fused_info['relevant_contexts'] = [ctx for ctx, _ in top_contexts]
            fused_info['context_scores'] = [score for _, score in top_contexts]
            
            return fused_info
            
        except Exception as e:
            self.logger.warning(f"上下文相关性增强失败: {e}")
            return fused_info
