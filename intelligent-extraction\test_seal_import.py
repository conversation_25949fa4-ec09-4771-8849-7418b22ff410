#!/usr/bin/env python3
"""
测试PaddleOCR印章检测功能导入
"""

import sys
import os

def test_paddleocr_imports():
    """测试PaddleOCR相关导入"""
    print("🔍 测试PaddleOCR导入...")
    
    try:
        from paddleocr import PaddleOCR
        print("✅ PaddleOCR 导入成功")
    except ImportError as e:
        print(f"❌ PaddleOCR 导入失败: {e}")
        return False
    
    try:
        from paddleocr import SealTextDetection
        print("✅ SealTextDetection 导入成功")
        seal_available = True
    except ImportError as e:
        print(f"❌ SealTextDetection 导入失败: {e}")
        seal_available = False
    
    # 测试创建SealTextDetection实例
    if seal_available:
        try:
            print("🔍 测试创建SealTextDetection实例...")
            seal_detector = SealTextDetection(
                model_name="PP-OCRv4_mobile_seal_det",
                device='cpu'
            )
            print("✅ SealTextDetection 实例创建成功")
            return True
        except Exception as e:
            print(f"❌ SealTextDetection 实例创建失败: {e}")
            return False
    
    return False

def test_paddleocr_version():
    """测试PaddleOCR版本"""
    try:
        import paddleocr
        version = getattr(paddleocr, '__version__', 'Unknown')
        print(f"📦 PaddleOCR 版本: {version}")
        
        # 检查是否有印章检测相关的模块
        import inspect
        members = inspect.getmembers(paddleocr)
        seal_related = [name for name, obj in members if 'seal' in name.lower()]
        
        if seal_related:
            print(f"🔖 发现印章相关模块: {seal_related}")
        else:
            print("⚠️  未发现印章相关模块")
            
    except Exception as e:
        print(f"❌ 版本检查失败: {e}")

def main():
    """主函数"""
    print("🚀 开始PaddleOCR印章检测导入测试")
    print("=" * 50)
    
    # 测试版本信息
    test_paddleocr_version()
    print()
    
    # 测试导入
    success = test_paddleocr_imports()
    
    print()
    print("=" * 50)
    if success:
        print("🎉 印章检测功能可用！")
    else:
        print("⚠️  印章检测功能不可用")
        print("💡 可能的解决方案:")
        print("   1. 确保PaddleOCR版本支持印章检测")
        print("   2. 检查是否需要额外安装印章检测模块")
        print("   3. 尝试更新PaddleOCR到最新版本")

if __name__ == "__main__":
    main()
