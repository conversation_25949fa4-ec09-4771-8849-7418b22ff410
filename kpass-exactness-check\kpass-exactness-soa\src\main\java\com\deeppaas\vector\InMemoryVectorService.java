package com.deeppaas.vector;

import com.deeppaas.paddleocr.PaddleOCRv3Service;
import com.deeppaas.paddleocr.model.PPStructureV3Result;
import com.deeppaas.vector.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.awt.Rectangle;
import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 简化的内存向量服务
 * 专门针对档案卷处理，无需持久化存储
 */
@Component
@Slf4j
public class InMemoryVectorService {
    
    @Autowired
    private PaddleOCRv3Service paddleOcrService;

    @Autowired
    private MemoryOptimizedEmbeddingService embeddingService;
    
    // 内存中的向量存储
    private final Map<String, ArchiveVolumeVectors> volumeVectorsMap = new ConcurrentHashMap<>();
    
    // 线程池用于并行处理
    private final ExecutorService executorService = Executors.newFixedThreadPool(
        Runtime.getRuntime().availableProcessors());
    
    /**
     * 为一卷档案构建内存向量索引
     */
    public String buildVolumeVectors(String volumeId, List<File> documentFiles) {
        try {
            log.info("开始为卷 {} 构建内存向量，文档数量: {}", volumeId, documentFiles.size());
            long startTime = System.currentTimeMillis();
            
            // 1. 并行解析所有文档
            List<DocumentTextInfo> documentTexts = parseDocumentsConcurrently(documentFiles);
            
            // 2. 构建文本向量
            ArchiveVolumeVectors volumeVectors = buildTextVectors(volumeId, documentTexts);
            
            // 3. 存储到内存
            volumeVectorsMap.put(volumeId, volumeVectors);
            
            long endTime = System.currentTimeMillis();
            log.info("卷 {} 向量构建完成，耗时: {}ms，文本段数: {}", 
                volumeId, endTime - startTime, volumeVectors.getTextSegments().size());
            
            return volumeId;
            
        } catch (Exception e) {
            log.error("构建卷向量失败: {}", volumeId, e);
            throw new RuntimeException("向量构建失败", e);
        }
    }
    
    /**
     * 并行解析文档
     */
    private List<DocumentTextInfo> parseDocumentsConcurrently(List<File> documentFiles) {
        List<CompletableFuture<DocumentTextInfo>> futures = new ArrayList<>();
        
        for (File file : documentFiles) {
            CompletableFuture<DocumentTextInfo> future = CompletableFuture.supplyAsync(() -> {
                try {
                    PPStructureV3Result result = paddleOcrService.parseDocument(file);
                    return extractTextInfo(file.getName(), result);
                } catch (Exception e) {
                    log.error("解析文档失败: {}", file.getName(), e);
                    return null;
                }
            }, executorService);
            
            futures.add(future);
        }
        
        // 等待所有解析完成
        return futures.stream()
            .map(CompletableFuture::join)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }
    
    /**
     * 提取文档文本信息
     */
    private DocumentTextInfo extractTextInfo(String fileName, PPStructureV3Result structureResult) {
        DocumentTextInfo docInfo = new DocumentTextInfo();
        docInfo.setFileName(fileName);
        docInfo.setTextSegments(new ArrayList<>());
        
        // 提取文本段落
        for (PPStructureV3Result.LayoutRegion region : structureResult.getLayoutRegions()) {
            if ("text".equals(region.getType()) || "title".equals(region.getType())) {
                TextSegment segment = new TextSegment();
                segment.setText(region.getContent());
                segment.setType(region.getType());
                segment.setBoundingBox(region.getBbox());
                segment.setConfidence(region.getConfidence());
                segment.setFileName(fileName);
                
                docInfo.getTextSegments().add(segment);
            }
        }
        
        // 构建完整文本
        String fullText = docInfo.getTextSegments().stream()
            .map(TextSegment::getText)
            .collect(Collectors.joining("\n"));
        docInfo.setFullText(fullText);
        
        return docInfo;
    }
    
    /**
     * 构建文本向量
     */
    private ArchiveVolumeVectors buildTextVectors(String volumeId, List<DocumentTextInfo> documentTexts) {
        ArchiveVolumeVectors volumeVectors = new ArchiveVolumeVectors();
        volumeVectors.setVolumeId(volumeId);
        volumeVectors.setTextSegments(new ArrayList<>());
        volumeVectors.setVectorIndex(new HashMap<>());
        
        int segmentIndex = 0;
        
        for (DocumentTextInfo docInfo : documentTexts) {
            for (TextSegment segment : docInfo.getTextSegments()) {
                try {
                    // 调用embedding服务
                    float[] embedding = getTextEmbedding(segment.getText());
                    
                    // 创建向量化文本段
                    VectorizedTextSegment vectorSegment = new VectorizedTextSegment();
                    vectorSegment.setSegmentId(volumeId + "_" + segmentIndex);
                    vectorSegment.setText(segment.getText());
                    vectorSegment.setEmbedding(embedding);
                    vectorSegment.setType(segment.getType());
                    vectorSegment.setBoundingBox(segment.getBoundingBox());
                    vectorSegment.setConfidence(segment.getConfidence());
                    vectorSegment.setFileName(segment.getFileName());
                    
                    volumeVectors.getTextSegments().add(vectorSegment);
                    volumeVectors.getVectorIndex().put(vectorSegment.getSegmentId(), segmentIndex);
                    
                    segmentIndex++;
                    
                } catch (Exception e) {
                    log.warn("文本向量化失败: {}", segment.getText().substring(0, Math.min(50, segment.getText().length())), e);
                }
            }
        }
        
        volumeVectors.setCreatedTime(LocalDateTime.now());
        return volumeVectors;
    }
    
    /**
     * 获取文本embedding
     */
    private float[] getTextEmbedding(String text) {
        try {
            // 使用优化的embedding服务
            return embeddingService.getTextEmbedding(text);

        } catch (Exception e) {
            log.error("获取文本embedding失败", e);
            // 返回零向量作为降级方案
            return new float[256]; // 使用轻量级向量维度
        }
    }
    
    /**
     * 在卷中搜索相似文本
     */
    public List<SimilarTextSegment> searchSimilarTexts(String volumeId, String queryText, int topK) {
        ArchiveVolumeVectors volumeVectors = volumeVectorsMap.get(volumeId);
        if (volumeVectors == null) {
            log.warn("未找到卷向量: {}", volumeId);
            return new ArrayList<>();
        }
        
        try {
            // 获取查询文本的向量
            float[] queryVector = getTextEmbedding(queryText);
            
            // 计算与所有文本段的相似度
            List<SimilarTextSegment> similarities = new ArrayList<>();
            
            for (VectorizedTextSegment segment : volumeVectors.getTextSegments()) {
                double similarity = calculateCosineSimilarity(queryVector, segment.getEmbedding());
                
                SimilarTextSegment similar = new SimilarTextSegment();
                similar.setSegmentId(segment.getSegmentId());
                similar.setText(segment.getText());
                similar.setType(segment.getType());
                similar.setFileName(segment.getFileName());
                similar.setSimilarity(similarity);
                similar.setBoundingBox(segment.getBoundingBox());
                
                similarities.add(similar);
            }
            
            // 按相似度排序并返回topK
            return similarities.stream()
                .sorted((a, b) -> Double.compare(b.getSimilarity(), a.getSimilarity()))
                .limit(topK)
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            log.error("搜索相似文本失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取卷的所有文本内容（用于构建上下文）
     */
    public String getVolumeContext(String volumeId, int maxLength) {
        ArchiveVolumeVectors volumeVectors = volumeVectorsMap.get(volumeId);
        if (volumeVectors == null) {
            return "";
        }
        
        StringBuilder context = new StringBuilder();
        
        // 按文件名和位置排序
        List<VectorizedTextSegment> sortedSegments = volumeVectors.getTextSegments().stream()
            .sorted((a, b) -> {
                int fileCompare = a.getFileName().compareTo(b.getFileName());
                if (fileCompare != 0) return fileCompare;
                
                // 按Y坐标排序（从上到下）
                Rectangle bboxA = a.getBoundingBox();
                Rectangle bboxB = b.getBoundingBox();
                return Integer.compare(bboxA.y, bboxB.y);
            })
            .collect(Collectors.toList());
        
        for (VectorizedTextSegment segment : sortedSegments) {
            if (context.length() + segment.getText().length() > maxLength) {
                break;
            }
            
            context.append("[").append(segment.getFileName()).append("] ");
            context.append(segment.getText()).append("\n");
        }
        
        return context.toString();
    }
    
    /**
     * 清理卷向量（处理完成后调用）
     */
    public void clearVolumeVectors(String volumeId) {
        ArchiveVolumeVectors removed = volumeVectorsMap.remove(volumeId);
        if (removed != null) {
            log.info("已清理卷 {} 的向量数据，释放内存", volumeId);
        }
    }
    
    /**
     * 计算余弦相似度
     */
    private double calculateCosineSimilarity(float[] vector1, float[] vector2) {
        if (vector1.length != vector2.length) {
            return 0.0;
        }
        
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;
        
        for (int i = 0; i < vector1.length; i++) {
            dotProduct += vector1[i] * vector2[i];
            norm1 += vector1[i] * vector1[i];
            norm2 += vector2[i] * vector2[i];
        }
        
        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }
        
        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }
    
    @PreDestroy
    public void cleanup() {
        executorService.shutdown();
        volumeVectorsMap.clear();
        log.info("内存向量服务已清理");
    }
}
