package com.toolbox.extraction.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.List;

/**
 * 提取结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExtractionResult {
    private boolean success;
    private String taskId;
    private Map<String, Object> results;
    private Double processingTime;
    private String message;
    private String error;
    
    // 成功结果构造器
    public ExtractionResult(boolean success, Map<String, Object> results, Double processingTime) {
        this.success = success;
        this.results = results;
        this.processingTime = processingTime;
    }
    
    // 任务提交结果构造器
    public ExtractionResult(String taskId, String message) {
        this.success = true;
        this.taskId = taskId;
        this.message = message;
    }
    
    // 错误结果构造器
    public ExtractionResult(String error) {
        this.success = false;
        this.error = error;
    }
}

/**
 * 提取响应（对应Python API响应）
 */
@Data
@NoArgsConstructor
public class ExtractionResponse {
    private boolean success;
    
    @JsonProperty("task_id")
    private String taskId;
    
    private Map<String, Object> results;
    private String error;
    
    @JsonProperty("processing_time")
    private Double processingTime;
}

/**
 * 任务状态
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskStatus {
    private String taskId;
    private String status; // pending, processing, completed, failed, cancelled
    private Double progress; // 0-100
    private String error;
    
    public boolean isCompleted() {
        return "completed".equals(status);
    }
    
    public boolean isFailed() {
        return "failed".equals(status);
    }
    
    public boolean isProcessing() {
        return "processing".equals(status);
    }
}

/**
 * 任务状态响应
 */
@Data
@NoArgsConstructor
public class TaskStatusResponse {
    @JsonProperty("task_id")
    private String taskId;
    
    private String status;
    private Map<String, Object> results;
    private String error;
    private Double progress;
}

/**
 * 系统状态
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SystemStatus {
    private String status;
    private Map<String, Object> gpuInfo;
    private Map<String, Object> modelStatus;
    private Map<String, Object> performanceStats;
    
    public boolean isHealthy() {
        return "running".equals(status);
    }
    
    public String getGpuMode() {
        if (gpuInfo != null) {
            return (String) gpuInfo.get("mode");
        }
        return "unknown";
    }
    
    public Integer getGpuCount() {
        if (gpuInfo != null) {
            return (Integer) gpuInfo.get("gpu_count");
        }
        return 0;
    }
}

/**
 * 系统状态响应
 */
@Data
@NoArgsConstructor
public class SystemStatusResponse {
    private String status;
    
    @JsonProperty("gpu_info")
    private Map<String, Object> gpuInfo;
    
    @JsonProperty("model_status")
    private Map<String, Object> modelStatus;
    
    @JsonProperty("performance_stats")
    private Map<String, Object> performanceStats;
}

/**
 * 档案要素
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ArchiveElements {
    private String title;        // 题名
    private String responsible;  // 责任者
    private String documentNo;   // 文号
    private String issueDate;    // 发文日期
    
    // 从提取结果构建
    public static ArchiveElements fromExtractionResult(Map<String, Object> results) {
        ArchiveElements elements = new ArchiveElements();
        
        if (results != null) {
            elements.setTitle(getStringValue(results, "题名"));
            elements.setResponsible(getStringValue(results, "责任者"));
            elements.setDocumentNo(getStringValue(results, "文号"));
            elements.setIssueDate(getStringValue(results, "发文日期"));
        }
        
        return elements;
    }
    
    private static String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }
    
    public boolean isComplete() {
        return title != null && responsible != null && 
               documentNo != null && issueDate != null;
    }
}

/**
 * 提取选项
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExtractionOptions {
    private Double confidenceThreshold = 0.7;
    private Integer maxRetries = 2;
    private Boolean enableMultiStrategy = true;
    private String domain = "archive";
    private Boolean useArchiveRules = true;
    private Boolean enableDateParsing = true;
    private Boolean enableDocumentNumberParsing = true;
    
    public Map<String, Object> toMap() {
        return Map.of(
            "confidence_threshold", confidenceThreshold,
            "max_retries", maxRetries,
            "enable_multi_strategy", enableMultiStrategy,
            "domain", domain,
            "use_archive_rules", useArchiveRules,
            "enable_date_parsing", enableDateParsing,
            "enable_document_number_parsing", enableDocumentNumberParsing
        );
    }
}

/**
 * 提取异常
 */
public class ExtractionException extends RuntimeException {
    public ExtractionException(String message) {
        super(message);
    }
    
    public ExtractionException(String message, Throwable cause) {
        super(message, cause);
    }
}
