#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
档案要素提取服务 - 优化版本，使用模型池和并发处理
"""
import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import json

from ..core.model_pool import get_model_pool
from ..models.ocr_models import OCRResult


class ArchiveExtractionService:
    """档案要素提取服务 - 优化版本"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.model_pool = get_model_pool(config_path)
        
        # 线程池用于并发处理
        self.executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="ArchiveExtraction")
        
        # 默认要提取的档案要素
        self.default_elements = ["题名", "责任者", "文号", "发文日期"]

        # 保存最后一次OCR结果（用于调试和响应构建）
        self._last_ocr_result = None

        self.logger.info("档案要素提取服务初始化完成")
    
    async def extract_from_image(self,
                               image_path: Union[str, Path],
                               elements: Optional[List[str]] = None,
                               confidence_threshold: float = 0.5,
                               enable_stamp_processing: bool = False,
                               stamp_confidence_threshold: float = 0.8) -> Dict[str, Any]:
        """
        从图像中提取档案要素

        Args:
            image_path: 图像文件路径
            elements: 要提取的要素列表，默认为["题名", "责任者", "文号", "发文日期"]
            confidence_threshold: OCR置信度阈值
            enable_stamp_processing: 是否启用印章检测
            stamp_confidence_threshold: 印章检测置信度阈值

        Returns:
            提取结果字典
        """
        start_time = time.time()

        try:
            # 使用默认要素如果未指定
            if elements is None:
                elements = self.default_elements.copy()

            self.logger.info(f"开始提取档案要素: {image_path}")
            self.logger.info(f"目标要素: {elements}")
            if enable_stamp_processing:
                self.logger.info(f"启用印章检测，置信度阈值: {stamp_confidence_threshold}")

            # 步骤1: OCR文本识别（可选印章检测）
            ocr_result = await self._perform_ocr(
                image_path,
                confidence_threshold,
                enable_stamp_processing,
                stamp_confidence_threshold
            )

            # 保存OCR结果供后续使用
            self._last_ocr_result = ocr_result

            if not ocr_result['success']:
                return {
                    'success': False,
                    'error': f"OCR识别失败: {ocr_result.get('error', '未知错误')}",
                    'processing_time': time.time() - start_time
                }
            
            self.logger.info(f"OCR完成，耗时: {time.time() - start_time:.2f}秒")
            # 步骤2: 智能要素提取
            extraction_result = await self._extract_elements(
                ocr_result['full_text'], 
                ocr_result['text_blocks'],
                elements
            )
            
            # 组装最终结果
            result = {
                'success': True,
                'elements': extraction_result,
                'ocr_info': {
                    'total_blocks': len(ocr_result['text_blocks']),
                    'full_text_length': len(ocr_result['full_text']),
                    'confidence_threshold': confidence_threshold
                },
                'processing_time': time.time() - start_time
            }
            
            self.logger.info(f"档案要素提取完成，耗时: {result['processing_time']:.2f}秒")
            return result
            
        except Exception as e:
            self.logger.error(f"档案要素提取失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }
    
    async def _perform_ocr(self, image_path: Union[str, Path], confidence_threshold: float,
                          enable_stamp_processing: bool = False,
                          stamp_confidence_threshold: float = 0.8) -> Dict[str, Any]:
        """执行OCR识别，可选印章检测"""
        try:
            # 获取OCR模型实例（常驻内存）
            ocr_model = self.model_pool.get_ocr_model()

            # 在线程池中执行OCR（避免阻塞异步循环）
            loop = asyncio.get_event_loop()

            if enable_stamp_processing:
                # 使用带印章检测的OCR方法 - RTX 3060 GPU模式
                result = await loop.run_in_executor(
                    self.executor,
                    ocr_model.recognize_with_stamp_detection,
                    str(image_path),
                    confidence_threshold,
                    False,  # use_cpu=False，启用RTX 3060 GPU加速
                    enable_stamp_processing,
                    stamp_confidence_threshold
                )
            else:
                # 使用标准OCR方法 - RTX 3060 GPU模式
                result = await loop.run_in_executor(
                    self.executor,
                    ocr_model.recognize,
                    str(image_path),
                    confidence_threshold,
                    False  # use_cpu=False，启用RTX 3060 GPU加速
                )

            if not result.get('success', False):
                return {
                    'success': False,
                    'error': result.get('error', 'OCR识别失败')
                }

            # 处理OCR结果
            text_blocks = result.get('results', [])
            full_text = '\n'.join([block['text'] for block in text_blocks])

            # 处理印章检测结果（如果有）
            stamp_info = result.get('stamp_detection', {})

            self.logger.info(f"OCR识别完成，检测到{len(text_blocks)}个文本块")
            if stamp_info.get('enabled', False):
                self.logger.info(f"印章检测完成，检测到{stamp_info.get('total_stamps', 0)}个印章")

            return {
                'success': True,
                'text_blocks': text_blocks,
                'full_text': full_text,
                'stamp_detection': stamp_info
            }

        except Exception as e:
            self.logger.error(f"OCR识别异常: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _extract_elements(self, 
                              full_text: str, 
                              text_blocks: List[Dict[str, Any]], 
                              elements: List[str]) -> Dict[str, Any]:
        """使用LLM提取档案要素"""
        try:
            # 获取LLM模型实例（常驻内存）
            llm_model = self.model_pool.get_llm_model()
            
            if llm_model is None:
                # 如果LLM不可用，使用规则提取作为降级方案
                self.logger.warning("LLM模型不可用，使用规则提取")
                return self._rule_based_extraction(full_text, text_blocks, elements)
            
            # 构建提取提示词
            prompt = self._build_extraction_prompt(full_text, elements)
            
            # 判断是否为QWEN类型的LLM模型
            if llm_model.model_name.startswith("qwen3"):
                enable_thinking = False  # 确保关闭thinking mode, Ollama服务只需要在promp最后接上/no_think
                kwargs = {
                    'enable_thinking': enable_thinking,  # 确保关闭thinking mode
                    'temperature': 0.1,  # 降低随机性，提高准确性
                    'num_predict': 1024  # 限制输出长度
                }
            else:
                kwargs = {}

            # 在线程池中执行LLM推理
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                self.executor,
                llm_model.generate,
                prompt,
                kwargs
            )
            
            # 解析LLM响应
            response_content = response.content if hasattr(response, 'content') else str(response)
            self.logger.debug(f"LLM响应类型: {type(response)}, 内容长度: {len(response_content)}")
            extracted_elements = self._parse_llm_response(response_content, elements)
            
            self.logger.info(f"LLM要素提取完成: {list(extracted_elements.keys())}")
            return extracted_elements
            
        except Exception as e:
            self.logger.error(f"LLM要素提取失败: {e}")
            # 降级到规则提取
            return self._rule_based_extraction(full_text, text_blocks, elements)
    
    def _build_extraction_prompt(self, full_text: str, elements: List[str]) -> str:
        """构建档案要素提取提示词"""
        elements_str = "、".join(elements)
        
        prompt = f"""请从以下档案文本中提取指定的要素信息。

档案文本：
{full_text}

需要提取的要素：{elements_str}

请按照以下JSON格式返回结果：
{{
    "题名": "文档标题",
    "责任者": "发文单位或责任人",
    "文号": "文件编号",
    "发文日期": "YYYYMMDD格式的日期"
}}

注意：
1. 如果某个要素在文本中找不到，请返回null
2. 日期请统一转换为YYYYMMDD格式
3. 只返回JSON，不要其他说明文字
"""
        return prompt
    
    def _parse_llm_response(self, response: str, elements: List[str]) -> Dict[str, Any]:
        """解析LLM响应"""
        try:
            # 尝试解析JSON
            if '{' in response and '}' in response:
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                json_str = response[json_start:json_end]
                
                result = json.loads(json_str)
                
                # 确保所有要求的要素都在结果中
                extracted = {}
                for element in elements:
                    extracted[element] = result.get(element, None)
                
                return extracted
            else:
                raise ValueError("响应中未找到JSON格式")
                
        except Exception as e:
            self.logger.error(f"LLM响应解析失败: {e}")
            # 返回空结果
            return {element: None for element in elements}
    
    def _rule_based_extraction(self, 
                             full_text: str, 
                             text_blocks: List[Dict[str, Any]], 
                             elements: List[str]) -> Dict[str, Any]:
        """基于规则的要素提取（降级方案）"""
        import re
        
        result = {}
        
        for element in elements:
            if element == "题名":
                # 提取标题（通常在文档开头，字体较大）
                lines = full_text.split('\n')
                for line in lines[:5]:  # 检查前5行
                    line = line.strip()
                    if len(line) > 5 and len(line) < 50:  # 合理的标题长度
                        result[element] = line
                        break
                else:
                    result[element] = None
                    
            elif element == "文号":
                # 提取文号（包含数字和特定格式）
                patterns = [
                    r'[〔\[](\d{4})[〕\]]\s*第?\s*(\d+)\s*号',
                    r'(\w+字)\s*[〔\[](\d{4})[〕\]]\s*第?\s*(\d+)\s*号',
                    r'(\w+)\s*(\d{4})\s*(\d+)\s*号'
                ]
                
                for pattern in patterns:
                    match = re.search(pattern, full_text)
                    if match:
                        result[element] = match.group(0)
                        break
                else:
                    result[element] = None
                    
            elif element == "发文日期":
                # 提取日期
                date_patterns = [
                    r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日',
                    r'(\d{4})-(\d{1,2})-(\d{1,2})',
                    r'(\d{4})\.(\d{1,2})\.(\d{1,2})'
                ]
                
                for pattern in date_patterns:
                    match = re.search(pattern, full_text)
                    if match:
                        year, month, day = match.groups()
                        result[element] = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                        break
                else:
                    result[element] = None
                    
            elif element == "责任者":
                # 提取责任者（发文单位）
                # 通常在文档末尾或特定位置
                lines = full_text.split('\n')
                for line in reversed(lines[-10:]):  # 检查最后10行
                    line = line.strip()
                    if any(keyword in line for keyword in ['单位', '部门', '办公室', '委员会', '政府']):
                        result[element] = line
                        break
                else:
                    result[element] = None
            else:
                result[element] = None
        
        self.logger.info(f"规则提取完成: {list(result.keys())}")
        return result
    
    def cleanup(self):
        """清理资源"""
        if self.executor:
            self.executor.shutdown(wait=True)
        self.logger.info("档案要素提取服务资源清理完成")
