# 简化版档案要素提取配置 - 针对6GB显存优化

# PaddleOCR 3.0服务配置
paddleocr.v3.server.url=http://localhost:8080
paddleocr.v3.timeout=60000
paddleocr.v3.retry.count=3

# PP-ChatOCRv4配置（显存优化）
paddleocr.chat.v4.qianfan_api_key=${QIANFAN_API_KEY:your_api_key_here}
paddleocr.chat.v4.model_name=ernie-4.5-turbo
paddleocr.chat.v4.temperature=0.1
paddleocr.chat.v4.max_tokens=2048

# Embedding配置（离线优化）
embedding.lightweight.enable=true
embedding.vector.dimension=256
embedding.ollama.enable=true
embedding.cache.enable=true
embedding.cache.max_size=500

# Ollama BGE-M3配置
ollama.base.url=http://localhost:11434
ollama.embedding.model=bge-m3
ollama.embedding.timeout=30000
ollama.embedding.batch_size=8
ollama.embedding.enable_cache=true
ollama.embedding.cache_size=1000

# 内存向量配置（针对100-1000页优化）
vector.memory.max_volume_size=1000
vector.memory.max_text_segments=5000
vector.memory.context_max_length=3000

# 档案要素提取配置
archive.element.confidence.threshold=0.6
archive.element.similarity.threshold.title=0.8
archive.element.similarity.threshold.responsible_party=0.7
archive.element.similarity.threshold.document_number=1.0
archive.element.similarity.threshold.issue_date=1.0

# 智能提取策略配置
archive.strategy.quality_threshold=0.8
archive.strategy.complexity_threshold=0.7
archive.strategy.gpu_memory_threshold=0.8
archive.strategy.enable_dynamic_switching=true

# 并发处理配置（显存友好）
archive.processing.thread_pool_size=2
archive.processing.batch_size=20
archive.processing.timeout_seconds=300

# 性能优化配置
archive.performance.enable_parallel_extraction=true
archive.performance.enable_result_cache=false
archive.performance.auto_cleanup_memory=true
archive.performance.gpu_memory_check=true

# 显存管理配置
gpu.memory.max_usage_percent=80
gpu.memory.check_interval_seconds=30
gpu.memory.force_cleanup_threshold=90

# 日志配置
logging.level.com.deeppaas.vector=INFO
logging.level.com.deeppaas.archive=INFO
logging.level.com.deeppaas.paddleocr=INFO
