#!/usr/bin/env python3
"""
环境测试脚本
检查 py3.9_copy 环境中的依赖是否正确安装
"""

import sys
import traceback

def test_basic_imports():
    """测试基本导入"""
    print("=== 基本Python环境 ===")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
def test_core_dependencies():
    """测试核心依赖"""
    print("\n=== 核心依赖测试 ===")
    
    # PyTorch
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"   CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   GPU数量: {torch.cuda.device_count()}")
            print(f"   当前GPU: {torch.cuda.get_device_name(0)}")
    except Exception as e:
        print(f"❌ PyTorch导入失败: {e}")
    
    # Transformers
    try:
        import transformers
        print(f"✅ Transformers: {transformers.__version__}")
        
        import tokenizers
        print(f"✅ Tokenizers: {tokenizers.__version__}")
    except Exception as e:
        print(f"❌ Transformers导入失败: {e}")
    
    # PaddlePaddle
    try:
        import paddle
        print(f"✅ PaddlePaddle: {paddle.__version__}")
    except Exception as e:
        print(f"❌ PaddlePaddle导入失败: {e}")
        print(f"   错误详情: {traceback.format_exc()}")
    
    # PaddleOCR
    try:
        import paddleocr
        print(f"✅ PaddleOCR: {paddleocr.__version__}")
    except Exception as e:
        print(f"❌ PaddleOCR导入失败: {e}")

def test_web_framework():
    """测试Web框架"""
    print("\n=== Web框架测试 ===")
    
    try:
        import fastapi
        print(f"✅ FastAPI: {fastapi.__version__}")
        
        import pydantic
        print(f"✅ Pydantic: {pydantic.__version__}")
        
        import uvicorn
        print(f"✅ Uvicorn: {uvicorn.__version__}")
        
        # 测试兼容性
        from fastapi import FastAPI
        from pydantic import BaseModel
        
        class TestModel(BaseModel):
            name: str
            value: int
        
        app = FastAPI()
        
        @app.get("/test")
        def test_endpoint():
            return {"message": "FastAPI + Pydantic 兼容性正常"}
        
        print("✅ FastAPI + Pydantic 兼容性测试通过")
        
    except Exception as e:
        print(f"❌ Web框架测试失败: {e}")

def test_image_processing():
    """测试图像处理"""
    print("\n=== 图像处理测试 ===")
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
        
        import PIL
        print(f"✅ Pillow: {PIL.__version__}")
        
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
        
        # 简单功能测试
        test_array = np.zeros((100, 100, 3), dtype=np.uint8)
        print("✅ NumPy数组创建正常")
        
    except Exception as e:
        print(f"❌ 图像处理测试失败: {e}")

def test_text_processing():
    """测试文本处理"""
    print("\n=== 文本处理测试 ===")
    
    try:
        import jieba
        print(f"✅ Jieba: {jieba.__version__}")
        
        # 简单分词测试
        words = jieba.lcut("智能信息提取模块测试")
        print(f"✅ 分词测试: {words}")
        
    except Exception as e:
        print(f"❌ 文本处理测试失败: {e}")

def test_config_serialization():
    """测试配置和序列化"""
    print("\n=== 配置序列化测试 ===")
    
    try:
        import yaml
        print(f"✅ PyYAML: {yaml.__version__}")
        
        # 简单YAML测试
        test_config = {"test": "value", "number": 123}
        yaml_str = yaml.dump(test_config)
        loaded_config = yaml.safe_load(yaml_str)
        assert loaded_config == test_config
        print("✅ YAML序列化测试通过")
        
    except Exception as e:
        print(f"❌ 配置序列化测试失败: {e}")

def main():
    """主测试函数"""
    print("智能信息提取模块 - 环境依赖测试")
    print("=" * 50)
    
    test_basic_imports()
    test_core_dependencies()
    test_web_framework()
    test_image_processing()
    test_text_processing()
    test_config_serialization()
    
    print("\n" + "=" * 50)
    print("环境测试完成！")
    print("\n如果看到❌错误，请参考 requirements.txt 中的安装说明进行修复。")

if __name__ == "__main__":
    main()
