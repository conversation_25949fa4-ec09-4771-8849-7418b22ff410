package com.toolbox.compliance.controller;

import com.toolbox.compliance.service.ContentAccuracyService;
import com.toolbox.compliance.model.*;
import com.toolbox.compliance.config.ContentAccuracyProperties;
import com.toolbox.extraction.service.ArchiveExtractionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 条目内容正确性检查控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/compliance/content-accuracy")
@Tag(name = "条目内容正确性检查", description = "基于AI智能提取的内容正确性检查")
public class ContentAccuracyController {
    
    @Autowired
    private ContentAccuracyService contentAccuracyService;

    @Autowired
    private ArchiveExtractionService extractionService;

    @Autowired
    private ContentAccuracyProperties contentAccuracyProperties;
    
    /**
     * 获取系统配置信息
     */
    @GetMapping("/system-config")
    @Operation(summary = "获取系统配置", description = "获取内容正确性检查的系统配置信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSystemConfig() {

        try {
            Map<String, Object> systemConfig = contentAccuracyProperties.getSystemConfigInfo();

            return ResponseEntity.ok(ApiResponse.success(systemConfig, "获取系统配置成功"));

        } catch (Exception e) {
            log.error("获取系统配置失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取系统配置失败: " + e.getMessage()));
        }
    }

    /**
     * 测试字段映射配置
     */
    @PostMapping("/test-field-mapping")
    @Operation(summary = "测试字段映射配置", description = "测试单个字段的映射配置是否正确")
    public ResponseEntity<ApiResponse<FieldMappingTestResult>> testFieldMapping(
            @RequestBody FieldMappingTestRequest request) {
        
        try {
            log.info("测试字段映射: {}", request);
            
            FieldMappingTestResult result = contentAccuracyService.testFieldMapping(request);
            
            return ResponseEntity.ok(ApiResponse.success(result, "字段映射测试完成"));
            
        } catch (Exception e) {
            log.error("字段映射测试失败", e);
            return ResponseEntity.ok(ApiResponse.error("字段映射测试失败: " + e.getMessage()));
        }
    }
    
    /**
     * 测试内容正确性配置
     */
    @PostMapping("/test-config")
    @Operation(summary = "测试配置", description = "测试完整的内容正确性检查配置")
    public ResponseEntity<ApiResponse<ConfigTestResult>> testContentAccuracyConfig(
            @RequestBody ContentAccuracyConfig config) {
        
        try {
            log.info("测试内容正确性配置: {}", config);
            
            ConfigTestResult result = contentAccuracyService.testConfiguration(config);
            
            return ResponseEntity.ok(ApiResponse.success(result, "配置测试完成"));
            
        } catch (Exception e) {
            log.error("配置测试失败", e);
            return ResponseEntity.ok(ApiResponse.error("配置测试失败: " + e.getMessage()));
        }
    }
    
    /**
     * 解析Excel列信息
     */
    @PostMapping("/parse-excel-columns")
    @Operation(summary = "解析Excel列", description = "解析Excel文件的列信息用于字段映射")
    public ResponseEntity<ApiResponse<ExcelColumnsInfo>> parseExcelColumns(
            @RequestParam("file") MultipartFile file) {
        
        try {
            log.info("解析Excel列信息，文件名: {}", file.getOriginalFilename());
            
            ExcelColumnsInfo columnsInfo = contentAccuracyService.parseExcelColumns(file);
            
            return ResponseEntity.ok(ApiResponse.success(columnsInfo, "Excel列解析成功"));
            
        } catch (Exception e) {
            log.error("Excel列解析失败", e);
            return ResponseEntity.ok(ApiResponse.error("Excel列解析失败: " + e.getMessage()));
        }
    }
    
    /**
     * 启动内容正确性检查
     */
    @PostMapping("/start-check")
    @Operation(summary = "启动检查", description = "启动内容正确性检查任务")
    public ResponseEntity<ApiResponse<CheckTaskInfo>> startContentAccuracyCheck(
            @RequestBody ContentAccuracyCheckRequest request) {
        
        try {
            log.info("启动内容正确性检查，配置: {}", request.getConfig());
            
            // 验证请求
            if (request.getExcelFile() == null) {
                return ResponseEntity.ok(ApiResponse.error("Excel文件不能为空"));
            }
            
            if (request.getDocumentFiles() == null || request.getDocumentFiles().isEmpty()) {
                return ResponseEntity.ok(ApiResponse.error("档案文档文件不能为空"));
            }
            
            // 启动异步检查任务
            CompletableFuture<ContentAccuracyCheckResult> future = 
                contentAccuracyService.startContentAccuracyCheckAsync(request);
            
            // 生成任务ID
            String taskId = "content_accuracy_" + System.currentTimeMillis();
            
            // 存储任务信息
            CheckTaskInfo taskInfo = new CheckTaskInfo(taskId, "CONTENT_ACCURACY", "running");
            contentAccuracyService.storeTaskInfo(taskId, taskInfo, future);
            
            return ResponseEntity.ok(ApiResponse.success(taskInfo, "内容正确性检查任务已启动"));
            
        } catch (Exception e) {
            log.error("启动内容正确性检查失败", e);
            return ResponseEntity.ok(ApiResponse.error("启动检查失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取检查进度
     */
    @GetMapping("/check-progress/{taskId}")
    @Operation(summary = "获取检查进度", description = "获取内容正确性检查任务的进度")
    public ResponseEntity<ApiResponse<CheckProgress>> getCheckProgress(
            @PathVariable String taskId) {
        
        try {
            CheckProgress progress = contentAccuracyService.getCheckProgress(taskId);
            
            if (progress == null) {
                return ResponseEntity.ok(ApiResponse.error("任务不存在"));
            }
            
            return ResponseEntity.ok(ApiResponse.success(progress, "获取进度成功"));
            
        } catch (Exception e) {
            log.error("获取检查进度失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取进度失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取检查结果
     */
    @GetMapping("/check-result/{taskId}")
    @Operation(summary = "获取检查结果", description = "获取内容正确性检查的完整结果")
    public ResponseEntity<ApiResponse<ContentAccuracyCheckResult>> getCheckResult(
            @PathVariable String taskId) {
        
        try {
            ContentAccuracyCheckResult result = contentAccuracyService.getCheckResult(taskId);
            
            if (result == null) {
                return ResponseEntity.ok(ApiResponse.error("任务结果不存在"));
            }
            
            return ResponseEntity.ok(ApiResponse.success(result, "获取结果成功"));
            
        } catch (Exception e) {
            log.error("获取检查结果失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取结果失败: " + e.getMessage()));
        }
    }
    
    /**
     * 取消检查任务
     */
    @PostMapping("/cancel-check/{taskId}")
    @Operation(summary = "取消检查", description = "取消正在进行的内容正确性检查任务")
    public ResponseEntity<ApiResponse<Boolean>> cancelCheck(
            @PathVariable String taskId) {
        
        try {
            boolean cancelled = contentAccuracyService.cancelCheck(taskId);
            
            String message = cancelled ? "任务已取消" : "任务无法取消或不存在";
            return ResponseEntity.ok(ApiResponse.success(cancelled, message));
            
        } catch (Exception e) {
            log.error("取消检查任务失败", e);
            return ResponseEntity.ok(ApiResponse.error("取消任务失败: " + e.getMessage()));
        }
    }
    
    /**
     * 导出内容正确性报告
     */
    @PostMapping("/export-report")
    @Operation(summary = "导出报告", description = "导出内容正确性检查报告")
    public ResponseEntity<byte[]> exportContentAccuracyReport(
            @RequestBody ContentAccuracyReportExportRequest request) {
        
        try {
            log.info("导出内容正确性报告");
            
            byte[] reportData = contentAccuracyService.exportReport(request);
            
            return ResponseEntity.ok()
                    .header("Content-Disposition", "attachment; filename=content_accuracy_report.pdf")
                    .header("Content-Type", "application/pdf")
                    .body(reportData);
            
        } catch (Exception e) {
            log.error("导出报告失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 导出带错误标注的Excel
     */
    @PostMapping("/export-annotated-excel")
    @Operation(summary = "导出标注Excel", description = "导出带有错误标注的Excel文件")
    public ResponseEntity<byte[]> exportAnnotatedExcel(
            @RequestBody ExcelAnnotationExportRequest request) {
        
        try {
            log.info("导出标注Excel");
            
            byte[] excelData = contentAccuracyService.exportAnnotatedExcel(request);
            
            return ResponseEntity.ok()
                    .header("Content-Disposition", "attachment; filename=annotated_excel.xlsx")
                    .header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .body(excelData);
            
        } catch (Exception e) {
            log.error("导出标注Excel失败", e);
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * 采纳修正建议
     */
    @PostMapping("/accept-suggestion")
    @Operation(summary = "采纳建议", description = "采纳AI提供的修正建议")
    public ResponseEntity<ApiResponse<Boolean>> acceptSuggestion(
            @RequestBody SuggestionAcceptRequest request) {
        
        try {
            log.info("采纳修正建议: {}", request);
            
            boolean accepted = contentAccuracyService.acceptSuggestion(request);
            
            String message = accepted ? "建议已采纳" : "采纳建议失败";
            return ResponseEntity.ok(ApiResponse.success(accepted, message));
            
        } catch (Exception e) {
            log.error("采纳建议失败", e);
            return ResponseEntity.ok(ApiResponse.error("采纳建议失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取AI提取服务状态
     */
    @GetMapping("/extraction-service-status")
    @Operation(summary = "获取AI服务状态", description = "获取AI提取服务的运行状态")
    public ResponseEntity<ApiResponse<SystemStatus>> getExtractionServiceStatus() {
        
        try {
            SystemStatus status = extractionService.getExtractionServiceStatus();
            
            return ResponseEntity.ok(ApiResponse.success(status, "获取AI服务状态成功"));
            
        } catch (Exception e) {
            log.error("获取AI服务状态失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取AI服务状态失败: " + e.getMessage()));
        }
    }
    
    /**
     * 批量处理档案要素提取
     */
    @PostMapping("/batch-extract")
    @Operation(summary = "批量提取", description = "批量提取档案要素用于内容比对")
    public ResponseEntity<ApiResponse<List<ArchiveElements>>> batchExtractArchiveElements(
            @RequestParam("files") List<MultipartFile> files) {
        
        try {
            log.info("批量提取档案要素，文件数量: {}", files.size());
            
            if (files.size() > 50) {
                return ResponseEntity.ok(ApiResponse.error("批量处理文件数量不能超过50个"));
            }
            
            List<ArchiveElements> elementsList = extractionService.batchExtractArchiveElements(files);
            
            return ResponseEntity.ok(ApiResponse.success(elementsList, "批量提取完成"));
            
        } catch (Exception e) {
            log.error("批量提取失败", e);
            return ResponseEntity.ok(ApiResponse.error("批量提取失败: " + e.getMessage()));
        }
    }
    
    /**
     * 保存规则配置
     */
    @PostMapping("/save-rule-config")
    @Operation(summary = "保存规则配置", description = "保存内容正确性检查规则配置")
    public ResponseEntity<ApiResponse<Boolean>> saveRuleConfiguration(
            @RequestBody RuleConfigSaveRequest request) {
        
        try {
            log.info("保存规则配置: {}", request.getRuleCode());
            
            boolean saved = contentAccuracyService.saveRuleConfiguration(request);
            
            String message = saved ? "配置保存成功" : "配置保存失败";
            return ResponseEntity.ok(ApiResponse.success(saved, message));
            
        } catch (Exception e) {
            log.error("保存规则配置失败", e);
            return ResponseEntity.ok(ApiResponse.error("保存配置失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取历史检查记录
     */
    @GetMapping("/check-history")
    @Operation(summary = "获取检查历史", description = "获取内容正确性检查的历史记录")
    public ResponseEntity<ApiResponse<List<CheckHistoryRecord>>> getCheckHistory(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        try {
            List<CheckHistoryRecord> history = contentAccuracyService.getCheckHistory(page, size);
            
            return ResponseEntity.ok(ApiResponse.success(history, "获取检查历史成功"));
            
        } catch (Exception e) {
            log.error("获取检查历史失败", e);
            return ResponseEntity.ok(ApiResponse.error("获取检查历史失败: " + e.getMessage()));
        }
    }
}
