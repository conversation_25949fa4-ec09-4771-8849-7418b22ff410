#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门调试GPU模式的OCR结果
"""

import os
import json
import tempfile
import subprocess

def debug_gpu_ocr():
    """调试GPU模式OCR"""
    print("🔍 专门调试GPU模式OCR结果")
    
    # 测试图片路径
    test_image = "uploads/125ef0ca-61e6-4cf5-9845-e8b9f3bd1358.jpg"
    
    if not os.path.exists(test_image):
        print(f"❌ 测试图片不存在: {test_image}")
        return
    
    # 创建临时配置文件
    config = {
        "image_path": os.path.abspath(test_image),
        "use_cpu": False,  # 使用GPU
        "gpu_id": 0,
        "use_angle_cls": True,
        "lang": "ch",
        "confidence_threshold": 0.1  # 降低阈值
    }
    
    # 写入临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
        config_file = f.name
    
    try:
        # 直接调用OCR worker脚本
        worker_script = "src/models/ocr_worker.py"
        python_exe = r"C:\ProgramData\Anaconda3\envs\py39ocr\python.exe"
        cmd = [python_exe, worker_script, config_file]
        
        print(f"执行命令: {' '.join(cmd)}")
        print(f"配置文件内容: {json.dumps(config, ensure_ascii=False, indent=2)}")
        
        # 运行worker脚本，忽略stderr的编码错误
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='ignore',  # 忽略编码错误
            cwd=os.getcwd()
        )
        
        print(f"\n返回码: {result.returncode}")
        
        # 显示STDOUT
        if result.stdout:
            print("\n--- STDOUT ---")
            try:
                output_data = json.loads(result.stdout)
                print(json.dumps(output_data, ensure_ascii=False, indent=2))
                
                if output_data.get('success'):
                    results = output_data.get('results', [])
                    print(f"\n识别结果: {len(results)} 个文本块")
                    if results:
                        for i, item in enumerate(results):
                            print(f"  文本块{i+1}: '{item.get('text', '')}' (置信度: {item.get('confidence', 0):.3f})")
                    else:
                        print("  ⚠️ 结果数组为空")
                else:
                    print(f"\n❌ 失败: {output_data.get('error', '未知错误')}")
                    
            except json.JSONDecodeError as e:
                print(f"JSON解析失败: {e}")
                print("原始输出:")
                print(result.stdout)
        
        # 显示STDERR (调试信息) - 忽略编码错误
        if result.stderr:
            print("\n--- STDERR (调试信息) ---")
            # 按行处理stderr
            for line_num, line in enumerate(result.stderr.strip().split('\n'), 1):
                if line.strip():
                    try:
                        debug_data = json.loads(line)
                        print(f"调试{line_num}: {json.dumps(debug_data, ensure_ascii=False, indent=2)}")
                    except json.JSONDecodeError:
                        # 显示非JSON行，但限制长度
                        line_preview = line[:100] + "..." if len(line) > 100 else line
                        print(f"非JSON调试{line_num}: {line_preview}")
        
    finally:
        # 清理临时文件
        try:
            os.unlink(config_file)
        except:
            pass

if __name__ == "__main__":
    debug_gpu_ocr()
