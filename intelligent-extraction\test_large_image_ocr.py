#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试大图像OCR处理
"""
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.models.ocr_subprocess import OCRSubprocessRunner

def test_large_image_ocr(image_path: str):
    """测试大图像OCR处理"""
    
    image_file = Path(image_path)
    if not image_file.exists():
        print(f"❌ 图像文件不存在: {image_path}")
        return False
    
    # 获取图像信息
    try:
        from PIL import Image
        with Image.open(image_file) as img:
            width, height = img.size
            file_size = image_file.stat().st_size
            print(f"📊 图像信息: {width}×{height}, {file_size/1024:.1f}KB, {img.format}")
    except Exception as e:
        print(f"⚠️ 无法获取图像信息: {e}")
    
    print(f"🧪 测试大图像OCR处理: {image_file.name}")
    print("=" * 60)
    
    try:
        # 创建OCR运行器
        print("1. 创建OCR运行器...")
        ocr_runner = OCRSubprocessRunner(device_id=0)
        print("✅ OCR运行器创建成功")
        
        # 测试CPU模式
        print("\n2. 测试CPU模式OCR...")
        start_time = time.time()
        
        result = ocr_runner.recognize(
            image_path=str(image_file),
            confidence_threshold=0.3,  # 降低阈值
            use_cpu=True
        )
        
        processing_time = time.time() - start_time
        
        print(f"⏱️ 处理时间: {processing_time:.2f}秒")
        
        if result.get('success'):
            text_blocks = result.get('results', [])
            print(f"✅ OCR处理成功!")
            print(f"📝 检测到文本块: {len(text_blocks)}个")
            
            # 显示前5个文本块
            for i, block in enumerate(text_blocks[:5], 1):
                text = block.get('text', '')
                confidence = block.get('confidence', 0)
                print(f"   {i}. '{text}' (置信度: {confidence:.3f})")
            
            if len(text_blocks) > 5:
                print(f"   ... 还有 {len(text_blocks) - 5} 个文本块")
            
            return True
        else:
            error = result.get('error', '未知错误')
            print(f"❌ OCR处理失败: {error}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python test_large_image_ocr.py <图像路径>")
        print("示例: python test_large_image_ocr.py G:\\tmp\\Image_00002.jpg")
        return
    
    image_path = sys.argv[1]
    success = test_large_image_ocr(image_path)
    
    if success:
        print("\n🎉 大图像OCR测试成功!")
    else:
        print("\n❌ 大图像OCR测试失败")


if __name__ == "__main__":
    main()
