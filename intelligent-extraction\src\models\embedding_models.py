"""
嵌入模型封装 - 支持Ollama本地部署的嵌入模型
"""
import json
import logging
import requests
import numpy as np
import time
from typing import List, Dict, Any, Union, Optional
from dataclasses import dataclass

@dataclass
class EmbeddingResponse:
    """嵌入响应结果"""
    embeddings: np.ndarray
    model: str
    usage: Dict[str, int]
    response_time: float

class OllamaEmbeddingModel:
    """Ollama嵌入模型封装"""
    
    def __init__(self, 
                 model_name: str = "bge-m3",
                 base_url: str = "http://localhost:11434",
                 device_id: int = 0,
                 **kwargs):
        """
        初始化Ollama嵌入模型
        
        Args:
            model_name: 嵌入模型名称，如 "bge-m3", "bge-large-zh-v1.5", "nomic-embed-text"
            base_url: Ollama服务地址
            device_id: GPU设备ID
            **kwargs: 其他配置参数
        """
        self.model_name = model_name
        self.base_url = base_url.rstrip('/')
        self.device_id = device_id
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.max_length = kwargs.get("max_length", 512)
        self.batch_size = kwargs.get("batch_size", 8)
        self.timeout = kwargs.get("timeout", 30)
        
        # 验证连接和模型
        self._check_connection()
        self._ensure_model_available()
        
        # 获取模型维度
        self.embedding_dim = self._get_embedding_dimension()
    
    def _check_connection(self):
        """检查Ollama服务连接"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            response.raise_for_status()
            self.logger.info(f"Ollama嵌入服务连接成功: {self.base_url}")
        except Exception as e:
            self.logger.error(f"Ollama服务连接失败: {e}")
            raise ConnectionError(f"无法连接到Ollama服务 {self.base_url}: {e}")
    
    def _ensure_model_available(self):
        """确保嵌入模型可用"""
        try:
            # 获取已安装的模型列表
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            response.raise_for_status()
            
            models_data = response.json()
            available_models = [model['name'] for model in models_data.get('models', [])]
            
            if self.model_name not in available_models:
                self.logger.warning(f"嵌入模型 {self.model_name} 未安装，尝试拉取...")
                self._pull_model()
            else:
                self.logger.info(f"嵌入模型 {self.model_name} 已可用")
                
        except Exception as e:
            self.logger.error(f"检查嵌入模型可用性失败: {e}")
            raise
    
    def _pull_model(self):
        """拉取嵌入模型"""
        try:
            self.logger.info(f"开始拉取嵌入模型: {self.model_name}")
            
            pull_data = {"name": self.model_name}
            response = requests.post(
                f"{self.base_url}/api/pull",
                json=pull_data,
                timeout=600,  # 拉取模型可能需要较长时间
                stream=True
            )
            response.raise_for_status()
            
            # 处理流式响应
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line)
                        if 'status' in data:
                            self.logger.info(f"拉取进度: {data['status']}")
                        if data.get('status') == 'success':
                            self.logger.info(f"嵌入模型 {self.model_name} 拉取成功")
                            return
                    except json.JSONDecodeError:
                        continue
                        
        except Exception as e:
            self.logger.error(f"拉取嵌入模型失败: {e}")
            raise RuntimeError(f"无法拉取嵌入模型 {self.model_name}: {e}")
    
    def _get_embedding_dimension(self) -> int:
        """获取嵌入维度"""
        try:
            # 使用测试文本获取嵌入维度
            test_embedding = self.encode(["测试文本"])
            return test_embedding.shape[1]
        except Exception as e:
            self.logger.warning(f"无法获取嵌入维度，使用默认值: {e}")
            # 根据模型名称返回常见维度
            if "bge-m3" in self.model_name:
                return 1024
            elif "bge-large" in self.model_name:
                return 1024
            elif "bge-small" in self.model_name:
                return 512
            elif "nomic-embed" in self.model_name:
                return 768
            else:
                return 768  # 默认维度
    
    def encode(self, 
               texts: Union[str, List[str]], 
               normalize: bool = True,
               **kwargs) -> np.ndarray:
        """
        编码文本为嵌入向量
        
        Args:
            texts: 单个文本或文本列表
            normalize: 是否归一化向量
            **kwargs: 额外参数
            
        Returns:
            np.ndarray: 嵌入向量，形状为 (n_texts, embedding_dim)
        """
        start_time = time.time()
        
        # 统一处理为列表
        if isinstance(texts, str):
            texts = [texts]
        
        if not texts:
            return np.array([]).reshape(0, self.embedding_dim)
        
        try:
            # 批量处理
            all_embeddings = []
            
            for i in range(0, len(texts), self.batch_size):
                batch_texts = texts[i:i + self.batch_size]
                batch_embeddings = self._encode_batch(batch_texts)
                all_embeddings.extend(batch_embeddings)
            
            # 转换为numpy数组
            embeddings = np.array(all_embeddings)
            
            # 归一化
            if normalize:
                norms = np.linalg.norm(embeddings, axis=1, keepdims=True)
                norms = np.where(norms == 0, 1, norms)  # 避免除零
                embeddings = embeddings / norms
            
            response_time = time.time() - start_time
            self.logger.debug(f"编码完成: {len(texts)}个文本, 耗时: {response_time:.2f}s")
            
            return embeddings
            
        except Exception as e:
            self.logger.error(f"文本编码失败: {e}")
            raise RuntimeError(f"嵌入编码失败: {e}")
    
    def _encode_batch(self, texts: List[str]) -> List[List[float]]:
        """编码一批文本 - Ollama API每次只能处理一个文本"""
        try:
            embeddings = []

            for text in texts:
                # 构建请求数据
                request_data = {
                    "model": self.model_name,
                    "prompt": text,
                }

                # 发送请求
                response = requests.post(
                    f"{self.base_url}/api/embeddings",
                    json=request_data,
                    timeout=self.timeout
                )
                response.raise_for_status()

                # 解析响应
                result = response.json()

                if 'embedding' in result:
                    embeddings.append(result['embedding'])
                else:
                    raise ValueError(f"响应中未找到嵌入数据: {result}")

                # 避免请求过快
                if len(texts) > 1:
                    time.sleep(0.1)

            return embeddings

        except Exception as e:
            self.logger.error(f"批量编码失败: {e}")
            raise
    
    def similarity(self, 
                   text1: Union[str, np.ndarray], 
                   text2: Union[str, np.ndarray]) -> float:
        """
        计算两个文本的相似度
        
        Args:
            text1: 文本1或其嵌入向量
            text2: 文本2或其嵌入向量
            
        Returns:
            float: 余弦相似度 [-1, 1]
        """
        try:
            # 获取嵌入向量
            if isinstance(text1, str):
                emb1 = self.encode([text1])[0]
            else:
                emb1 = text1
                
            if isinstance(text2, str):
                emb2 = self.encode([text2])[0]
            else:
                emb2 = text2
            
            # 计算余弦相似度
            similarity = np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))
            return float(similarity)
            
        except Exception as e:
            self.logger.error(f"相似度计算失败: {e}")
            return 0.0
    
    def semantic_search(self, 
                       query: str, 
                       documents: List[str], 
                       top_k: int = 5) -> List[Dict[str, Any]]:
        """
        语义搜索
        
        Args:
            query: 查询文本
            documents: 文档列表
            top_k: 返回前k个最相似的文档
            
        Returns:
            List[Dict]: 搜索结果，包含文档和相似度
        """
        try:
            if not documents:
                return []
            
            # 编码查询和文档
            query_embedding = self.encode([query])[0]
            doc_embeddings = self.encode(documents)
            
            # 计算相似度
            similarities = np.dot(doc_embeddings, query_embedding)
            
            # 排序并返回top_k
            top_indices = np.argsort(similarities)[::-1][:top_k]
            
            results = []
            for idx in top_indices:
                results.append({
                    "document": documents[idx],
                    "similarity": float(similarities[idx]),
                    "index": int(idx)
                })
            
            return results
            
        except Exception as e:
            self.logger.error(f"语义搜索失败: {e}")
            return []
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取嵌入模型信息"""
        try:
            response = requests.post(
                f"{self.base_url}/api/show",
                json={"name": self.model_name},
                timeout=10
            )
            response.raise_for_status()
            
            model_info = response.json()
            return {
                "name": self.model_name,
                "embedding_dim": self.embedding_dim,
                "max_length": self.max_length,
                "batch_size": self.batch_size,
                "size": model_info.get('size', 0),
                "family": model_info.get('details', {}).get('family', 'unknown'),
                "modified_at": model_info.get('modified_at', ''),
            }
            
        except Exception as e:
            self.logger.error(f"获取嵌入模型信息失败: {e}")
            return {
                "name": self.model_name, 
                "embedding_dim": self.embedding_dim,
                "error": str(e)
            }
    
    def cleanup(self):
        """清理资源"""
        # Ollama模型由服务端管理，客户端无需特殊清理
        self.logger.info(f"Ollama嵌入模型 {self.model_name} 清理完成")
    
    def __str__(self) -> str:
        return f"OllamaEmbeddingModel(model={self.model_name}, dim={self.embedding_dim})"


class QwenEmbeddingModel(OllamaEmbeddingModel):
    """Qwen嵌入模型的特化版本"""

    def __init__(self,
                 model_size: str = "4b",
                 base_url: str = "http://localhost:11434",
                 device_id: int = 0,
                 **kwargs):
        """
        初始化Qwen嵌入模型

        Args:
            model_size: 模型大小 "0.6b", "4b", "8b"
            base_url: Ollama服务地址
            device_id: GPU设备ID
            **kwargs: 其他配置参数
        """
        # 使用Qwen3专用嵌入模型
        if model_size == "0.6b":
            model_name = "dengcao/qwen3-embedding-0.6b:q4_k_m"
        elif model_size == "4b":
            model_name = "dengcao/qwen3-embedding-4b:q4_k_m"
        elif model_size == "8b":
            model_name = "dengcao/qwen3-embedding-8b:q4_k_m"
        else:
            raise ValueError(f"不支持的Qwen嵌入模型大小: {model_size}")

        super().__init__(model_name, base_url, device_id, **kwargs)


class BGEEmbeddingModel(OllamaEmbeddingModel):
    """BGE嵌入模型的特化版本"""
    
    def __init__(self, 
                 model_size: str = "m3",
                 base_url: str = "http://localhost:11434",
                 device_id: int = 0,
                 **kwargs):
        """
        初始化BGE嵌入模型
        
        Args:
            model_size: 模型大小 "small", "base", "large", "m3"
            base_url: Ollama服务地址
            device_id: GPU设备ID
            **kwargs: 其他配置参数
        """
        if model_size == "m3":
            model_name = "bge-m3"
        else:
            model_name = f"bge-{model_size}-zh-v1.5"
        
        super().__init__(model_name, base_url, device_id, **kwargs)
