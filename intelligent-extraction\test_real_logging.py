#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟实际服务运行的日志测试
"""
import sys
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_service_logging():
    """测试服务启动时的日志情况"""
    
    print("=" * 60)
    print("模拟实际服务运行的日志测试")
    print("=" * 60)
    
    # 1. 模拟start_service.py的日志配置
    print("\n1. 模拟服务启动日志配置...")
    from src.utils.logger_config import create_project_logger
    logger = create_project_logger("intelligent-extraction")
    
    # 2. 模拟服务组件初始化
    print("\n2. 模拟服务组件初始化...")
    
    try:
        # 模拟ConfigManager初始化
        from src.core.config_manager import ConfigManager
        config_manager = ConfigManager()
        print("  ✅ ConfigManager初始化完成")
        
        # 模拟ArchiveExtractionService初始化
        from src.services.archive_extraction_service import ArchiveExtractionService
        service = ArchiveExtractionService()
        print("  ✅ ArchiveExtractionService初始化完成")
        
        # 检查服务的logger配置
        service_logger = service.logger
        print(f"  服务logger级别: {logging.getLevelName(service_logger.getEffectiveLevel())}")
        print(f"  服务logger禁用状态: {service_logger.disabled}")
        
        # 测试服务logger的输出
        print("\n3. 测试服务logger输出:")
        service_logger.info("这是服务的INFO消息")
        service_logger.warning("这是服务的WARNING消息")
        service_logger.error("这是服务的ERROR消息")
        
    except Exception as e:
        print(f"  ❌ 服务初始化失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. 测试其他模块的logger
    print("\n4. 测试其他关键模块的logger:")
    
    try:
        # 测试model_pool
        from src.core.model_pool import get_model_pool
        model_pool = get_model_pool()
        print("  ✅ ModelPool获取成功")
        
        # 检查model_pool的内部logger
        if hasattr(model_pool, 'logger'):
            pool_logger = model_pool.logger
            print(f"  ModelPool logger级别: {logging.getLevelName(pool_logger.getEffectiveLevel())}")
            pool_logger.info("这是ModelPool的INFO消息")
        
    except Exception as e:
        print(f"  ❌ ModelPool测试失败: {e}")
    
    # 4. 检查所有活跃的logger
    print("\n5. 检查所有活跃的logger:")
    
    # 获取所有logger
    loggers = [logging.getLogger(name) for name in logging.Logger.manager.loggerDict]
    loggers.append(logging.getLogger())  # 添加根logger
    
    # 过滤出项目相关的logger
    project_loggers = [logger for logger in loggers 
                      if logger.name.startswith('src') or 
                         logger.name.startswith('intelligent-extraction') or
                         logger.name == 'root']
    
    for logger in project_loggers:
        if logger.name:  # 跳过空名称的logger
            print(f"  {logger.name:50} | 级别: {logging.getLevelName(logger.getEffectiveLevel()):8} | 禁用: {logger.disabled}")

if __name__ == "__main__":
    test_service_logging()
