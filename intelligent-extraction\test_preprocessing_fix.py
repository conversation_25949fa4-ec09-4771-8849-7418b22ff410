#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图像预处理是否能解决大图像OCR失败问题
"""
import sys
import time
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.utils.image_preprocessor import DocumentImageProcessor
from src.models.ocr_subprocess import OCRSubprocessRunner

def test_preprocessing_solution(large_image_path: str):
    """测试图像预处理解决方案"""
    print("🧪 测试图像预处理解决大图像OCR失败问题")
    print("=" * 60)
    
    large_image_file = Path(large_image_path)
    if not large_image_file.exists():
        print(f"❌ 大图像文件不存在: {large_image_path}")
        return False
    
    # 显示原始图像信息
    try:
        from PIL import Image
        with Image.open(large_image_file) as img:
            width, height = img.size
            file_size = large_image_file.stat().st_size
            print(f"📊 原始图像: {width}×{height}, {file_size/1024:.1f}KB, {img.format}")
    except Exception as e:
        print(f"⚠️ 无法获取图像信息: {e}")
        return False
    
    # 步骤1: 图像预处理
    print(f"\n📋 步骤1: 图像预处理")
    try:
        preprocessor = DocumentImageProcessor()
        print("✅ 图像预处理器创建成功")
        
        # 预处理图像
        start_time = time.time()
        processed_data, stats = preprocessor.process_image(
            image_input=str(large_image_file),
            output_format='JPEG'
        )
        preprocessing_time = time.time() - start_time
        
        print(f"✅ 图像预处理完成，耗时: {preprocessing_time:.2f}秒")
        print(f"📊 预处理统计: {stats}")
        
        # 保存预处理后的图像到临时文件
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as f:
            f.write(processed_data)
            processed_image_path = f.name
        
        print(f"💾 预处理图像保存到: {processed_image_path}")
        
        # 显示预处理后图像信息
        try:
            with Image.open(processed_image_path) as img:
                new_width, new_height = img.size
                new_file_size = Path(processed_image_path).stat().st_size
                print(f"📊 预处理后: {new_width}×{new_height}, {new_file_size/1024:.1f}KB")
                
                # 计算压缩比
                size_reduction = (file_size - new_file_size) / file_size * 100
                pixel_reduction = ((width * height) - (new_width * new_height)) / (width * height) * 100
                print(f"📉 文件大小减少: {size_reduction:.1f}%")
                print(f"📉 像素数量减少: {pixel_reduction:.1f}%")
        except Exception as e:
            print(f"⚠️ 无法获取预处理后图像信息: {e}")
        
    except Exception as e:
        print(f"❌ 图像预处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 步骤2: 使用预处理后的图像进行OCR
    print(f"\n📋 步骤2: OCR处理预处理后的图像")
    try:
        ocr_runner = OCRSubprocessRunner(device_id=0)
        print("✅ OCR运行器创建成功")
        
        # OCR识别
        start_time = time.time()
        result = ocr_runner.recognize(
            image_path=processed_image_path,
            confidence_threshold=0.3,
            use_cpu=True
        )
        ocr_time = time.time() - start_time
        
        print(f"⏱️ OCR处理时间: {ocr_time:.2f}秒")
        
        if result.get('success'):
            text_blocks = result.get('results', [])
            print(f"✅ OCR处理成功!")
            print(f"📝 检测到文本块: {len(text_blocks)}个")
            
            # 显示前5个文本块
            for i, block in enumerate(text_blocks[:5], 1):
                text = block.get('text', '')
                confidence = block.get('confidence', 0)
                print(f"   {i}. '{text}' (置信度: {confidence:.3f})")
            
            if len(text_blocks) > 5:
                print(f"   ... 还有 {len(text_blocks) - 5} 个文本块")
            
            # 清理临时文件
            try:
                Path(processed_image_path).unlink()
            except:
                pass
            
            return True
        else:
            error = result.get('error', '未知错误')
            print(f"❌ OCR处理失败: {error}")
            
            # 清理临时文件
            try:
                Path(processed_image_path).unlink()
            except:
                pass
            
            return False
            
    except Exception as e:
        print(f"❌ OCR测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        
        # 清理临时文件
        try:
            Path(processed_image_path).unlink()
        except:
            pass
        
        return False


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python test_preprocessing_fix.py <大图像路径>")
        print("示例: python test_preprocessing_fix.py G:\\tmp\\Image_00002.jpg")
        return
    
    large_image_path = sys.argv[1]
    success = test_preprocessing_solution(large_image_path)
    
    print(f"\n🎯 测试结果:")
    if success:
        print("✅ 图像预处理成功解决了大图像OCR失败问题!")
        print("💡 解决方案: 通过图像预处理将大图像缩放到合适尺寸，避免OCR子进程崩溃")
    else:
        print("❌ 图像预处理未能解决大图像OCR失败问题")
        print("💡 需要进一步调试和优化")


if __name__ == "__main__":
    main()
