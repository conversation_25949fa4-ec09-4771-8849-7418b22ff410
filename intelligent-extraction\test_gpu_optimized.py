#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GPU优化后的OCR性能
基于GitHub讨论中的建议进行参数调优
"""

import os
import sys
import time
import json
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

from models.ocr_subprocess import OCRSubprocessRunner

def test_optimized_gpu_ocr():
    """测试优化后的GPU OCR"""
    print("🚀 测试优化后的GPU OCR性能...")

    # 测试图片路径
    test_image = project_root / "uploads/125ef0ca-61e6-4cf5-9845-e8b9f3bd1358.jpg"
    print(f"lujing:{test_image}")
    if not test_image.exists():
        print(f"❌ 测试图片不存在: {test_image}")
        return

    print(f"📸 测试图片: {test_image}")

    try:
        # 创建OCR运行器
        ocr_runner = OCRSubprocessRunner(device_id=0)

        # 执行GPU OCR
        start_time = time.time()
        result = ocr_runner.recognize(str(test_image), confidence_threshold=0.5, use_cpu=False)
        end_time = time.time()

        processing_time = end_time - start_time

        print(f"\n⏱️ GPU处理时间: {processing_time:.2f}秒")

        if result and result.get('success'):
            ocr_results = result.get('results', [])
            print(f"✅ 检测到文本块数量: {len(ocr_results)}")

            if len(ocr_results) > 0:
                print("\n📝 检测到的文本内容:")
                for i, item in enumerate(ocr_results):
                    text = item.get('text', '')
                    confidence = item.get('confidence', 0)
                    print(f"  {i+1}. {text} (置信度: {confidence:.3f})")

                print(f"\n🎯 GPU模式成功！检测到 {len(ocr_results)} 个文本块")
                return True
            else:
                print("❌ GPU模式仍然返回空结果")
                return False
        else:
            error_msg = result.get('error', '未知错误') if result else '无结果返回'
            print(f"❌ OCR处理失败: {error_msg}")
            return False

    except Exception as e:
        print(f"❌ GPU OCR测试失败: {str(e)}")
        return False

def compare_cpu_gpu_performance():
    """比较CPU和GPU性能"""
    print("\n" + "="*60)
    print("🔄 CPU vs GPU 性能对比测试")
    print("="*60)
    
    test_image = project_root / "uploads/125ef0ca-61e6-4cf5-9845-e8b9f3bd1358.jpg"
    
    if not test_image.exists():
        print(f"❌ 测试图片不存在: {test_image}")
        return
    
    results = {}

    # 创建OCR运行器
    ocr_runner = OCRSubprocessRunner(device_id=0)

    # 测试CPU模式
    print("\n🖥️ 测试CPU模式...")
    try:
        start_time = time.time()
        cpu_result = ocr_runner.recognize(str(test_image), confidence_threshold=0.5, use_cpu=True)
        cpu_time = time.time() - start_time

        cpu_text_count = len(cpu_result.get('results', [])) if cpu_result and cpu_result.get('success') else 0
        results['cpu'] = {
            'time': cpu_time,
            'text_count': cpu_text_count,
            'success': cpu_text_count > 0
        }

        print(f"   ⏱️ CPU时间: {cpu_time:.2f}秒")
        print(f"   📝 检测文本块: {cpu_text_count}")

    except Exception as e:
        print(f"   ❌ CPU测试失败: {str(e)}")
        results['cpu'] = {'time': 0, 'text_count': 0, 'success': False}

    # 测试GPU模式
    print("\n🚀 测试GPU模式（优化后）...")
    try:
        start_time = time.time()
        gpu_result = ocr_runner.recognize(str(test_image), confidence_threshold=0.5, use_cpu=False)
        gpu_time = time.time() - start_time

        gpu_text_count = len(gpu_result.get('results', [])) if gpu_result and gpu_result.get('success') else 0
        results['gpu'] = {
            'time': gpu_time,
            'text_count': gpu_text_count,
            'success': gpu_text_count > 0
        }

        print(f"   ⏱️ GPU时间: {gpu_time:.2f}秒")
        print(f"   📝 检测文本块: {gpu_text_count}")

    except Exception as e:
        print(f"   ❌ GPU测试失败: {str(e)}")
        results['gpu'] = {'time': 0, 'text_count': 0, 'success': False}
    
    # 性能对比分析
    print("\n" + "="*60)
    print("📊 性能对比结果")
    print("="*60)
    
    if results['cpu']['success'] and results['gpu']['success']:
        speedup = results['cpu']['time'] / results['gpu']['time']
        print(f"🏃 速度提升: {speedup:.2f}x")
        
        if results['cpu']['text_count'] == results['gpu']['text_count']:
            print(f"✅ 检测结果一致: {results['cpu']['text_count']} 个文本块")
        else:
            print(f"⚠️ 检测结果不一致:")
            print(f"   CPU: {results['cpu']['text_count']} 个文本块")
            print(f"   GPU: {results['gpu']['text_count']} 个文本块")
    
    elif results['cpu']['success'] and not results['gpu']['success']:
        print("❌ GPU模式仍然失败，CPU模式正常")
        print("💡 建议: 继续使用CPU模式或进一步调试GPU配置")
    
    elif not results['cpu']['success'] and results['gpu']['success']:
        print("✅ GPU模式修复成功！")
        speedup = results['cpu']['time'] / results['gpu']['time'] if results['cpu']['time'] > 0 else float('inf')
        print(f"🏃 GPU速度: {results['gpu']['time']:.2f}秒")
    
    else:
        print("❌ 两种模式都失败了")
    
    return results

if __name__ == "__main__":
    print("🔧 PaddleOCR GPU优化测试")
    print("基于GitHub讨论 #14767 的建议进行参数调优")
    print("="*60)
    
    # 首先测试优化后的GPU模式
    gpu_success = test_optimized_gpu_ocr()
    
    # 然后进行性能对比
    comparison_results = compare_cpu_gpu_performance()
    
    print("\n" + "="*60)
    print("🎯 测试总结")
    print("="*60)
    
    if gpu_success:
        print("✅ GPU优化成功！")
        print("💡 建议: 可以在生产环境中使用GPU模式")
    else:
        print("❌ GPU优化仍需进一步调试")
        print("💡 建议: 暂时使用CPU模式，继续研究GPU兼容性问题")
