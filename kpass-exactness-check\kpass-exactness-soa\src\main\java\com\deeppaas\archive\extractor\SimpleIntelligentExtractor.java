package com.deeppaas.archive.extractor;

import com.deeppaas.vector.InMemoryVectorService;
import com.deeppaas.vector.model.SimilarTextSegment;
import com.deeppaas.paddleocr.PaddleOCRv3Service;
import com.deeppaas.paddleocr.model.PPChatOCRv4Result;
import com.deeppaas.archive.model.ArchiveElements;
import com.deeppaas.archive.strategy.IntelligentExtractionStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 简化的智能档案要素提取器
 * 基于内存向量和PP-ChatOCRv4
 */
@Component
@Slf4j
public class SimpleIntelligentExtractor {
    
    @Autowired
    private InMemoryVectorService vectorService;

    @Autowired
    private PaddleOCRv3Service paddleOcrService;

    @Autowired
    private IntelligentExtractionStrategy extractionStrategy;
    
    private final ExecutorService executorService = Executors.newFixedThreadPool(4);
    
    /**
     * 批量提取一卷档案的要素
     */
    public Map<String, ArchiveElements> extractVolumeElements(String volumeId, List<File> documentFiles) {
        try {
            log.info("开始批量提取卷 {} 的档案要素，文档数量: {}", volumeId, documentFiles.size());
            long startTime = System.currentTimeMillis();
            
            // 1. 构建内存向量索引
            vectorService.buildVolumeVectors(volumeId, documentFiles);
            
            // 2. 并行提取各文档要素
            Map<String, ArchiveElements> results = extractElementsConcurrently(volumeId, documentFiles);
            
            // 3. 清理内存向量
            vectorService.clearVolumeVectors(volumeId);
            
            long endTime = System.currentTimeMillis();
            log.info("卷 {} 要素提取完成，耗时: {}ms，成功提取: {} 个文档", 
                volumeId, endTime - startTime, results.size());
            
            return results;
            
        } catch (Exception e) {
            log.error("批量要素提取失败: {}", volumeId, e);
            // 确保清理内存
            vectorService.clearVolumeVectors(volumeId);
            throw new RuntimeException("要素提取失败", e);
        }
    }
    
    /**
     * 并行提取要素
     */
    private Map<String, ArchiveElements> extractElementsConcurrently(String volumeId, List<File> documentFiles) {
        List<CompletableFuture<Map.Entry<String, ArchiveElements>>> futures = new ArrayList<>();
        
        for (File file : documentFiles) {
            CompletableFuture<Map.Entry<String, ArchiveElements>> future = 
                CompletableFuture.supplyAsync(() -> {
                    try {
                        ArchiveElements elements = extractSingleDocumentElements(volumeId, file);
                        return new AbstractMap.SimpleEntry<>(file.getName(), elements);
                    } catch (Exception e) {
                        log.error("提取文档要素失败: {}", file.getName(), e);
                        return new AbstractMap.SimpleEntry<>(file.getName(), new ArchiveElements());
                    }
                }, executorService);
            
            futures.add(future);
        }
        
        // 等待所有提取完成
        return futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue
            ));
    }
    
    /**
     * 提取单个文档的要素（使用智能策略）
     */
    private ArchiveElements extractSingleDocumentElements(String volumeId, File documentFile) {
        try {
            log.debug("开始智能提取文档要素: {}", documentFile.getName());

            // 1. 构建档案要素提取的上下文
            String volumeContext = vectorService.getVolumeContext(volumeId, 5000);

            // 2. 使用智能提取策略
            ArchiveElements elements = extractionStrategy.extractElements(documentFile, volumeContext);

            // 3. 基于向量检索进行结果验证和补充
            elements = enhanceWithVectorSearch(volumeId, elements, documentFile.getName());

            log.debug("智能要素提取完成: {} -> 方法:{}, 平均置信度:{:.2f}",
                documentFile.getName(), elements.getExtractionMethod(), elements.getAverageConfidence());

            return elements;

        } catch (Exception e) {
            log.error("智能要素提取失败: {}", documentFile.getName(), e);
            return fallbackExtraction(documentFile);
        }
    }

    /**
     * 降级提取方案
     */
    private ArchiveElements fallbackExtraction(File documentFile) {
        log.warn("使用降级提取方案: {}", documentFile.getName());

        try {
            // 使用最基础的OCR + 规则提取
            ArchiveElements elements = new ArchiveElements();
            elements.setFileName(documentFile.getName());
            elements.setExtractionMethod("FALLBACK_BASIC");

            // 设置保守的置信度
            Map<String, Double> confidence = new HashMap<>();
            confidence.put("title", 0.5);
            confidence.put("responsible_party", 0.5);
            confidence.put("document_number", 0.6);
            confidence.put("issue_date", 0.6);
            elements.setConfidenceScores(confidence);

            return elements;

        } catch (Exception e) {
            log.error("降级提取也失败: {}", documentFile.getName(), e);
            return new ArchiveElements();
        }
    }
    
    /**
     * 构建档案要素提取Prompt
     */
    private String buildArchiveExtractionPrompt(String fileName, String volumeContext) {
        return String.format("""
            你是一个专业的档案管理专家，正在处理档案卷中的文档。
            
            当前处理文档：%s
            
            卷内上下文信息：
            %s
            
            请从当前文档中准确提取以下四个档案要素：
            
            1. 题名（文档标题）：
               - 文档的正式标题，通常在顶部中央
               - 字体较大，内容简洁
               - 如：《关于加强档案管理的通知》
            
            2. 责任者（发文单位/签发人）：
               - 发文单位：如"XX部"、"XX局"、"XX委员会"
               - 签发人：个人姓名和职务
               - 通常在文档头部或尾部
            
            3. 文号（文件编号）：
               - 格式：机关简称〔年份〕序号
               - 如：国发〔2023〕15号、教发[2023]20号
               - 通常在文档头部显著位置
            
            4. 发文日期：
               - 格式：YYYY年MM月DD日、YYYY-MM-DD等
               - 文档的正式发布日期
               - 通常在文档尾部
            
            提取要求：
            - 只提取当前文档中的信息，不要使用卷内其他文档的信息
            - 如果某个要素不存在，返回空字符串
            - 提取内容要准确完整，不添加额外修饰
            - 返回JSON格式结果，包含要素值和置信度
            
            返回格式：
            {
                "title": "提取的题名",
                "title_confidence": 0.95,
                "responsible_party": "提取的责任者",
                "responsible_party_confidence": 0.90,
                "document_number": "提取的文号",
                "document_number_confidence": 0.98,
                "issue_date": "提取的发文日期",
                "issue_date_confidence": 0.92
            }
            """, fileName, volumeContext.substring(0, Math.min(1000, volumeContext.length())));
    }
    
    /**
     * 解析提取结果
     */
    private ArchiveElements parseExtractionResult(PPChatOCRv4Result chatResult, String fileName) {
        ArchiveElements elements = new ArchiveElements();
        elements.setFileName(fileName);
        
        try {
            Map<String, Object> extractedInfo = chatResult.getExtractedInfo();
            
            if (extractedInfo.containsKey("structured_result")) {
                String jsonResult = (String) extractedInfo.get("structured_result");
                Map<String, Object> data = parseJsonResult(jsonResult);
                
                elements.setTitle(getStringValue(data, "title"));
                elements.setResponsibleParty(getStringValue(data, "responsible_party"));
                elements.setDocumentNumber(getStringValue(data, "document_number"));
                elements.setIssueDate(getStringValue(data, "issue_date"));
                
                // 设置置信度
                Map<String, Double> confidenceScores = new HashMap<>();
                confidenceScores.put("title", getDoubleValue(data, "title_confidence"));
                confidenceScores.put("responsible_party", getDoubleValue(data, "responsible_party_confidence"));
                confidenceScores.put("document_number", getDoubleValue(data, "document_number_confidence"));
                confidenceScores.put("issue_date", getDoubleValue(data, "issue_date_confidence"));
                
                elements.setConfidenceScores(confidenceScores);
            }
            
        } catch (Exception e) {
            log.warn("解析提取结果失败: {}", fileName, e);
            // 使用基础解析作为降级
            parseBasicResult(chatResult, elements);
        }
        
        return elements;
    }
    
    /**
     * 基于向量检索增强结果
     */
    private ArchiveElements enhanceWithVectorSearch(String volumeId, ArchiveElements elements, String fileName) {
        try {
            // 对于置信度较低的要素，尝试通过向量检索找到相似内容进行验证
            if (elements.getConfidenceScore("title") < 0.8 && !isEmpty(elements.getTitle())) {
                List<SimilarTextSegment> similarTexts = vectorService.searchSimilarTexts(
                    volumeId, elements.getTitle(), 3);
                
                // 如果找到高相似度的文本，可以提高置信度
                if (!similarTexts.isEmpty() && similarTexts.get(0).getSimilarity() > 0.9) {
                    elements.updateConfidenceScore("title", 0.9);
                }
            }
            
            // 类似地处理其他要素...
            
        } catch (Exception e) {
            log.warn("向量检索增强失败: {}", fileName, e);
        }
        
        return elements;
    }
    
    /**
     * 解析JSON结果
     */
    private Map<String, Object> parseJsonResult(String jsonResult) {
        try {
            // 这里使用JSON解析库，如Jackson或Gson
            // 简化示例，实际需要引入JSON库
            return new HashMap<>(); // 实际实现需要JSON解析
        } catch (Exception e) {
            log.error("JSON解析失败", e);
            return new HashMap<>();
        }
    }
    
    /**
     * 基础结果解析（降级方案）
     */
    private void parseBasicResult(PPChatOCRv4Result chatResult, ArchiveElements elements) {
        // 从chatResult中提取基础信息
        Map<String, Object> extractedInfo = chatResult.getExtractedInfo();
        
        elements.setTitle((String) extractedInfo.getOrDefault("title", ""));
        elements.setResponsibleParty((String) extractedInfo.getOrDefault("responsible_party", ""));
        elements.setDocumentNumber((String) extractedInfo.getOrDefault("document_number", ""));
        elements.setIssueDate((String) extractedInfo.getOrDefault("issue_date", ""));
        
        // 设置默认置信度
        Map<String, Double> defaultConfidence = new HashMap<>();
        defaultConfidence.put("title", 0.7);
        defaultConfidence.put("responsible_party", 0.7);
        defaultConfidence.put("document_number", 0.7);
        defaultConfidence.put("issue_date", 0.7);
        elements.setConfidenceScores(defaultConfidence);
    }
    
    private String getStringValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        return value != null ? value.toString() : "";
    }
    
    private Double getDoubleValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return 0.7; // 默认置信度
    }
    
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
}
