#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志级别调试脚本
"""
import logging
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.logger_config import setup_logging

def test_logging_levels():
    """测试不同模块的日志级别"""
    
    print("=" * 60)
    print("日志级别调试测试")
    print("=" * 60)
    
    # 1. 设置日志配置
    print("\n1. 设置日志配置...")
    setup_logging(level="INFO", force_reconfigure=True)
    
    # 2. 获取不同层级的日志器
    loggers = {
        'root': logging.getLogger(),
        'src': logging.getLogger('src'),
        'src.services': logging.getLogger('src.services'),
        'src.services.archive_extraction_service': logging.getLogger('src.services.archive_extraction_service'),
        'src.models': logging.getLogger('src.models'),
        'src.models.llm_models': logging.getLogger('src.models.llm_models'),
    }
    
    # 3. 检查每个日志器的配置
    print("\n2. 检查日志器配置:")
    for name, logger in loggers.items():
        effective_level = logger.getEffectiveLevel()
        level_name = logging.getLevelName(effective_level)
        print(f"  {name:40} | 级别: {level_name:8} ({effective_level}) | 处理器数量: {len(logger.handlers)}")
    
    # 4. 测试不同级别的日志输出
    print("\n3. 测试日志输出:")
    test_logger = logging.getLogger('src.services.archive_extraction_service')
    
    print("  测试 DEBUG 级别:")
    test_logger.debug("这是一条DEBUG消息")
    
    print("  测试 INFO 级别:")
    test_logger.info("这是一条INFO消息")
    
    print("  测试 WARNING 级别:")
    test_logger.warning("这是一条WARNING消息")
    
    print("  测试 ERROR 级别:")
    test_logger.error("这是一条ERROR消息")
    
    # 5. 检查根日志器的处理器
    print("\n4. 根日志器处理器详情:")
    root_logger = logging.getLogger()
    for i, handler in enumerate(root_logger.handlers):
        print(f"  处理器 {i}: {type(handler).__name__}")
        print(f"    级别: {logging.getLevelName(handler.level)} ({handler.level})")
        print(f"    格式器: {handler.formatter}")
    
    # 6. 测试手动设置日志级别
    print("\n5. 手动设置日志级别测试:")
    test_logger.setLevel(logging.DEBUG)
    print(f"  设置后的级别: {logging.getLevelName(test_logger.level)}")
    print("  再次测试 DEBUG 输出:")
    test_logger.debug("手动设置级别后的DEBUG消息")
    
    # 7. 检查是否有propagate问题
    print("\n6. 检查日志传播设置:")
    for name, logger in loggers.items():
        print(f"  {name:40} | propagate: {logger.propagate}")

if __name__ == "__main__":
    test_logging_levels()
