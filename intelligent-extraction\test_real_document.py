#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实A4扫描文档测试脚本
支持图像预处理和性能优化
"""
import asyncio
import aiohttp
import json
import time
import os
from pathlib import Path
from PIL import Image, ImageEnhance
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DocumentImageProcessor:
    """文档图像预处理器"""
    
    def __init__(self):
        self.target_long_side = 1600  # 目标长边像素
        self.jpeg_quality = 90        # JPEG压缩质量
        self.enhance_contrast = True   # 是否增强对比度
        self.enhance_sharpness = True  # 是否增强锐度
    
    def analyze_image(self, image_path: Path) -> dict:
        """分析图像基本信息"""
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                file_size = image_path.stat().st_size
                
                # 计算DPI（如果有EXIF信息）
                dpi = img.info.get('dpi', (72, 72))
                
                # 估算实际DPI（基于A4纸张尺寸）
                a4_width_mm, a4_height_mm = 210, 297
                estimated_dpi_w = width / (a4_width_mm / 25.4)
                estimated_dpi_h = height / (a4_height_mm / 25.4)
                
                return {
                    'original_size': (width, height),
                    'file_size_mb': file_size / (1024 * 1024),
                    'aspect_ratio': width / height,
                    'exif_dpi': dpi,
                    'estimated_dpi': (estimated_dpi_w, estimated_dpi_h),
                    'format': img.format,
                    'mode': img.mode
                }
        except Exception as e:
            logger.error(f"图像分析失败: {e}")
            return {}
    
    def optimize_image(self, image_path: Path, output_path: Path = None) -> Path:
        """优化图像用于OCR处理"""
        try:
            with Image.open(image_path) as img:
                # 转换为RGB模式（如果需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 计算缩放比例
                width, height = img.size
                long_side = max(width, height)
                
                if long_side > self.target_long_side:
                    scale_ratio = self.target_long_side / long_side
                    new_width = int(width * scale_ratio)
                    new_height = int(height * scale_ratio)
                    
                    # 使用高质量重采样
                    img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    logger.info(f"图像缩放: {width}×{height} -> {new_width}×{new_height}")
                
                # 图像增强
                if self.enhance_contrast:
                    enhancer = ImageEnhance.Contrast(img)
                    img = enhancer.enhance(1.2)  # 轻微增强对比度
                
                if self.enhance_sharpness:
                    enhancer = ImageEnhance.Sharpness(img)
                    img = enhancer.enhance(1.1)  # 轻微增强锐度
                
                # 保存优化后的图像
                if output_path is None:
                    output_path = image_path.parent / f"optimized_{image_path.name}"
                
                img.save(output_path, 'JPEG', quality=self.jpeg_quality, optimize=True)
                logger.info(f"优化图像保存: {output_path}")
                
                return output_path
                
        except Exception as e:
            logger.error(f"图像优化失败: {e}")
            return image_path


async def test_document_extraction(image_path: str, optimize_image: bool = True):
    """测试真实文档的要素提取"""
    
    # 验证图像文件
    image_file = Path(image_path)
    if not image_file.exists():
        print(f"❌ 图像文件不存在: {image_path}")
        return False
    
    print(f"📄 测试文档: {image_file.name}")
    print("=" * 60)
    
    # 图像预处理
    processor = DocumentImageProcessor()
    
    # 分析原始图像
    print("🔍 分析原始图像...")
    image_info = processor.analyze_image(image_file)
    if image_info:
        print(f"   尺寸: {image_info['original_size'][0]}×{image_info['original_size'][1]}")
        print(f"   文件大小: {image_info['file_size_mb']:.2f} MB")
        print(f"   格式: {image_info['format']} ({image_info['mode']})")
        print(f"   估算DPI: {image_info['estimated_dpi'][0]:.0f}×{image_info['estimated_dpi'][1]:.0f}")
    
    # 图像优化
    test_image_path = image_file
    if optimize_image:
        print("\n🔧 优化图像...")
        start_time = time.time()
        test_image_path = processor.optimize_image(image_file)
        optimization_time = time.time() - start_time
        print(f"   优化耗时: {optimization_time:.2f}秒")
        
        # 分析优化后图像
        optimized_info = processor.analyze_image(test_image_path)
        if optimized_info:
            print(f"   优化后尺寸: {optimized_info['original_size'][0]}×{optimized_info['original_size'][1]}")
            print(f"   优化后大小: {optimized_info['file_size_mb']:.2f} MB")
            size_reduction = (1 - optimized_info['file_size_mb'] / image_info['file_size_mb']) * 100
            print(f"   文件压缩: {size_reduction:.1f}%")
    
    # 执行档案要素提取
    print(f"\n📊 执行档案要素提取...")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 准备文件上传
            data = aiohttp.FormData()
            data.add_field('file', 
                          open(test_image_path, 'rb'), 
                          filename=test_image_path.name,
                          content_type='image/jpeg')
            data.add_field('custom_keys', '题名,责任者,文号,发文日期')
            data.add_field('confidence_threshold', '0.5')
            
            start_time = time.time()
            
            async with session.post("http://localhost:8080/extract/archive", 
                                  data=data) as response:
                
                total_time = time.time() - start_time
                
                if response.status == 200:
                    result = await response.json()
                    
                    print(f"✅ 提取成功")
                    print(f"⏱️ 总处理时间: {total_time:.2f}秒")
                    
                    if result.get('success'):
                        elements = result.get('results', {})
                        print(f"🔧 服务处理时间: {result.get('processing_time', 0):.2f}秒")
                        
                        print(f"\n📋 提取结果:")
                        for key, value in elements.items():
                            status = "✅" if value and value != "null" else "❌"
                            print(f"   {status} {key}: {value}")
                        
                        # 统计提取成功率
                        extracted_count = sum(1 for v in elements.values() if v and v != "null")
                        success_rate = extracted_count / len(elements) * 100
                        print(f"\n📈 提取成功率: {extracted_count}/{len(elements)} ({success_rate:.1f}%)")
                        
                        # OCR信息
                        ocr_info = result.get('ocr_info', {})
                        if ocr_info:
                            print(f"📝 OCR信息:")
                            print(f"   检测文本块: {ocr_info.get('total_blocks', 0)}")
                            print(f"   文本总长度: {ocr_info.get('full_text_length', 0)}")
                            print(f"   置信度阈值: {ocr_info.get('confidence_threshold', 0)}")
                        
                        return True
                    else:
                        print(f"❌ 提取失败: {result.get('error')}")
                        return False
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP错误 {response.status}: {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
        finally:
            # 清理优化后的临时文件
            if optimize_image and test_image_path != image_file:
                try:
                    test_image_path.unlink()
                    logger.info(f"清理临时文件: {test_image_path}")
                except:
                    pass


async def test_multiple_documents(image_paths: list, optimize_image: bool = True):
    """批量测试多个文档"""
    print("🧪 批量文档测试")
    print("=" * 60)
    
    results = []
    total_start_time = time.time()
    
    for i, image_path in enumerate(image_paths, 1):
        print(f"\n📄 测试文档 {i}/{len(image_paths)}")
        print("-" * 40)
        
        result = await test_document_extraction(image_path, optimize_image)
        results.append({
            'path': image_path,
            'success': result
        })
    
    total_time = time.time() - total_start_time
    
    # 统计结果
    print(f"\n{'='*60}")
    print("📊 批量测试总结:")
    
    success_count = sum(1 for r in results if r['success'])
    
    for i, result in enumerate(results, 1):
        status = "✅" if result['success'] else "❌"
        filename = Path(result['path']).name
        print(f"   {i}. {status} {filename}")
    
    print(f"\n总体结果: {success_count}/{len(results)} 成功 ({success_count/len(results)*100:.1f}%)")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均每个文档: {total_time/len(results):.2f}秒")
    
    return success_count == len(results)


def main():
    """主函数 - 等待用户提供图像路径"""
    print("📄 真实A4扫描文档测试工具")
    print("=" * 60)
    print("请提供要测试的图像文件路径，支持以下格式：")
    print("- 单个文件: /path/to/document.jpg")
    print("- 多个文件: /path/to/doc1.jpg,/path/to/doc2.jpg")
    print("- 文件夹: /path/to/documents/")
    print()
    
    # 示例用法说明
    print("💡 使用示例:")
    print("   python test_real_document.py")
    print("   然后输入图像路径，例如:")
    print("   C:\\Documents\\scan001.jpg")
    print("   或")
    print("   C:\\Documents\\scan001.jpg,C:\\Documents\\scan002.jpg")
    print()
    
    # 等待用户输入
    user_input = input("请输入图像路径: ").strip()
    
    if not user_input:
        print("❌ 未提供图像路径")
        return
    
    # 解析输入
    if ',' in user_input:
        # 多个文件
        image_paths = [path.strip() for path in user_input.split(',')]
        print(f"📋 将测试 {len(image_paths)} 个文档")
        asyncio.run(test_multiple_documents(image_paths))
    else:
        # 单个文件或文件夹
        path = Path(user_input.strip())
        if path.is_file():
            print("📄 单文档测试模式")
            asyncio.run(test_document_extraction(str(path)))
        elif path.is_dir():
            # 扫描文件夹中的图像文件
            image_extensions = {'.jpg', '.jpeg', '.png', '.tiff', '.tif', '.bmp'}
            image_files = [
                str(f) for f in path.iterdir() 
                if f.is_file() and f.suffix.lower() in image_extensions
            ]
            
            if image_files:
                print(f"📁 文件夹模式: 找到 {len(image_files)} 个图像文件")
                asyncio.run(test_multiple_documents(image_files))
            else:
                print("❌ 文件夹中未找到支持的图像文件")
        else:
            print("❌ 路径不存在或不是有效的文件/文件夹")


if __name__ == "__main__":
    main()
