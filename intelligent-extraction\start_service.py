#!/usr/bin/env python3
"""
启动智能信息提取服务
位置: intelligent-extraction/start_service.py
"""
import os
import sys
from pathlib import Path

# 基本环境设置
# 注意：PaddleOCR现在通过子进程运行，不需要特殊的环境变量设置

# 禁用PaddlePaddle的一些可能冲突的功能
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# 首先配置日志
from src.utils.logger_config import create_project_logger
logger = create_project_logger("intelligent-extraction")

from src.api.web_service import run_web_service

def main():
    """启动服务"""
    logger.info("🚀 启动智能信息提取服务")
    logger.info("=" * 50)
    logger.info(f"项目目录: {project_dir}")
    logger.info(f"工作目录: {os.getcwd()}")

    # 检查环境
    logger.info("🔍 环境检查:")
    
    try:
        import torch
        logger.info(f"✅ PyTorch: {torch.__version__}")
        logger.info(f"✅ CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            logger.info(f"✅ GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                logger.info(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
    except ImportError:
        logger.error("❌ PyTorch未安装")
    
    try:
        import requests
        # 检查Ollama服务
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            logger.info(f"✅ Ollama服务运行中，已安装模型: {len(models)}个")
        else:
            logger.warning("⚠️ Ollama服务状态异常")
    except Exception:
        logger.error("❌ Ollama服务未启动，请先运行: ollama serve")
    
    try:
        from src.models import check_model_compatibility
        compatibility = check_model_compatibility()
        logger.info(f"✅ 模型兼容性检查: {'通过' if all(compatibility.values()) else '部分通过'}")
    except Exception as e:
        logger.warning(f"⚠️ 模型兼容性检查失败: {e}")

    logger.info("🌐 启动Web服务...")
    logger.info("服务地址: http://localhost:8080")
    logger.info("API文档: http://localhost:8080/docs")
    logger.info("健康检查: http://localhost:8080/health")
    logger.info("按 Ctrl+C 停止服务")
    
    try:
        # 启动服务
        run_web_service(
            config_path=None,  # 使用默认配置
            host="0.0.0.0",
            port=8080
        )
    except KeyboardInterrupt:
        logger.info("⏹️ 服务已停止")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
