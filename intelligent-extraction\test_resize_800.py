#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试800像素缩放效果
"""
import sys
import os
import io
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.image_preprocessor import DocumentImageProcessor
from PIL import Image

def test_resize_logic():
    """测试缩放逻辑"""
    print("🔧 测试800像素缩放逻辑")
    print("=" * 50)
    
    # 初始化处理器
    processor = DocumentImageProcessor()
    
    # 显示当前配置
    print(f"📋 当前配置:")
    print(f"  目标长边: {processor.config['target_long_side']}px")
    print(f"  缩放阈值: {processor.config['min_resize_threshold']}px")
    
    # 测试不同尺寸的图像
    test_sizes = [
        (400, 300, "小图像"),
        (800, 600, "刚好800px"),
        (1200, 900, "中等图像"),
        (2461, 3526, "A4扫描图像"),
        (3000, 4000, "大图像")
    ]
    
    print(f"\n🖼️  测试不同尺寸:")
    for width, height, desc in test_sizes:
        long_side = max(width, height)
        needs_resize = long_side > processor.config['min_resize_threshold']
        
        if needs_resize:
            scale_ratio = processor.config['target_long_side'] / long_side
            new_width = int(width * scale_ratio)
            new_height = int(height * scale_ratio)
            new_long_side = max(new_width, new_height)
            
            print(f"  {desc}: {width}×{height} -> {new_width}×{new_height}")
            print(f"    长边: {long_side} -> {new_long_side} (缩放: {scale_ratio:.3f})")
        else:
            print(f"  {desc}: {width}×{height} (无需缩放)")
    
    # 如果有测试图像，实际测试处理
    test_image = Path("test_processed_image.jpg")
    if test_image.exists():
        print(f"\n🖼️  实际测试图像: {test_image.name}")
        
        # 获取原始图像信息
        with Image.open(test_image) as img:
            original_size = img.size
            print(f"  原始尺寸: {original_size[0]}×{original_size[1]}")
        
        # 处理图像
        processed_bytes, result_info = processor.process_image(test_image, "JPEG")

        if processed_bytes:
            # 从字节数据创建图像来检查尺寸
            processed_img = Image.open(io.BytesIO(processed_bytes))
            new_size = processed_img.size
            print(f"  处理后尺寸: {new_size[0]}×{new_size[1]}")
            print(f"  处理时间: {result_info.get('processing_time', 0):.3f}秒")
            print(f"  文件大小变化: {result_info.get('original_size_kb', 0):.1f}KB -> {result_info.get('processed_size_kb', 0):.1f}KB")
            print(f"  估算DPI: {result_info.get('estimated_dpi', 'N/A')}")
            print(f"  长边缩放: {result_info.get('long_side', 0)} -> {max(new_size)}")
            processed_img.close()
        else:
            print(f"  ❌ 处理失败: {result_info.get('error', '未知错误')}")
    else:
        print(f"\n⚠️  测试图像不存在: {test_image}")

if __name__ == "__main__":
    test_resize_logic()
