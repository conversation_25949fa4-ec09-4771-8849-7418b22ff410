#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR混合模式处理器
智能GPU/CPU回退机制，解决GPU模式空结果问题
位置: intelligent-extraction/src/models/ocr_hybrid.py
"""

import time
import logging
from pathlib import Path
from typing import Dict, Any, List, Union, Optional
from .ocr_subprocess import OCRSubprocessRunner

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HybridOCRProcessor:
    """
    混合OCR处理器
    
    特性：
    1. 智能GPU/CPU回退机制
    2. 性能监控和统计
    3. 自动故障恢复
    4. 结果验证
    """
    
    def __init__(self, device_id: int = 0, confidence_threshold: float = 0.5):
        """
        初始化混合OCR处理器
        
        Args:
            device_id: GPU设备ID
            confidence_threshold: 置信度阈值
        """
        self.device_id = device_id
        self.confidence_threshold = confidence_threshold
        self.logger = logging.getLogger(__name__)
        
        # 创建OCR运行器
        self.ocr_runner = OCRSubprocessRunner(device_id=device_id)
        
        # 性能统计
        self.stats = {
            'gpu_attempts': 0,
            'gpu_successes': 0,
            'gpu_failures': 0,
            'cpu_fallbacks': 0,
            'total_processing_time': 0.0,
            'gpu_time': 0.0,
            'cpu_time': 0.0
        }
        
        # GPU可用性状态
        self.gpu_available = True
        self.gpu_failure_count = 0
        self.max_gpu_failures = 3  # 连续失败3次后暂时禁用GPU
        
        self.logger.info(f"混合OCR处理器初始化完成 (device_id={device_id})")
    
    def recognize(self, image_path: Union[str, Path], 
                  force_cpu: bool = False,
                  enable_fallback: bool = True) -> Dict[str, Any]:
        """
        智能OCR识别
        
        Args:
            image_path: 图像路径
            force_cpu: 强制使用CPU模式
            enable_fallback: 启用GPU->CPU回退
            
        Returns:
            Dict: OCR结果，包含性能信息
        """
        start_time = time.time()
        result = None
        mode_used = None
        
        try:
            # 决定处理模式
            if force_cpu or not self.gpu_available:
                # 直接使用CPU模式
                result = self._process_with_cpu(image_path)
                mode_used = 'cpu'
            else:
                # 尝试GPU模式
                result = self._process_with_gpu(image_path, enable_fallback)
                mode_used = result.get('mode_used', 'unknown')
            
            # 计算总处理时间
            total_time = time.time() - start_time
            self.stats['total_processing_time'] += total_time
            
            # 添加性能信息到结果
            if result:
                result['performance'] = {
                    'total_time': total_time,
                    'mode_used': mode_used,
                    'gpu_available': self.gpu_available,
                    'fallback_enabled': enable_fallback
                }
            
            return result
            
        except Exception as e:
            self.logger.error(f"OCR识别失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'results': [],
                'performance': {
                    'total_time': time.time() - start_time,
                    'mode_used': 'error',
                    'gpu_available': self.gpu_available
                }
            }
    
    def _process_with_gpu(self, image_path: Union[str, Path], 
                         enable_fallback: bool = True) -> Dict[str, Any]:
        """
        GPU模式处理（带回退机制）
        
        Args:
            image_path: 图像路径
            enable_fallback: 启用回退机制
            
        Returns:
            Dict: OCR结果
        """
        self.stats['gpu_attempts'] += 1
        gpu_start_time = time.time()
        
        try:
            # 尝试GPU处理
            self.logger.info("🚀 尝试GPU模式处理...")
            result = self.ocr_runner.recognize(
                image_path, 
                confidence_threshold=self.confidence_threshold,
                use_cpu=False
            )
            
            gpu_time = time.time() - gpu_start_time
            self.stats['gpu_time'] += gpu_time
            
            # 验证GPU结果
            if self._is_valid_result(result):
                # GPU成功
                self.stats['gpu_successes'] += 1
                self.gpu_failure_count = 0  # 重置失败计数
                
                result['mode_used'] = 'gpu'
                result['processing_time'] = gpu_time
                
                self.logger.info(f"✅ GPU模式成功 ({gpu_time:.2f}s, {len(result.get('results', []))}个文本块)")
                return result
            else:
                # GPU返回空结果
                self.logger.warning("⚠️ GPU模式返回空结果")
                self._handle_gpu_failure()
                
                if enable_fallback:
                    self.logger.info("🔄 启动CPU回退模式...")
                    return self._process_with_cpu_fallback(image_path, gpu_time)
                else:
                    return {
                        'success': False,
                        'error': 'GPU模式返回空结果，回退已禁用',
                        'results': [],
                        'mode_used': 'gpu_failed'
                    }
                    
        except Exception as e:
            gpu_time = time.time() - gpu_start_time
            self.stats['gpu_time'] += gpu_time
            
            self.logger.error(f"❌ GPU模式异常: {e}")
            self._handle_gpu_failure()
            
            if enable_fallback:
                self.logger.info("🔄 GPU异常，启动CPU回退模式...")
                return self._process_with_cpu_fallback(image_path, gpu_time)
            else:
                return {
                    'success': False,
                    'error': f'GPU模式异常: {e}',
                    'results': [],
                    'mode_used': 'gpu_error'
                }
    
    def _process_with_cpu(self, image_path: Union[str, Path]) -> Dict[str, Any]:
        """
        CPU模式处理
        
        Args:
            image_path: 图像路径
            
        Returns:
            Dict: OCR结果
        """
        cpu_start_time = time.time()
        
        try:
            self.logger.info("🖥️ 使用CPU模式处理...")
            result = self.ocr_runner.recognize(
                image_path,
                confidence_threshold=self.confidence_threshold,
                use_cpu=True
            )
            
            cpu_time = time.time() - cpu_start_time
            self.stats['cpu_time'] += cpu_time
            
            if result:
                result['mode_used'] = 'cpu'
                result['processing_time'] = cpu_time
                
                text_count = len(result.get('results', []))
                self.logger.info(f"✅ CPU模式完成 ({cpu_time:.2f}s, {text_count}个文本块)")
            
            return result
            
        except Exception as e:
            cpu_time = time.time() - cpu_start_time
            self.stats['cpu_time'] += cpu_time
            
            self.logger.error(f"❌ CPU模式异常: {e}")
            return {
                'success': False,
                'error': f'CPU模式异常: {e}',
                'results': [],
                'mode_used': 'cpu_error'
            }
    
    def _process_with_cpu_fallback(self, image_path: Union[str, Path], 
                                  gpu_time: float) -> Dict[str, Any]:
        """
        CPU回退处理
        
        Args:
            image_path: 图像路径
            gpu_time: GPU处理时间
            
        Returns:
            Dict: OCR结果
        """
        self.stats['cpu_fallbacks'] += 1
        
        result = self._process_with_cpu(image_path)
        
        if result:
            result['mode_used'] = 'cpu_fallback'
            result['gpu_time'] = gpu_time
            result['fallback_reason'] = 'GPU模式失败或返回空结果'
        
        return result
    
    def _is_valid_result(self, result: Optional[Dict[str, Any]]) -> bool:
        """
        验证OCR结果是否有效
        
        Args:
            result: OCR结果
            
        Returns:
            bool: 是否有效
        """
        if not result:
            return False
        
        if not result.get('success', False):
            return False
        
        results = result.get('results', [])
        if not results or len(results) == 0:
            return False
        
        return True
    
    def _handle_gpu_failure(self):
        """处理GPU失败"""
        self.stats['gpu_failures'] += 1
        self.gpu_failure_count += 1
        
        if self.gpu_failure_count >= self.max_gpu_failures:
            self.logger.warning(f"⚠️ GPU连续失败{self.gpu_failure_count}次，暂时禁用GPU模式")
            self.gpu_available = False
    
    def reset_gpu_status(self):
        """重置GPU状态（用于手动恢复）"""
        self.gpu_available = True
        self.gpu_failure_count = 0
        self.logger.info("🔄 GPU状态已重置")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            Dict: 性能统计
        """
        stats = self.stats.copy()
        
        # 计算成功率
        if stats['gpu_attempts'] > 0:
            stats['gpu_success_rate'] = stats['gpu_successes'] / stats['gpu_attempts']
        else:
            stats['gpu_success_rate'] = 0.0
        
        # 计算平均处理时间
        if stats['gpu_successes'] > 0:
            stats['avg_gpu_time'] = stats['gpu_time'] / stats['gpu_successes']
        else:
            stats['avg_gpu_time'] = 0.0
        
        if stats['cpu_fallbacks'] > 0:
            stats['avg_cpu_time'] = stats['cpu_time'] / stats['cpu_fallbacks']
        else:
            stats['avg_cpu_time'] = 0.0
        
        # 添加状态信息
        stats['gpu_available'] = self.gpu_available
        stats['gpu_failure_count'] = self.gpu_failure_count
        
        return stats
    
    def __str__(self) -> str:
        return f"HybridOCRProcessor(device_id={self.device_id}, gpu_available={self.gpu_available})"


if __name__ == "__main__":
    # 测试代码
    processor = HybridOCRProcessor()
    print("混合OCR处理器创建成功")
    print(processor.get_performance_stats())
