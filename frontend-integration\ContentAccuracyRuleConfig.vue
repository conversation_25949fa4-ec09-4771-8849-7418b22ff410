<template>
  <div class="content-accuracy-rule-config">
    <!-- 规则基本信息 -->
    <el-card class="rule-basic-info" shadow="never">
      <div slot="header" class="card-header">
        <span class="rule-title">条目内容正确性检查</span>
        <el-tag type="info" size="small">AI智能提取</el-tag>
      </div>
      
      <div class="rule-description">
        <p>通过AI智能提取技术，自动识别档案文档中的关键信息，与Excel录入数据进行比对，发现内容不一致的问题。</p>
        <p class="support-fields">支持字段：题名、责任者、文号、成文日期</p>
      </div>
    </el-card>

    <!-- 字段选择配置 -->
    <el-card class="field-selection-config" shadow="never">
      <div slot="header" class="card-header">
        <span>检查字段配置</span>
        <el-tooltip content="选择需要进行内容正确性检查的字段" placement="top">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div>

      <div class="field-selection-grid">
        <div class="field-group">
          <h4>档案要素字段</h4>
          <div class="field-checkboxes">
            <el-checkbox-group v-model="selectedFields" @change="onFieldSelectionChange">
              <el-checkbox 
                v-for="field in availableFields" 
                :key="field.code"
                :label="field.code"
                :disabled="!field.enabled"
                class="field-checkbox"
              >
                <div class="field-checkbox-content">
                  <span class="field-name">{{ field.name }}</span>
                  <span class="field-description">{{ field.description }}</span>
                  <el-tag v-if="!field.enabled" type="info" size="mini">即将支持</el-tag>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>

      <!-- 字段映射配置 -->
      <div v-if="selectedFields.length > 0" class="field-mapping-config">
        <h4>Excel字段映射</h4>
        <el-table :data="fieldMappingData" border size="small">
          <el-table-column prop="archiveField" label="档案要素" width="120">
            <template slot-scope="scope">
              <el-tag type="primary" size="small">{{ getFieldName(scope.row.archiveField) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="excelColumn" label="Excel列名" width="200">
            <template slot-scope="scope">
              <el-select 
                v-model="scope.row.excelColumn" 
                placeholder="选择Excel列"
                size="small"
                @change="onMappingChange"
              >
                <el-option
                  v-for="column in excelColumns"
                  :key="column.value"
                  :label="column.label"
                  :value="column.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="matchStrategy" label="匹配策略" width="150">
            <template slot-scope="scope">
              <el-select 
                v-model="scope.row.matchStrategy" 
                size="small"
                @change="onMappingChange"
              >
                <el-option label="完全匹配" value="exact" />
                <el-option label="模糊匹配" value="fuzzy" />
                <el-option label="智能匹配" value="smart" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="tolerance" label="容错率" width="120">
            <template slot-scope="scope">
              <el-slider
                v-model="scope.row.tolerance"
                :min="0"
                :max="100"
                :step="5"
                size="small"
                @change="onMappingChange"
              />
              <span class="tolerance-value">{{ scope.row.tolerance }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button 
                type="text" 
                size="small" 
                @click="testFieldMapping(scope.row)"
                :loading="scope.row.testing"
              >
                测试
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 系统配置信息展示（只读） -->
    <el-card class="system-config-info" shadow="never">
      <div slot="header" class="card-header">
        <span>系统配置信息</span>
        <el-tooltip content="这些配置由系统管理员在配置文件中设置" placement="top">
          <i class="el-icon-info"></i>
        </el-tooltip>
      </div>

      <div class="config-info-grid">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="config-info-item">
              <label>置信度阈值</label>
              <div class="config-value">{{ systemConfig.confidenceThreshold || '0.7' }}</div>
              <p class="config-description">AI提取结果的最低置信度要求</p>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="config-info-item">
              <label>重试次数</label>
              <div class="config-value">{{ systemConfig.maxRetries || '2' }}</div>
              <p class="config-description">提取失败时的重试次数</p>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="config-info-item">
              <label>批处理大小</label>
              <div class="config-value">{{ systemConfig.batchSize || '5' }}</div>
              <p class="config-description">单次处理的文档数量</p>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="config-info-item">
              <label>错误报告级别</label>
              <div class="config-value">{{ getErrorLevelName(systemConfig.errorLevel) }}</div>
              <p class="config-description">错误判定标准模式</p>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 预览和测试 -->
    <el-card class="preview-test" shadow="never">
      <div slot="header" class="card-header">
        <span>配置预览</span>
        <div class="header-actions">
          <el-button size="small" @click="testConfiguration" :loading="testing">
            <i class="el-icon-cpu"></i> 测试配置
          </el-button>
          <el-button size="small" type="primary" @click="saveConfiguration" :loading="saving">
            <i class="el-icon-check"></i> 保存配置
          </el-button>
        </div>
      </div>

      <div class="config-preview">
        <div class="preview-section">
          <h4>检查字段 ({{ selectedFields.length }})</h4>
          <div class="selected-fields">
            <el-tag 
              v-for="fieldCode in selectedFields" 
              :key="fieldCode"
              type="success"
              size="small"
              class="field-tag"
            >
              {{ getFieldName(fieldCode) }}
            </el-tag>
          </div>
        </div>

        <div class="preview-section">
          <h4>字段映射</h4>
          <div class="mapping-preview">
            <div 
              v-for="mapping in fieldMappingData" 
              :key="mapping.archiveField"
              class="mapping-item"
            >
              <span class="archive-field">{{ getFieldName(mapping.archiveField) }}</span>
              <i class="el-icon-right"></i>
              <span class="excel-column">{{ mapping.excelColumn || '未配置' }}</span>
              <el-tag size="mini" :type="mapping.excelColumn ? 'success' : 'warning'">
                {{ mapping.matchStrategy || '默认' }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 测试结果 -->
      <div v-if="testResult" class="test-result">
        <el-alert
          :title="testResult.success ? '配置测试通过' : '配置测试失败'"
          :type="testResult.success ? 'success' : 'error'"
          :description="testResult.message"
          show-icon
          :closable="false"
        />
        
        <div v-if="testResult.details" class="test-details">
          <h5>测试详情</h5>
          <pre>{{ JSON.stringify(testResult.details, null, 2) }}</pre>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'ContentAccuracyRuleConfig',
  props: {
    ruleConfig: {
      type: Object,
      default: () => ({})
    },
    excelColumns: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedFields: [],
      testing: false,
      saving: false,
      testResult: null,
      systemConfig: {}, // 系统配置信息（从后端获取）
      
      // 可用字段配置
      availableFields: [
        {
          code: 'title',
          name: '题名',
          description: '档案文档的标题名称',
          enabled: true
        },
        {
          code: 'responsible',
          name: '责任者',
          description: '文档的责任单位或个人',
          enabled: true
        },
        {
          code: 'documentNo',
          name: '文号',
          description: '文档的编号或字号',
          enabled: true
        },
        {
          code: 'issueDate',
          name: '成文日期',
          description: '文档的成文或发文日期',
          enabled: true
        }
      ],
      
      // 字段映射数据
      fieldMappingData: []
    }
  },
  watch: {
    ruleConfig: {
      handler(newConfig) {
        this.loadConfiguration(newConfig)
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // 加载系统配置
    this.loadSystemConfig()
  },
  methods: {
    // 加载系统配置
    async loadSystemConfig() {
      try {
        const response = await this.$api.getSystemConfig()
        this.systemConfig = response.data || {}
      } catch (error) {
        console.error('加载系统配置失败:', error)
      }
    },

    // 加载配置
    loadConfiguration(config) {
      if (config && config.contentAccuracy) {
        const { selectedFields, fieldMapping } = config.contentAccuracy

        this.selectedFields = selectedFields || []

        // 重建字段映射数据
        this.rebuildFieldMappingData()

        // 应用已保存的映射配置
        if (fieldMapping) {
          this.fieldMappingData.forEach(item => {
            const savedMapping = fieldMapping[item.archiveField]
            if (savedMapping) {
              Object.assign(item, savedMapping)
            }
          })
        }
      }
    },
    
    // 字段选择变化
    onFieldSelectionChange() {
      this.rebuildFieldMappingData()
      this.emitConfigChange()
    },
    
    // 重建字段映射数据
    rebuildFieldMappingData() {
      this.fieldMappingData = this.selectedFields.map(fieldCode => ({
        archiveField: fieldCode,
        excelColumn: '',
        matchStrategy: 'smart',
        tolerance: 80,
        testing: false
      }))
    },
    
    // 映射配置变化
    onMappingChange() {
      this.emitConfigChange()
    },
    
    // 获取字段名称
    getFieldName(fieldCode) {
      const field = this.availableFields.find(f => f.code === fieldCode)
      return field ? field.name : fieldCode
    },
    
    // 获取错误级别名称
    getErrorLevelName(level) {
      const levelMap = {
        'strict': '严格模式',
        'normal': '标准模式',
        'loose': '宽松模式'
      }
      return levelMap[level] || '标准模式'
    },
    
    // 测试字段映射
    async testFieldMapping(mapping) {
      mapping.testing = true
      
      try {
        // 调用后端API测试映射配置
        const response = await this.$api.testFieldMapping({
          archiveField: mapping.archiveField,
          excelColumn: mapping.excelColumn,
          matchStrategy: mapping.matchStrategy,
          tolerance: mapping.tolerance
        })
        
        if (response.success) {
          this.$message.success(`${this.getFieldName(mapping.archiveField)} 映射测试通过`)
        } else {
          this.$message.error(`映射测试失败: ${response.message}`)
        }
      } catch (error) {
        this.$message.error('映射测试失败')
        console.error('Field mapping test error:', error)
      } finally {
        mapping.testing = false
      }
    },
    
    // 测试整体配置
    async testConfiguration() {
      if (this.selectedFields.length === 0) {
        this.$message.warning('请至少选择一个检查字段')
        return
      }
      
      // 检查字段映射完整性
      const incompleteMappings = this.fieldMappingData.filter(item => !item.excelColumn)
      if (incompleteMappings.length > 0) {
        this.$message.warning('请完成所有字段的Excel列映射配置')
        return
      }
      
      this.testing = true
      this.testResult = null
      
      try {
        const configData = this.buildConfigurationData()
        
        const response = await this.$api.testContentAccuracyConfig(configData)
        
        this.testResult = {
          success: response.success,
          message: response.message,
          details: response.details
        }
        
        if (response.success) {
          this.$message.success('配置测试通过')
        } else {
          this.$message.error('配置测试失败')
        }
      } catch (error) {
        this.testResult = {
          success: false,
          message: '测试请求失败',
          details: error.message
        }
        this.$message.error('配置测试失败')
        console.error('Configuration test error:', error)
      } finally {
        this.testing = false
      }
    },
    
    // 保存配置
    async saveConfiguration() {
      if (this.selectedFields.length === 0) {
        this.$message.warning('请至少选择一个检查字段')
        return
      }
      
      this.saving = true
      
      try {
        const configData = this.buildConfigurationData()
        
        // 触发父组件保存
        this.$emit('config-save', {
          ruleCode: 'CONTENT_ACCURACY',
          ruleConfig: configData
        })
        
        this.$message.success('配置保存成功')
      } catch (error) {
        this.$message.error('配置保存失败')
        console.error('Configuration save error:', error)
      } finally {
        this.saving = false
      }
    },
    
    // 构建配置数据
    buildConfigurationData() {
      // 构建字段映射对象
      const fieldMapping = {}
      this.fieldMappingData.forEach(item => {
        fieldMapping[item.archiveField] = {
          excelColumn: item.excelColumn,
          matchStrategy: item.matchStrategy,
          tolerance: item.tolerance
        }
      })

      return {
        selectedFields: this.selectedFields,
        fieldMapping: fieldMapping
      }
    },
    
    // 发出配置变化事件
    emitConfigChange() {
      const configData = this.buildConfigurationData()
      this.$emit('config-change', configData)
    }
  }
}
</script>

<style scoped>
.content-accuracy-rule-config {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rule-title {
  font-size: 16px;
  font-weight: 600;
}

.rule-description {
  color: #666;
  line-height: 1.6;
}

.support-fields {
  color: #409EFF;
  font-weight: 500;
  margin-top: 8px;
}

.field-selection-grid {
  margin-top: 16px;
}

.field-group h4 {
  margin-bottom: 16px;
  color: #333;
}

.field-checkboxes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.field-checkbox {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.3s;
}

.field-checkbox:hover {
  border-color: #409EFF;
  background-color: #f0f9ff;
}

.field-checkbox-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.field-name {
  font-weight: 500;
  color: #333;
}

.field-description {
  font-size: 12px;
  color: #666;
}

.field-mapping-config {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
}

.tolerance-value {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.config-info-grid {
  margin-top: 16px;
}

.config-info-item {
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.config-info-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 12px;
}

.config-value {
  font-size: 16px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 4px;
}

.config-description {
  margin-top: 4px;
  font-size: 11px;
  color: #666;
  line-height: 1.4;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.config-preview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.preview-section h4 {
  margin-bottom: 12px;
  color: #333;
}

.selected-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.field-tag {
  margin: 0;
}

.mapping-preview {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mapping-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.archive-field {
  font-weight: 500;
  color: #409EFF;
}

.excel-column {
  color: #333;
}

.test-result {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.test-details {
  margin-top: 12px;
}

.test-details h5 {
  margin-bottom: 8px;
  color: #333;
}

.test-details pre {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
