#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像预处理模块
通用文档图像智能预处理，优化OCR性能
支持任意尺寸的扫描文档（A4、A3、A2等）
"""
from ..utils.logger_config import get_logger
import time
import io
from pathlib import Path
from typing import Tuple, Optional, Dict, Any, Union
from PIL import Image, ImageEnhance, ImageFilter
import psutil
import os


class DocumentImageProcessor:
    """文档图像预处理器

    通用文档图像预处理，支持任意尺寸的扫描文档
    自动检测图像尺寸，超过阈值时按比例缩放优化OCR性能
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, model_pool=None):
        """初始化图像处理器

        Args:
            config: 配置参数字典
            model_pool: 模型池实例（用于获取印章检测模型）
        """
        self.logger = get_logger(__name__)
        
        # 默认配置
        default_config = {
            'target_long_side': 800,       # 目标长边像素 - 通用文档优化
            'jpeg_quality': 90,            # JPEG压缩质量
            'enhance_contrast': 1.15,      # 对比度增强倍数
            'enhance_sharpness': 1.05,     # 锐度增强倍数
            'remove_exif': True,           # 是否移除EXIF数据
            'enable_denoising': False,     # 是否启用去噪
            'min_resize_threshold': 800,   # 最小缩放阈值 - 超过800就缩放
            'memory_limit_mb': 500,        # 内存使用限制(MB)
        }
        
        # 合并用户配置
        self.config = {**default_config, **(config or {})}

        # 保存模型池引用
        self.model_pool = model_pool

        # 印章检测相关初始化
        self.seal_detector = None
        self._seal_detector_initialized = False

        # 性能统计
        self.stats = {
            'total_processed': 0,
            'total_time': 0.0,
            'memory_saved_mb': 0.0,
            'size_reduction_ratio': 0.0
        }

        self.logger.info(f"图像预处理器初始化完成，配置: {self.config}")
    
    def analyze_image(self, image_input: Union[str, Path, bytes, Image.Image]) -> Dict[str, Any]:
        """分析图像基本信息
        
        Args:
            image_input: 图像输入（文件路径、字节数据或PIL图像对象）
            
        Returns:
            图像分析结果字典
        """
        try:
            # 统一处理不同输入类型
            if isinstance(image_input, (str, Path)):
                with Image.open(image_input) as img:
                    return self._analyze_pil_image(img, Path(image_input))
            elif isinstance(image_input, bytes):
                with Image.open(io.BytesIO(image_input)) as img:
                    return self._analyze_pil_image(img)
            elif isinstance(image_input, Image.Image):
                return self._analyze_pil_image(image_input)
            else:
                raise ValueError(f"不支持的图像输入类型: {type(image_input)}")
                
        except Exception as e:
            self.logger.error(f"图像分析失败: {e}")
            return {}
    
    def _analyze_pil_image(self, img: Image.Image, file_path: Optional[Path] = None) -> Dict[str, Any]:
        """分析PIL图像对象"""
        width, height = img.size
        
        # 计算文件大小（如果有文件路径）
        file_size_mb = 0
        if file_path and file_path.exists():
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
        
        # 通用DPI估算（基于图像尺寸和经验值）
        estimated_dpi_w, estimated_dpi_h = self._estimate_dpi(width, height, file_size_mb)
        
        # 判断是否需要缩放
        long_side = max(width, height)
        needs_resize = long_side > self.config['min_resize_threshold']
        
        return {
            'original_size': (width, height),
            'file_size_mb': file_size_mb,
            'aspect_ratio': width / height,
            'estimated_dpi': (estimated_dpi_w, estimated_dpi_h),
            'format': img.format,
            'mode': img.mode,
            'long_side': long_side,
            'needs_resize': needs_resize,
            'aspect_ratio_category': self._categorize_aspect_ratio(width / height)
        }
    
    def _estimate_dpi(self, width: int, height: int, file_size_mb: float) -> Tuple[float, float]:
        """通用DPI估算方法

        基于图像尺寸、文件大小和经验值进行DPI估算

        Args:
            width: 图像宽度（像素）
            height: 图像高度（像素）
            file_size_mb: 文件大小（MB）

        Returns:
            Tuple[float, float]: (水平DPI, 垂直DPI)
        """
        # 计算总像素数
        total_pixels = width * height

        # 基于像素数的DPI估算
        # 经验值：常见扫描DPI范围
        if total_pixels < 500_000:      # < 0.5MP，可能是低分辨率扫描
            base_dpi = 150
        elif total_pixels < 2_000_000:  # < 2MP，标准扫描
            base_dpi = 200
        elif total_pixels < 8_000_000:  # < 8MP，高质量扫描
            base_dpi = 300
        elif total_pixels < 20_000_000: # < 20MP，专业扫描
            base_dpi = 400
        else:                           # >= 20MP，超高分辨率
            base_dpi = 600

        # 基于文件大小的调整因子
        # 文件越大，通常DPI越高（假设相同压缩比）
        if file_size_mb > 0:
            # 每MB对应的像素数，用于推断压缩质量和DPI
            pixels_per_mb = total_pixels / file_size_mb

            if pixels_per_mb > 2_000_000:   # 高压缩比，可能是高DPI
                dpi_adjustment = 1.2
            elif pixels_per_mb < 500_000:   # 低压缩比，可能是低DPI
                dpi_adjustment = 0.8
            else:
                dpi_adjustment = 1.0
        else:
            dpi_adjustment = 1.0

        # 计算最终DPI
        estimated_dpi = base_dpi * dpi_adjustment

        # 通常水平和垂直DPI相同（方形像素）
        return (estimated_dpi, estimated_dpi)

    def _categorize_aspect_ratio(self, ratio: float) -> str:
        """分类图像宽高比"""
        if ratio > 1.5:
            return "wide"      # 宽图像
        elif ratio < 0.67:
            return "tall"      # 高图像
        else:
            return "standard"  # 标准比例
    
    def process_image(self, 
                     image_input: Union[str, Path, bytes, Image.Image],
                     output_format: str = 'JPEG') -> Tuple[bytes, Dict[str, Any]]:
        """处理图像并返回优化后的字节数据
        
        Args:
            image_input: 图像输入
            output_format: 输出格式 ('JPEG', 'PNG')
            
        Returns:
            (优化后的图像字节数据, 处理统计信息)
        """
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        try:
            # 分析原始图像
            original_info = self.analyze_image(image_input)
            if not original_info:
                raise ValueError("图像分析失败")
            
            # 加载图像
            if isinstance(image_input, (str, Path)):
                img = Image.open(image_input)
            elif isinstance(image_input, bytes):
                img = Image.open(io.BytesIO(image_input))
            elif isinstance(image_input, Image.Image):
                img = image_input.copy()
            else:
                raise ValueError(f"不支持的输入类型: {type(image_input)}")
            
            # 处理流程
            processed_img = self._process_pipeline(img, original_info)
            
            # 转换为字节数据
            output_buffer = io.BytesIO()
            save_kwargs = {'format': output_format, 'optimize': True}
            
            if output_format.upper() == 'JPEG':
                save_kwargs['quality'] = self.config['jpeg_quality']
                # 确保RGB模式用于JPEG
                if processed_img.mode != 'RGB':
                    processed_img = processed_img.convert('RGB')
            
            processed_img.save(output_buffer, **save_kwargs)
            output_bytes = output_buffer.getvalue()
            
            # 计算处理统计
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            processing_stats = {
                'processing_time': end_time - start_time,
                'memory_used_mb': end_memory - start_memory,
                'original_size': original_info['original_size'],
                'processed_size': processed_img.size,
                'original_file_size_mb': original_info.get('file_size_mb', 0),
                'processed_file_size_mb': len(output_bytes) / (1024 * 1024),
                'size_reduction_ratio': 0,
                'needs_resize': original_info['needs_resize'],
                'format': output_format
            }

            # 添加印章处理统计信息
            if hasattr(self, '_last_stamp_info'):
                processing_stats['stamp_processing'] = self._last_stamp_info
                delattr(self, '_last_stamp_info')  # 清理临时数据
            
            # 计算压缩比
            if original_info.get('file_size_mb', 0) > 0:
                processing_stats['size_reduction_ratio'] = (
                    1 - processing_stats['processed_file_size_mb'] / original_info['file_size_mb']
                )
            
            # 更新全局统计
            self._update_stats(processing_stats)
            
            # 清理内存
            img.close()
            processed_img.close()
            
            self.logger.info(
                f"图像处理完成: {original_info['original_size']} -> {processed_img.size}, "
                f"耗时 {processing_stats['processing_time']:.2f}s"
            )
            
            return output_bytes, processing_stats
            
        except Exception as e:
            self.logger.error(f"图像处理失败: {e}")
            raise
    
    def _process_pipeline(self, img: Image.Image, original_info: Dict[str, Any]) -> Image.Image:
        """图像处理流水线"""
        
        # 1. 格式转换
        if img.mode not in ['RGB', 'L']:
            img = img.convert('RGB')
            self.logger.debug(f"转换图像模式到RGB")
        
        # 2. 移除EXIF数据
        if self.config['remove_exif'] and hasattr(img, '_getexif'):
            img = img.copy()  # 移除EXIF
            self.logger.debug("移除EXIF数据")
        
        # 3. 智能缩放
        if original_info['needs_resize']:
            img = self._smart_resize(img, original_info)

        # 4. 印章处理（如果启用）
        stamp_info = self._process_stamps_if_enabled(img)
        if stamp_info.get('versions', {}).get('main'):
            img = stamp_info['versions']['main']
            self.logger.debug(f"使用印章处理后的图像版本")

        # 保存印章处理信息到图像对象（用于后续统计）
        if hasattr(img, '_stamp_info'):
            img._stamp_info = stamp_info
        else:
            # 如果PIL Image不支持自定义属性，使用全局变量
            self._last_stamp_info = stamp_info

        # 5. 图像增强
        img = self._enhance_image(img)

        # 6. 可选去噪
        if self.config['enable_denoising']:
            img = self._denoise_image(img)

        return img
    
    def _smart_resize(self, img: Image.Image, original_info: Dict[str, Any]) -> Image.Image:
        """智能缩放处理"""
        width, height = img.size
        long_side = max(width, height)
        target_long_side = self.config['target_long_side']
        
        if long_side <= target_long_side:
            return img
        
        # 计算缩放比例
        scale_ratio = target_long_side / long_side
        new_width = int(width * scale_ratio)
        new_height = int(height * scale_ratio)
        
        # 使用高质量重采样 - PIL版本兼容性处理
        try:
            # 新版本PIL (Pillow >= 10.0.0)
            resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        except AttributeError:
            # 旧版本PIL
            resized_img = img.resize((new_width, new_height), Image.LANCZOS)
        
        self.logger.debug(
            f"图像缩放: {width}×{height} -> {new_width}×{new_height} "
            f"(比例: {scale_ratio:.3f})"
        )
        
        return resized_img
    
    def _enhance_image(self, img: Image.Image) -> Image.Image:
        """图像增强处理"""
        enhanced_img = img
        
        # 对比度增强
        if self.config['enhance_contrast'] != 1.0:
            enhancer = ImageEnhance.Contrast(enhanced_img)
            enhanced_img = enhancer.enhance(self.config['enhance_contrast'])
            self.logger.debug(f"对比度增强: {self.config['enhance_contrast']}")
        
        # 锐度增强
        if self.config['enhance_sharpness'] != 1.0:
            enhancer = ImageEnhance.Sharpness(enhanced_img)
            enhanced_img = enhancer.enhance(self.config['enhance_sharpness'])
            self.logger.debug(f"锐度增强: {self.config['enhance_sharpness']}")
        
        return enhanced_img
    
    def _denoise_image(self, img: Image.Image) -> Image.Image:
        """去噪处理"""
        # 使用轻微的高斯模糊去除扫描噪点
        denoised_img = img.filter(ImageFilter.GaussianBlur(radius=0.5))
        self.logger.debug("应用去噪处理")
        return denoised_img
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量(MB)"""
        try:
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / (1024 * 1024)
        except:
            return 0.0
    
    def _update_stats(self, processing_stats: Dict[str, Any]):
        """更新全局统计信息"""
        self.stats['total_processed'] += 1
        self.stats['total_time'] += processing_stats['processing_time']
        self.stats['memory_saved_mb'] += processing_stats.get('memory_used_mb', 0)
        
        # 更新平均压缩比
        if processing_stats['size_reduction_ratio'] > 0:
            current_avg = self.stats['size_reduction_ratio']
            count = self.stats['total_processed']
            self.stats['size_reduction_ratio'] = (
                (current_avg * (count - 1) + processing_stats['size_reduction_ratio']) / count
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        stats = self.stats.copy()
        if stats['total_processed'] > 0:
            stats['avg_processing_time'] = stats['total_time'] / stats['total_processed']
        else:
            stats['avg_processing_time'] = 0.0
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_processed': 0,
            'total_time': 0.0,
            'memory_saved_mb': 0.0,
            'size_reduction_ratio': 0.0
        }
        self.logger.info("统计信息已重置")

    def _init_seal_detector(self):
        """从模型池获取印章检测器"""
        if self._seal_detector_initialized:
            return

        stamp_config = self.config.get('stamp_processing', {})
        if stamp_config.get('enabled', False) and self.model_pool:
            try:
                self.seal_detector = self.model_pool.get_seal_detection_model()
                if self.seal_detector:
                    self.logger.info("从模型池获取印章检测器成功")
                else:
                    self.logger.warning("模型池中未找到印章检测模型")
            except Exception as e:
                self.logger.warning(f"从模型池获取印章检测器失败: {e}")
                self.seal_detector = None

        self._seal_detector_initialized = True

    def _process_stamps_if_enabled(self, img: Image.Image) -> Dict[str, Any]:
        """处理印章（使用模型池中的模型）"""
        stamp_config = self.config.get('stamp_processing', {})

        if not stamp_config.get('enabled', False):
            return {'enabled': False}

        # 从模型池获取印章检测器
        self._init_seal_detector()

        if not self.seal_detector:
            return {'enabled': True, 'detector_available': False}

        try:
            # 使用模型池中的印章检测模型
            detection_result = self.seal_detector.detect_seals(
                img,
                confidence_threshold=stamp_config.get('confidence_threshold', 0.8)
            )

            stamp_info = {
                'enabled': True,
                'detector_available': True,
                'detected_stamps': detection_result.detected_stamps,
                'stamp_regions': detection_result.stamp_regions,
                'detection_time': detection_result.detection_time,
                'versions': {}
            }

            # 如果检测到印章，创建处理版本
            if detection_result.detected_stamps > 0 and stamp_config.get('create_multiple_versions', True):
                stamp_info['versions'] = self._create_stamp_processed_versions(
                    img, detection_result.stamp_regions, stamp_config
                )

            return stamp_info

        except Exception as e:
            self.logger.error(f"印章处理失败: {e}")
            return {
                'enabled': True,
                'detector_available': True,
                'error': str(e),
                'detected_stamps': 0
            }

    def _create_stamp_processed_versions(self, img: Image.Image,
                                       stamp_regions: list,
                                       stamp_config: dict) -> Dict[str, Image.Image]:
        """创建印章处理的版本（简化为两个版本）"""
        versions = {
            'original': img.copy()  # 保留原图
        }

        # 只创建红蓝色移除+增强版本
        try:
            versions['redblue_removed'] = self._remove_redblue_channel(img, stamp_regions)
            # 设置主版本为处理后的版本
            versions['main'] = versions['redblue_removed']
        except Exception as e:
            self.logger.warning(f"红蓝色通道移除失败: {e}")
            # 失败时使用原图作为主版本
            versions['main'] = versions['original']

        return versions

    def _remove_redblue_channel(self, img: Image.Image, stamp_regions: list) -> Image.Image:
        """移除红蓝色通道，保留底层文字，同时增强对比度"""
        try:
            import cv2
            import numpy as np
        except ImportError:
            self.logger.error("OpenCV未安装，无法进行印章处理")
            return img

        # PIL转OpenCV
        img_array = np.array(img)
        if len(img_array.shape) == 3:
            img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        else:
            img_bgr = img_array

        # 转换到HSV颜色空间
        hsv = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2HSV)

        # 定义红色范围（HSV）- 红色在HSV中分布在两个区间
        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([160, 50, 50])
        upper_red2 = np.array([180, 255, 255])

        # 定义蓝色范围（HSV）
        lower_blue = np.array([100, 50, 50])
        upper_blue = np.array([130, 255, 255])

        # 创建红色和蓝色掩码
        red_mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        red_mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        red_mask = cv2.bitwise_or(red_mask1, red_mask2)

        blue_mask = cv2.inRange(hsv, lower_blue, upper_blue)

        # 合并红蓝掩码
        redblue_mask = cv2.bitwise_or(red_mask, blue_mask)

        # 在印章区域内应用掩码
        for region in stamp_regions:
            polygon = np.array(region['polygon'], dtype=np.int32)

            # 创建区域掩码
            region_mask = np.zeros(img_bgr.shape[:2], dtype=np.uint8)
            cv2.fillPoly(region_mask, [polygon], 255)

            # 只在印章区域内移除红蓝色
            combined_mask = cv2.bitwise_and(redblue_mask, region_mask)

            # 将红蓝色区域替换为白色（保留底层文字）
            img_bgr[combined_mask > 0] = [255, 255, 255]

        # 转换回PIL并进行对比度增强
        if len(img_array.shape) == 3:
            result_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)
        else:
            result_rgb = img_bgr

        result_img = Image.fromarray(result_rgb)

        # 合并对比度增强逻辑
        enhancer = ImageEnhance.Contrast(result_img)
        result_img = enhancer.enhance(1.2)

        # 锐化处理
        sharpness_enhancer = ImageEnhance.Sharpness(result_img)
        result_img = sharpness_enhancer.enhance(1.3)

        return result_img


# 全局处理器实例（单例模式）
_global_processor = None

def get_image_processor(config: Optional[Dict[str, Any]] = None,
                       model_pool=None,
                       force_reinit: bool = False) -> DocumentImageProcessor:
    """获取全局图像处理器实例

    Args:
        config: 配置参数字典
        model_pool: 模型池实例
        force_reinit: 是否强制重新初始化
    """
    global _global_processor
    if _global_processor is None or force_reinit:
        _global_processor = DocumentImageProcessor(config, model_pool)
    return _global_processor
