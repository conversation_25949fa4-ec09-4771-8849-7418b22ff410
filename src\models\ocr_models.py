"""
OCR模型封装 - 基于PaddleOCR
"""
import os
import logging
import numpy as np
import cv2
from typing import List, Dict, Any, Tuple, Optional, Union
from dataclasses import dataclass
from pathlib import Path

@dataclass
class OCRResult:
    """OCR识别结果"""
    text: str
    confidence: float
    bbox: List[List[int]]  # 边界框坐标
    
@dataclass
class DocumentOCRResult:
    """文档OCR结果"""
    full_text: str
    text_blocks: List[OCRResult]
    image_shape: Tuple[int, int]  # (height, width)
    processing_time: float

class PaddleOCRModel:
    """PaddleOCR模型封装"""
    
    def __init__(self, 
                 lang: str = "ch",
                 use_angle_cls: bool = True,
                 use_gpu: bool = True,
                 gpu_id: int = 0,
                 **kwargs):
        """
        初始化PaddleOCR模型
        
        Args:
            lang: 语言，支持 'ch', 'en', 'fr', 'german', 'korean', 'japan'
            use_angle_cls: 是否使用文字方向分类器
            use_gpu: 是否使用GPU
            gpu_id: GPU设备ID
            **kwargs: 其他PaddleOCR参数
        """
        self.lang = lang
        self.use_angle_cls = use_angle_cls
        self.use_gpu = use_gpu
        self.gpu_id = gpu_id
        self.logger = logging.getLogger(__name__)
        
        # 配置参数
        self.det_model_dir = kwargs.get("det_model_dir", None)
        self.rec_model_dir = kwargs.get("rec_model_dir", None)
        self.cls_model_dir = kwargs.get("cls_model_dir", None)
        
        # 初始化OCR引擎
        self._init_ocr_engine()
    
    def _init_ocr_engine(self):
        """初始化OCR引擎"""
        try:
            # 延迟导入PaddleOCR，避免与PyTorch冲突
            from paddleocr import PaddleOCR
            
            # 设置GPU
            if self.use_gpu:
                os.environ['CUDA_VISIBLE_DEVICES'] = str(self.gpu_id)
            
            # 初始化PaddleOCR
            ocr_kwargs = {
                'use_angle_cls': self.use_angle_cls,
                'lang': self.lang,
                'use_gpu': self.use_gpu,
                'show_log': False,  # 减少日志输出
            }
            
            # 添加自定义模型路径
            if self.det_model_dir:
                ocr_kwargs['det_model_dir'] = self.det_model_dir
            if self.rec_model_dir:
                ocr_kwargs['rec_model_dir'] = self.rec_model_dir
            if self.cls_model_dir:
                ocr_kwargs['cls_model_dir'] = self.cls_model_dir
            
            self.ocr_engine = PaddleOCR(**ocr_kwargs)
            self.logger.info(f"PaddleOCR初始化成功: lang={self.lang}, gpu={self.use_gpu}")
            
        except Exception as e:
            self.logger.error(f"PaddleOCR初始化失败: {e}")
            raise RuntimeError(f"无法初始化PaddleOCR: {e}")
    
    def recognize(self, 
                  image: Union[str, np.ndarray, Path],
                  confidence_threshold: float = 0.5) -> DocumentOCRResult:
        """
        识别图像中的文字
        
        Args:
            image: 图像路径或numpy数组
            confidence_threshold: 置信度阈值
            
        Returns:
            DocumentOCRResult: OCR识别结果
        """
        import time
        start_time = time.time()
        
        try:
            # 预处理图像
            img_array = self._preprocess_image(image)
            
            # 执行OCR识别
            ocr_results = self.ocr_engine.ocr(img_array, cls=self.use_angle_cls)
            
            # 处理结果
            text_blocks = []
            full_text_parts = []
            
            if ocr_results and ocr_results[0]:
                for line in ocr_results[0]:
                    if line and len(line) >= 2:
                        bbox = line[0]  # 边界框
                        text_info = line[1]  # (文本, 置信度)
                        
                        if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = float(text_info[1])
                            
                            # 过滤低置信度结果
                            if confidence >= confidence_threshold:
                                text_blocks.append(OCRResult(
                                    text=text,
                                    confidence=confidence,
                                    bbox=bbox
                                ))
                                full_text_parts.append(text)
            
            # 合并全文
            full_text = '\n'.join(full_text_parts)
            
            # 获取图像尺寸
            if isinstance(img_array, np.ndarray):
                image_shape = img_array.shape[:2]
            else:
                image_shape = (0, 0)
            
            processing_time = time.time() - start_time
            
            result = DocumentOCRResult(
                full_text=full_text,
                text_blocks=text_blocks,
                image_shape=image_shape,
                processing_time=processing_time
            )
            
            self.logger.info(f"OCR识别完成: {len(text_blocks)}个文本块, 耗时: {processing_time:.2f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"OCR识别失败: {e}")
            raise RuntimeError(f"OCR识别失败: {e}")
    
    def _preprocess_image(self, image: Union[str, np.ndarray, Path]) -> np.ndarray:
        """预处理图像"""
        try:
            if isinstance(image, (str, Path)):
                # 从文件路径读取
                image_path = str(image)
                if not os.path.exists(image_path):
                    raise FileNotFoundError(f"图像文件不存在: {image_path}")
                
                img_array = cv2.imread(image_path)
                if img_array is None:
                    raise ValueError(f"无法读取图像文件: {image_path}")
                    
            elif isinstance(image, np.ndarray):
                img_array = image.copy()
            else:
                raise ValueError(f"不支持的图像类型: {type(image)}")
            
            # 基本图像预处理
            if len(img_array.shape) == 3 and img_array.shape[2] == 3:
                # BGR转RGB（PaddleOCR内部会处理，这里保持原样）
                pass
            elif len(img_array.shape) == 2:
                # 灰度图转RGB
                img_array = cv2.cvtColor(img_array, cv2.COLOR_GRAY2BGR)
            
            return img_array
            
        except Exception as e:
            self.logger.error(f"图像预处理失败: {e}")
            raise
    
    def recognize_region(self, 
                        image: Union[str, np.ndarray, Path],
                        bbox: List[int],
                        confidence_threshold: float = 0.5) -> OCRResult:
        """
        识别图像中指定区域的文字
        
        Args:
            image: 图像路径或numpy数组
            bbox: 边界框 [x1, y1, x2, y2]
            confidence_threshold: 置信度阈值
            
        Returns:
            OCRResult: 区域OCR结果
        """
        try:
            # 预处理图像
            img_array = self._preprocess_image(image)
            
            # 裁剪区域
            x1, y1, x2, y2 = bbox
            roi = img_array[y1:y2, x1:x2]
            
            if roi.size == 0:
                return OCRResult(text="", confidence=0.0, bbox=bbox)
            
            # 识别裁剪区域
            ocr_results = self.ocr_engine.ocr(roi, cls=self.use_angle_cls)
            
            # 处理结果
            if ocr_results and ocr_results[0]:
                # 合并所有识别到的文本
                texts = []
                confidences = []
                
                for line in ocr_results[0]:
                    if line and len(line) >= 2:
                        text_info = line[1]
                        if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = float(text_info[1])
                            
                            if confidence >= confidence_threshold:
                                texts.append(text)
                                confidences.append(confidence)
                
                if texts:
                    combined_text = ' '.join(texts)
                    avg_confidence = sum(confidences) / len(confidences)
                    
                    return OCRResult(
                        text=combined_text,
                        confidence=avg_confidence,
                        bbox=bbox
                    )
            
            return OCRResult(text="", confidence=0.0, bbox=bbox)
            
        except Exception as e:
            self.logger.error(f"区域OCR识别失败: {e}")
            return OCRResult(text="", confidence=0.0, bbox=bbox)
    
    def batch_recognize(self, 
                       images: List[Union[str, np.ndarray, Path]],
                       confidence_threshold: float = 0.5) -> List[DocumentOCRResult]:
        """
        批量识别多个图像
        
        Args:
            images: 图像列表
            confidence_threshold: 置信度阈值
            
        Returns:
            List[DocumentOCRResult]: 批量OCR结果
        """
        results = []
        
        for i, image in enumerate(images):
            try:
                result = self.recognize(image, confidence_threshold)
                results.append(result)
                self.logger.debug(f"批量OCR进度: {i+1}/{len(images)}")
            except Exception as e:
                self.logger.error(f"批量OCR第{i+1}个图像失败: {e}")
                # 添加空结果
                results.append(DocumentOCRResult(
                    full_text="",
                    text_blocks=[],
                    image_shape=(0, 0),
                    processing_time=0.0
                ))
        
        return results
    
    def get_text_regions(self, 
                        image: Union[str, np.ndarray, Path],
                        region_type: str = "all") -> List[Dict[str, Any]]:
        """
        获取文本区域信息
        
        Args:
            image: 图像路径或numpy数组
            region_type: 区域类型 "all", "title", "content", "signature"
            
        Returns:
            List[Dict]: 文本区域信息
        """
        try:
            result = self.recognize(image)
            regions = []
            
            for block in result.text_blocks:
                # 计算区域属性
                bbox = block.bbox
                x1, y1 = min(point[0] for point in bbox), min(point[1] for point in bbox)
                x2, y2 = max(point[0] for point in bbox), max(point[1] for point in bbox)
                
                width = x2 - x1
                height = y2 - y1
                area = width * height
                
                # 估算区域类型
                estimated_type = self._estimate_region_type(
                    block.text, (x1, y1, x2, y2), result.image_shape
                )
                
                if region_type == "all" or estimated_type == region_type:
                    regions.append({
                        "text": block.text,
                        "confidence": block.confidence,
                        "bbox": [x1, y1, x2, y2],
                        "type": estimated_type,
                        "area": area,
                        "width": width,
                        "height": height
                    })
            
            return regions
            
        except Exception as e:
            self.logger.error(f"获取文本区域失败: {e}")
            return []
    
    def _estimate_region_type(self, 
                             text: str, 
                             bbox: Tuple[int, int, int, int], 
                             image_shape: Tuple[int, int]) -> str:
        """估算文本区域类型"""
        x1, y1, x2, y2 = bbox
        img_height, img_width = image_shape
        
        # 计算相对位置
        center_y = (y1 + y2) / 2
        relative_y = center_y / img_height if img_height > 0 else 0.5
        
        # 计算文本特征
        text_length = len(text)
        
        # 简单的区域类型判断
        if relative_y < 0.3 and text_length > 5:
            return "title"
        elif relative_y > 0.7 and any(keyword in text for keyword in ["单位", "署名", "日期"]):
            return "signature"
        else:
            return "content"
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_type": "PaddleOCR",
            "language": self.lang,
            "use_angle_cls": self.use_angle_cls,
            "use_gpu": self.use_gpu,
            "gpu_id": self.gpu_id,
            "det_model_dir": self.det_model_dir,
            "rec_model_dir": self.rec_model_dir,
            "cls_model_dir": self.cls_model_dir,
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            if hasattr(self, 'ocr_engine'):
                del self.ocr_engine
            self.logger.info("PaddleOCR模型清理完成")
        except Exception as e:
            self.logger.error(f"PaddleOCR清理失败: {e}")
    
    def __str__(self) -> str:
        return f"PaddleOCRModel(lang={self.lang}, gpu={self.use_gpu})"
