package com.deeppaas.vector;

import com.deeppaas.ollama.OllamaEmbeddingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * 显存优化的Embedding服务
 * 通过批处理、缓存和显存管理来优化ERNIE调用
 */
@Component
@Slf4j
public class MemoryOptimizedEmbeddingService {
    
    @Autowired
    private LightweightEmbeddingService lightweightService;

    @Autowired
    private OllamaEmbeddingService ollamaService;

    @Value("${embedding.ollama.enable:true}")
    private boolean enableOllama;

    @Value("${embedding.ollama.batch_size:8}")
    private int batchSize;
    
    @Value("${embedding.cache.enable:true}")
    private boolean enableCache;
    
    @Value("${embedding.cache.max_size:1000}")
    private int cacheMaxSize;
    
    // 显存使用控制
    private final Semaphore gpuMemorySemaphore;
    
    // 简单的LRU缓存
    private final Map<String, float[]> embeddingCache = new LinkedHashMap<String, float[]>(16, 0.75f, true) {
        @Override
        protected boolean removeEldestEntry(Map.Entry<String, float[]> eldest) {
            return size() > cacheMaxSize;
        }
    };
    
    public MemoryOptimizedEmbeddingService() {
        this.gpuMemorySemaphore = new Semaphore(1); // 同时只允许一个ERNIE调用
    }
    
    /**
     * 获取文本向量（显存优化版）
     */
    public float[] getTextEmbedding(String text) {
        // 1. 检查缓存
        if (enableCache) {
            String cacheKey = generateCacheKey(text);
            synchronized (embeddingCache) {
                if (embeddingCache.containsKey(cacheKey)) {
                    log.debug("命中embedding缓存: {}", text.substring(0, Math.min(20, text.length())));
                    return embeddingCache.get(cacheKey);
                }
            }
        }
        
        // 2. 选择embedding策略
        float[] embedding;
        if (enableOllama && shouldUseAdvancedEmbedding(text)) {
            // 使用BGE-M3
            embedding = getOllamaEmbeddingWithMemoryControl(text);
        } else {
            // 降级到轻量级
            embedding = lightweightService.getTextEmbedding(text);
        }
        
        // 3. 缓存结果
        if (enableCache && embedding != null) {
            String cacheKey = generateCacheKey(text);
            synchronized (embeddingCache) {
                embeddingCache.put(cacheKey, embedding);
            }
        }
        
        return embedding;
    }
    
    /**
     * 批量获取文本向量
     */
    public Map<String, float[]> getBatchTextEmbeddings(List<String> texts) {
        Map<String, float[]> results = new HashMap<>();
        
        // 1. 检查缓存
        List<String> uncachedTexts = new ArrayList<>();
        if (enableCache) {
            synchronized (embeddingCache) {
                for (String text : texts) {
                    String cacheKey = generateCacheKey(text);
                    if (embeddingCache.containsKey(cacheKey)) {
                        results.put(text, embeddingCache.get(cacheKey));
                    } else {
                        uncachedTexts.add(text);
                    }
                }
            }
        } else {
            uncachedTexts.addAll(texts);
        }
        
        if (uncachedTexts.isEmpty()) {
            return results;
        }
        
        // 2. 批量处理未缓存的文本
        if (enableOllama) {
            Map<String, float[]> batchResults = getBatchOllamaEmbeddings(uncachedTexts);
            results.putAll(batchResults);
        } else {
            // 使用轻量级服务逐个处理
            for (String text : uncachedTexts) {
                results.put(text, lightweightService.getTextEmbedding(text));
            }
        }
        
        // 3. 更新缓存
        if (enableCache) {
            synchronized (embeddingCache) {
                for (Map.Entry<String, float[]> entry : results.entrySet()) {
                    String cacheKey = generateCacheKey(entry.getKey());
                    embeddingCache.put(cacheKey, entry.getValue());
                }
            }
        }
        
        return results;
    }
    

    
    /**
     * 批量Ollama BGE-M3调用
     */
    private Map<String, float[]> getBatchOllamaEmbeddings(List<String> texts) {
        try {
            log.debug("批量调用Ollama BGE-M3服务，文本数量: {}", texts.size());

            // 直接使用Ollama服务的批量处理能力
            return ollamaService.getBatchEmbeddings(texts);

        } catch (Exception e) {
            log.error("Ollama批量调用失败，降级到逐个处理", e);

            // 降级到逐个处理
            Map<String, float[]> results = new HashMap<>();
            for (String text : texts) {
                try {
                    results.put(text, ollamaService.getTextEmbedding(text));
                } catch (Exception ex) {
                    log.warn("单个Ollama调用失败，使用轻量级方案: {}", text.substring(0, Math.min(30, text.length())));
                    results.put(text, lightweightService.getTextEmbedding(text));
                }
            }

            return results;
        }
    }


    
    /**
     * 检查是否应该使用高级embedding（BGE-M3优先）
     */
    private boolean shouldUseAdvancedEmbedding(String text) {
        // 对于重要的档案要素，使用BGE-M3获得更好的语义理解
        String[] importantKeywords = {"题名", "标题", "责任者", "发文单位", "文号", "发文日期"};

        for (String keyword : importantKeywords) {
            if (text.contains(keyword)) {
                return true;
            }
        }

        // 对于较长的文本，使用BGE-M3
        return text.length() > 30;
    }



    /**
     * 显存控制的Ollama BGE-M3调用
     */
    private float[] getOllamaEmbeddingWithMemoryControl(String text) {
        try {
            // BGE-M3显存占用相对较小，但仍需要控制
            if (!gpuMemorySemaphore.tryAcquire(30, TimeUnit.SECONDS)) {
                log.warn("获取GPU显存权限超时，降级到轻量级方案");
                return lightweightService.getTextEmbedding(text);
            }

            try {
                // 检查显存使用情况
                if (!checkGpuMemoryAvailable()) {
                    log.warn("GPU显存不足，降级到轻量级方案");
                    return lightweightService.getTextEmbedding(text);
                }

                // 调用Ollama BGE-M3服务
                return ollamaService.getTextEmbedding(text);

            } finally {
                gpuMemorySemaphore.release();
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Ollama BGE-M3调用被中断", e);
            return lightweightService.getTextEmbedding(text);
        } catch (Exception e) {
            log.error("Ollama BGE-M3调用失败", e);
            return lightweightService.getTextEmbedding(text);
        }
    }
    
    /**
     * 检查GPU显存是否可用
     */
    private boolean checkGpuMemoryAvailable() {
        try {
            // 这里可以调用GPU监控API
            // 简化实现：假设显存总是可用的
            return true;
        } catch (Exception e) {
            log.error("检查GPU显存失败", e);
            return false;
        }
    }
    

    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(String text) {
        return String.valueOf(text.hashCode());
    }
    

    
    /**
     * 清理缓存
     */
    public void clearCache() {
        synchronized (embeddingCache) {
            embeddingCache.clear();
        }
        log.info("Embedding缓存已清理");
    }
    
    /**
     * 获取缓存统计
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        synchronized (embeddingCache) {
            stats.put("cacheSize", embeddingCache.size());
            stats.put("maxCacheSize", cacheMaxSize);
            stats.put("cacheEnabled", enableCache);
        }
        stats.put("ollamaEnabled", enableOllama);
        stats.put("batchSize", batchSize);

        // 添加Ollama服务状态
        if (enableOllama) {
            try {
                Map<String, Object> ollamaStats = ollamaService.getCacheStats();
                stats.put("ollamaStats", ollamaStats);
            } catch (Exception e) {
                stats.put("ollamaStatus", "ERROR: " + e.getMessage());
            }
        }

        return stats;
    }
}
