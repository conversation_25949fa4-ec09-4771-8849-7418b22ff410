# 智能信息提取服务 - 完整Pipeline测试方案

## 🎯 测试目标

测试从图片上传到档案要素提取的完整流程，验证：
- FastAPI服务启动和健康状态
- 图片上传和OCR识别
- LLM档案要素提取（Qwen3:4b + Qwen3-Embedding-4B）
- 响应时间和准确性
- 错误处理和异常情况

## 🏗️ 测试架构

```
测试图片 → FastAPI接口 → OCR识别 → LLM提取 → 返回结果
    ↓           ↓           ↓         ↓         ↓
  准备阶段    服务启动     文字识别   要素提取   结果验证
```

## 📋 测试前准备

### 1. 环境检查
```bash
# 确保在正确的conda环境中
conda activate py3.9_copy

# 检查关键依赖
python -c "import torch; print(f'PyTorch: {torch.__version__}, CUDA: {torch.cuda.is_available()}')"
python -c "import paddleocr; print('PaddleOCR: OK')"
python -c "import fastapi; print(f'FastAPI: {fastapi.__version__}')"
```

### 2. 启动Ollama服务
```bash
# 启动Ollama服务
ollama serve

# 验证模型可用性
ollama list
# 应该看到：
# - qwen3:4b
# - dengcao/qwen3-embedding-4b:q4_k_m
```

### 3. 准备测试图片
```bash
# 创建测试图片目录
mkdir -p intelligent-extraction/test_images

# 将您的真实档案图片放入此目录
# 支持格式：.jpg, .jpeg, .png, .bmp, .tiff
```

## 🚀 测试执行

### 方式一：完整自动化测试
```bash
cd intelligent-extraction
python src/test/test_complete_pipeline.py
```

**特点：**
- 自动启动/停止服务
- 完整的测试流程
- 生成详细测试报告
- 适合CI/CD集成

### 方式二：手动服务 + 交互测试
```bash
# 终端1：启动服务
cd intelligent-extraction
python start_service.py

# 终端2：运行测试客户端
cd intelligent-extraction
python test_client.py
```

**特点：**
- 手动控制服务启停
- 交互式测试界面
- 实时查看服务日志
- 适合开发调试

### 方式三：批量测试
```bash
cd intelligent-extraction
python test_client.py batch
```

**特点：**
- 批量处理test_images目录中的所有图片
- 统计成功率
- 适合大量数据验证

## 📊 测试接口说明

### 1. 健康检查
```http
GET http://localhost:8080/health
```

### 2. 系统状态
```http
GET http://localhost:8080/status
```

### 3. 档案要素提取
```http
POST http://localhost:8080/extract/archive
Content-Type: multipart/form-data

file: [图片文件]
sync: true
custom_keys: 题名,责任者,文号,发文日期  # 可选
```

### 4. 自定义字段提取
```http
POST http://localhost:8080/extract/upload
Content-Type: multipart/form-data

file: [图片文件]
key_list: 自定义字段1,自定义字段2
sync: true
```

## 📈 测试指标

### 性能指标
- **响应时间**: < 10秒（单页档案）
- **内存使用**: < 6GB GPU显存
- **并发能力**: 支持多个请求排队处理

### 准确性指标
- **OCR识别率**: > 95%（清晰文档）
- **要素提取准确率**: > 90%（标准格式档案）
- **字段完整性**: 4个核心字段全部提取

### 稳定性指标
- **服务可用性**: > 99%
- **错误处理**: 优雅处理各种异常情况
- **资源清理**: 无内存泄漏

## 🔍 测试用例设计

### 标准测试用例
1. **标准公文档案**
   - 格式规范的政府公文
   - 包含完整的题名、责任者、文号、发文日期

2. **复杂格式档案**
   - 非标准格式
   - 字段位置不规律
   - 测试模型的适应性

3. **边界情况测试**
   - 模糊图片
   - 缺失字段
   - 异常格式

### 错误处理测试
1. **无效文件格式**
2. **文件过大**
3. **网络超时**
4. **服务异常**

## 📄 测试报告

测试完成后会生成JSON格式的详细报告：

```json
{
  "total_tests": 5,
  "passed_tests": 4,
  "failed_tests": 1,
  "success_rate": 0.8,
  "test_details": [
    {
      "test_name": "health_check",
      "success": true,
      "response_time": 0.05
    },
    {
      "test_name": "archive_extraction",
      "success": true,
      "response_time": 8.32,
      "extracted_fields": {
        "题名": "关于加强档案管理工作的通知",
        "责任者": "某某单位",
        "文号": "某字〔2024〕001号",
        "发文日期": "2024-01-15"
      },
      "accuracy": 0.95
    }
  ]
}
```

## 🐛 常见问题排查

### 1. 服务启动失败
```bash
# 检查端口占用
netstat -an | findstr :8080

# 检查Ollama服务
curl http://localhost:11434/api/tags

# 检查GPU状态
nvidia-smi
```

### 2. 模型加载失败
```bash
# 重新下载模型
ollama pull qwen3:4b
ollama pull dengcao/qwen3-embedding-4b:q4_k_m

# 检查模型文件
ollama list
```

### 3. 提取结果不准确
- 检查图片质量和清晰度
- 验证OCR识别结果
- 调整提取参数

### 4. 响应时间过长
- 检查GPU使用情况
- 优化图片大小
- 调整模型参数

## 📞 技术支持

如果测试过程中遇到问题，请提供：
1. 错误日志
2. 测试图片（脱敏后）
3. 系统环境信息
4. 测试报告文件

---

**注意事项：**
- 测试图片请确保已脱敏处理
- 首次运行可能需要下载模型，耗时较长
- 建议在GPU环境下进行测试以获得最佳性能
