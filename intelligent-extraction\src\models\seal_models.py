#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
印章检测模型模块
"""
import logging
import time
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

# 延迟导入，避免与其他PaddlePaddle模块冲突
SEAL_DETECTION_AVAILABLE = None  # 延迟检查
SealTextDetection = None

def _check_seal_detection_availability():
    """检查印章检测功能是否可用"""
    global SEAL_DETECTION_AVAILABLE, SealTextDetection

    if SEAL_DETECTION_AVAILABLE is not None:
        return SEAL_DETECTION_AVAILABLE

    try:
        from paddleocr import SealTextDetection as _SealTextDetection
        SealTextDetection = _SealTextDetection
        SEAL_DETECTION_AVAILABLE = True
        return True
    except ImportError as e:
        SEAL_DETECTION_AVAILABLE = False
        return False


@dataclass
class SealDetectionResult:
    """印章检测结果"""
    detected_stamps: int
    stamp_regions: List[Dict[str, Any]]
    detection_time: float
    confidence_threshold: float


class SealDetectionModel:
    """印章检测模型封装"""
    
    def __init__(self, model_name: str = "PP-OCRv4_mobile_seal_det", **kwargs):
        self.logger = logging.getLogger(__name__)
        self.model_name = model_name
        self.model = None
        self.is_loaded = False
        
        # 模型参数
        self.confidence_threshold = kwargs.get('confidence_threshold', 0.8)

        # 检查印章检测功能可用性
        if not _check_seal_detection_availability():
            self.logger.warning("PaddleOCR印章检测功能不可用，请检查安装")
    
    def load_model(self) -> bool:
        """加载印章检测模型"""
        if self.is_loaded:
            return True
            
        if not _check_seal_detection_availability():
            self.logger.error("PaddleOCR印章检测功能不可用")
            return False
        
        try:
            self.logger.info(f"正在加载印章检测模型: {self.model_name}")
            # 强制使用CPU模式避免CUDA依赖问题
            self.model = SealTextDetection(
                model_name=self.model_name,
                device='cpu'  # 强制使用CPU
            )
            self.is_loaded = True
            self.logger.info(f"印章检测模型加载成功 (CPU模式): {self.model_name}")
            return True
        except Exception as e:
            self.logger.error(f"印章检测模型加载失败: {e}")
            # 如果还是失败，尝试不指定device参数
            try:
                self.logger.info(f"尝试默认设备加载印章检测模型: {self.model_name}")
                self.model = SealTextDetection(model_name=self.model_name)
                self.is_loaded = True
                self.logger.info(f"印章检测模型加载成功 (默认设备): {self.model_name}")
                return True
            except Exception as e2:
                self.logger.error(f"印章检测模型加载最终失败: {e2}")
                return False
    
    def detect_seals(self, image, confidence_threshold: Optional[float] = None) -> SealDetectionResult:
        """检测印章区域"""
        if not self.is_loaded:
            if not self.load_model():
                return SealDetectionResult(0, [], 0.0, self.confidence_threshold)
        
        threshold = confidence_threshold or self.confidence_threshold
        
        try:
            detection_start = time.time()
            stamp_result = self.model.predict(image)
            detection_time = time.time() - detection_start
            
            # 解析检测结果
            stamp_regions = []
            if stamp_result and len(stamp_result) > 0:
                result_data = stamp_result[0]
                dt_polys = result_data.get('dt_polys', [])
                dt_scores = result_data.get('dt_scores', [])
                
                for poly, score in zip(dt_polys, dt_scores):
                    if score >= threshold:
                        stamp_regions.append({
                            'polygon': poly.tolist() if hasattr(poly, 'tolist') else poly,
                            'confidence': float(score)
                        })
            
            return SealDetectionResult(
                detected_stamps=len(stamp_regions),
                stamp_regions=stamp_regions,
                detection_time=detection_time,
                confidence_threshold=threshold
            )
            
        except Exception as e:
            self.logger.error(f"印章检测失败: {e}")
            return SealDetectionResult(0, [], 0.0, threshold)
    
    def unload_model(self):
        """卸载模型"""
        if self.model:
            del self.model
            self.model = None
            self.is_loaded = False
            self.logger.info(f"印章检测模型已卸载: {self.model_name}")


def create_seal_detection_model(config: Dict[str, Any]) -> Optional[SealDetectionModel]:
    """创建印章检测模型实例"""
    if not config.get('enabled', False):
        return None
    
    model_name = config.get('model_name', 'PP-OCRv4_mobile_seal_det')
    confidence_threshold = config.get('confidence_threshold', 0.8)
    
    return SealDetectionModel(
        model_name=model_name,
        confidence_threshold=confidence_threshold
    )
