#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的日志测试脚本 - 用于诊断实际运行时的日志问题
"""
import logging
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def quick_log_test():
    """快速日志测试"""
    print("快速日志测试开始...")
    
    # 1. 导入并初始化日志配置
    from src.utils.logger_config import setup_logging
    setup_logging(level="INFO", force_reconfigure=True)
    
    # 2. 测试不同模块的日志
    modules_to_test = [
        'src.services.archive_extraction_service',
        'src.models.llm_models',
        'src.core.model_pool'
    ]
    
    print("\n测试各模块日志输出:")
    for module_name in modules_to_test:
        logger = logging.getLogger(module_name)
        print(f"\n模块: {module_name}")
        print(f"级别: {logging.getLevelName(logger.getEffectiveLevel())}")
        
        # 测试输出
        logger.info(f"[{module_name}] INFO测试消息")
        logger.warning(f"[{module_name}] WARNING测试消息") 
        logger.error(f"[{module_name}] ERROR测试消息")
    
    print("\n如果您看到了上面的INFO消息，说明日志配置正常")
    print("如果只看到WARNING和ERROR消息，说明INFO级别被过滤了")

if __name__ == "__main__":
    quick_log_test()
