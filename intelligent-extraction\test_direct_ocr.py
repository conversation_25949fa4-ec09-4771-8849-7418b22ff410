#!/usr/bin/env python3
"""
直接测试OCR功能，不依赖Web服务
"""
import sys
import os
from pathlib import Path

# 添加项目路径
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

def test_direct_ocr():
    """直接测试OCR功能"""
    try:
        print("=== 直接测试OCR功能 ===")
        
        # 导入PPOCRv3Model
        from src.models.ocr_models import PPOCRv3Model
        
        print("✅ 成功导入PPOCRv3Model")
        
        # 创建模型实例
        model = PPOCRv3Model(device_id=0)
        print("✅ 成功创建PPOCRv3Model实例")
        
        # 检查是否有现有的测试图像
        test_images = []
        
        # 查找uploads目录中的图像
        uploads_dir = Path("uploads")
        if uploads_dir.exists():
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                test_images.extend(uploads_dir.glob(f"*{ext}"))
        
        # 如果没有找到图像，创建一个测试图像
        if not test_images:
            print("未找到现有图像，创建测试图像...")
            import numpy as np
            import cv2
            
            # 创建一个更复杂的测试图像
            img = np.ones((400, 800, 3), dtype=np.uint8) * 255  # 白色背景
            
            # 添加标题
            cv2.putText(img, 'ARCHIVE DOCUMENT', (200, 80), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 3)
            
            # 添加档案元素
            cv2.putText(img, 'Title: Test Archive Document', (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
            cv2.putText(img, 'Responsible: Test Organization', (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
            cv2.putText(img, 'Document No: TEST-2025-001', (50, 250), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
            cv2.putText(img, 'Issue Date: 2025-06-25', (50, 300), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
            
            # 添加一些装饰线条
            cv2.line(img, (50, 120), (750, 120), (0, 0, 0), 2)
            cv2.line(img, (50, 320), (750, 320), (0, 0, 0), 2)
            
            test_image_path = "test_direct_ocr.jpg"
            cv2.imwrite(test_image_path, img)
            test_images = [Path(test_image_path)]
            print(f"✅ 创建测试图像: {test_image_path}")
        
        # 测试每个图像
        for i, image_path in enumerate(test_images[:3]):  # 最多测试3个图像
            print(f"\n--- 测试图像 {i+1}: {image_path.name} ---")
            
            try:
                # 进行OCR识别
                print("开始OCR识别...")
                result = model.recognize(str(image_path), confidence_threshold=0.3)
                
                print(f"✅ OCR识别完成!")
                print(f"识别到 {len(result.text_blocks)} 个文本块")
                print(f"处理时间: {result.processing_time:.2f}秒")
                
                if result.text_blocks:
                    print("\n识别到的文本:")
                    for j, block in enumerate(result.text_blocks):
                        print(f"  {j+1}. '{block.text}' (置信度: {block.confidence:.2f})")
                
                print(f"\n完整文本:\n{result.full_text}")
                
                # 测试档案元素提取
                print("\n--- 测试档案元素提取 ---")
                from src.core.extraction_engine import ExtractionEngine
                from src.core.model_manager import ModelManager
                
                # 创建模型管理器和提取引擎
                config = {
                    'models': {
                        'ocr': {
                            'model_name': 'PPOCRv3',
                            'config': {}
                        }
                    },
                    'device': {
                        'force_mode': 'single'
                    }
                }
                
                model_manager = ModelManager(config)
                extraction_engine = ExtractionEngine(model_manager, config)
                
                # 进行档案元素提取
                archive_result = extraction_engine.extract_archive_elements(str(image_path))
                
                print(f"档案元素提取结果:")
                print(f"  题名: {archive_result.get('题名', 'N/A')}")
                print(f"  责任者: {archive_result.get('责任者', 'N/A')}")
                print(f"  文号: {archive_result.get('文号', 'N/A')}")
                print(f"  发文日期: {archive_result.get('发文日期', 'N/A')}")
                
            except Exception as e:
                print(f"❌ 处理图像 {image_path.name} 时出错: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n🎉 直接OCR测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始直接OCR测试...")
    success = test_direct_ocr()
    
    if success:
        print("\n✅ 测试成功!")
    else:
        print("\n❌ 测试失败")
