#!/usr/bin/env python3
"""
OCR结果详细诊断脚本
分析PaddleOCR 3.0.2在CPU和GPU模式下的结果差异
"""
import os
import sys
import time
import json
from pathlib import Path

# RTX 3060优化配置
os.environ['CUDA_VISIBLE_DEVICES'] = '0'
os.environ['FLAGS_allocator_strategy'] = 'auto_growth'
os.environ['FLAGS_fraction_of_gpu_memory_to_use'] = '0.7'
os.environ['FLAGS_eager_delete_tensor_gb'] = '0.0'
os.environ['FLAGS_fast_eager_deletion_mode'] = 'true'
os.environ['PADDLE_DISABLE_STATIC'] = '1'
os.environ['FLAGS_use_cuda'] = '1'
os.environ['FLAGS_cudnn_deterministic'] = '0'

def find_test_image():
    """查找测试图像"""
    possible_paths = [
        "uploads/125ef0ca-61e6-4cf5-9845-e8b9f3bd1358.jpg",
        "Image_00002.jpg",
        "test_medium_image.jpg"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            file_size = os.path.getsize(path) / 1024  # KB
            print(f"✅ 找到测试图像: {path} ({file_size:.1f}KB)")
            return path
    
    print("❌ 未找到测试图像")
    return None

def analyze_ocr_result(result, mode_name):
    """详细分析OCR结果"""
    print(f"\n🔍 分析{mode_name}OCR结果:")
    print(f"  结果类型: {type(result)}")
    
    if result is None:
        print("  ❌ 结果为None")
        return 0
    
    # 检查是否是列表
    if isinstance(result, list):
        print(f"  📊 列表长度: {len(result)}")
        
        for i, item in enumerate(result[:3]):  # 只显示前3个
            print(f"  项目{i+1}: {type(item)}")
            if isinstance(item, list) and len(item) >= 2:
                bbox = item[0]
                text_info = item[1]
                print(f"    边界框: {type(bbox)} - {bbox if len(str(bbox)) < 100 else '...'}")
                print(f"    文本信息: {type(text_info)} - {text_info}")
            else:
                print(f"    内容: {str(item)[:100]}...")
        
        return len(result)
    
    # 检查是否是字典
    elif isinstance(result, dict):
        print(f"  📊 字典键: {list(result.keys())}")
        
        # 常见的键名
        common_keys = ['dt_polys', 'rec_texts', 'rec_scores', 'results']
        for key in common_keys:
            if key in result:
                value = result[key]
                print(f"  {key}: {type(value)} - 长度: {len(value) if hasattr(value, '__len__') else 'N/A'}")
                if hasattr(value, '__len__') and len(value) > 0:
                    print(f"    示例: {str(value[0])[:100]}...")
        
        # 尝试获取文本数量
        rec_texts = result.get('rec_texts', [])
        return len(rec_texts) if rec_texts else 0
    
    # 检查是否有特殊属性
    elif hasattr(result, '__dict__'):
        print(f"  📊 对象属性: {list(result.__dict__.keys())}")
        return 0
    
    else:
        print(f"  📊 其他类型: {str(result)[:200]}...")
        return 0

def test_ocr_detailed(device_mode):
    """详细测试OCR"""
    print(f"\n{'='*50}")
    print(f"🔍 详细测试 {device_mode.upper()} 模式")
    print(f"{'='*50}")
    
    # 查找测试图像
    test_image = find_test_image()
    if not test_image:
        return False, None, 0
    
    try:
        from paddleocr import PaddleOCR
        
        # 初始化OCR
        print(f"⚡ 初始化{device_mode.upper()}模式OCR...")
        start_time = time.time()
        
        if device_mode == 'cpu':
            ocr = PaddleOCR(
                use_textline_orientation=True,
                lang='ch',
                device='cpu'
            )
        else:  # gpu
            ocr = PaddleOCR(
                use_textline_orientation=True,
                lang='ch',
                device='gpu:0'
            )
        
        init_time = time.time() - start_time
        print(f"✅ 初始化成功，耗时: {init_time:.2f}s")
        
        # 执行OCR
        print(f"📄 处理图像: {test_image}")
        start_time = time.time()
        
        # 使用predict方法（PaddleOCR 3.0.2推荐）
        result = ocr.predict(test_image)
        
        process_time = time.time() - start_time
        print(f"✅ 处理完成，耗时: {process_time:.2f}s")
        
        # 详细分析结果
        text_count = analyze_ocr_result(result, device_mode.upper())
        
        return True, result, text_count
        
    except Exception as e:
        print(f"❌ {device_mode.upper()}模式测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False, None, 0

def compare_results(cpu_result, gpu_result):
    """比较CPU和GPU结果"""
    print(f"\n{'='*50}")
    print("🔄 CPU vs GPU 结果对比")
    print(f"{'='*50}")
    
    if cpu_result is None or gpu_result is None:
        print("❌ 无法比较，某个结果为None")
        return
    
    print(f"CPU结果类型: {type(cpu_result)}")
    print(f"GPU结果类型: {type(gpu_result)}")
    
    # 如果都是列表，比较长度和内容
    if isinstance(cpu_result, list) and isinstance(gpu_result, list):
        print(f"CPU检测数量: {len(cpu_result)}")
        print(f"GPU检测数量: {len(gpu_result)}")
        
        if len(cpu_result) != len(gpu_result):
            print("⚠️ 检测数量不一致！")
        
        # 比较前几个结果
        min_len = min(len(cpu_result), len(gpu_result), 3)
        for i in range(min_len):
            print(f"\n项目{i+1}比较:")
            cpu_item = cpu_result[i]
            gpu_item = gpu_result[i]
            
            if isinstance(cpu_item, list) and isinstance(gpu_item, list):
                if len(cpu_item) >= 2 and len(gpu_item) >= 2:
                    cpu_text = cpu_item[1][0] if isinstance(cpu_item[1], (list, tuple)) else cpu_item[1]
                    gpu_text = gpu_item[1][0] if isinstance(gpu_item[1], (list, tuple)) else gpu_item[1]
                    print(f"  CPU文本: {cpu_text}")
                    print(f"  GPU文本: {gpu_text}")
                    print(f"  文本一致: {'✅' if cpu_text == gpu_text else '❌'}")

def main():
    """主函数"""
    print("🔬 OCR结果详细诊断")
    print("=" * 60)
    
    # 测试CPU模式
    cpu_success, cpu_result, cpu_count = test_ocr_detailed('cpu')
    
    # 测试GPU模式
    gpu_success, gpu_result, gpu_count = test_ocr_detailed('gpu')
    
    # 比较结果
    if cpu_success and gpu_success:
        compare_results(cpu_result, gpu_result)
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 诊断总结:")
    print(f"  CPU模式: {'✅' if cpu_success else '❌'} - 检测到{cpu_count}个文本块")
    print(f"  GPU模式: {'✅' if gpu_success else '❌'} - 检测到{gpu_count}个文本块")
    
    if cpu_success and gpu_success:
        if cpu_count == gpu_count:
            print("✅ CPU和GPU检测数量一致")
        else:
            print(f"⚠️ 检测数量不一致: CPU={cpu_count}, GPU={gpu_count}")
            print("💡 建议检查GPU模式的结果解析逻辑")

if __name__ == "__main__":
    main()
