#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GPU vs CPU性能对比
"""

import os
import sys
import time
import json
import tempfile
import subprocess

def test_ocr_performance(use_cpu=True, gpu_id=0):
    """测试OCR性能"""
    print(f"=== 测试 {'CPU' if use_cpu else f'GPU {gpu_id}'} 模式 ===")
    
    # 测试图片路径
    test_image = "uploads/125ef0ca-61e6-4cf5-9845-e8b9f3bd1358.jpg"
    
    if not os.path.exists(test_image):
        print(f"❌ 测试图片不存在: {test_image}")
        return None
    
    # 创建临时配置文件
    config = {
        "image_path": os.path.abspath(test_image),
        "use_cpu": use_cpu,
        "gpu_id": gpu_id,
        "use_angle_cls": True,
        "lang": "ch",
        "confidence_threshold": 0.5
    }
    
    # 写入临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
        config_file = f.name
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 直接调用OCR worker脚本 - 使用conda环境的Python
        worker_script = "src/models/ocr_worker.py"
        python_exe = r"C:\ProgramData\Anaconda3\envs\py39ocr\python.exe"
        cmd = [python_exe, worker_script, config_file]
        
        print(f"执行命令: {' '.join(cmd)}")
        print(f"配置: {'CPU' if use_cpu else f'GPU {gpu_id}'}")
        
        # 运行worker脚本
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            cwd=os.getcwd()
        )
        
        # 记录结束时间
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"总耗时: {total_time:.2f}秒")
        print(f"返回码: {result.returncode}")
        
        if result.returncode == 0 and result.stdout:
            try:
                # 解析JSON输出
                output_data = json.loads(result.stdout)
                
                if output_data.get('success'):
                    results = output_data.get('results', [])
                    print(f"✅ OCR成功，识别到 {len(results)} 个文本块")
                    
                    # 显示识别结果
                    for i, item in enumerate(results):
                        print(f"  文本块{i+1}: '{item.get('text', '')}' (置信度: {item.get('confidence', 0):.3f})")
                    
                    return {
                        'success': True,
                        'mode': 'CPU' if use_cpu else f'GPU {gpu_id}',
                        'time': total_time,
                        'text_blocks': len(results),
                        'results': results
                    }
                else:
                    print(f"❌ OCR失败: {output_data.get('error', '未知错误')}")
                    return {
                        'success': False,
                        'mode': 'CPU' if use_cpu else f'GPU {gpu_id}',
                        'time': total_time,
                        'error': output_data.get('error', '未知错误')
                    }
                    
            except json.JSONDecodeError:
                print("❌ 输出解析失败")
                print("原始输出:", result.stdout)
                return None
        else:
            print(f"❌ 执行失败，返回码: {result.returncode}")
            if result.stderr:
                print("错误信息:", result.stderr)
            return None
        
    finally:
        # 清理临时文件
        try:
            os.unlink(config_file)
        except:
            pass

def main():
    """主函数 - 性能对比测试"""
    print("🚀 OCR性能对比测试")
    print("=" * 50)
    
    # 测试CPU模式
    cpu_result = test_ocr_performance(use_cpu=True)
    print()
    
    # 测试GPU 0模式
    gpu0_result = test_ocr_performance(use_cpu=False, gpu_id=0)
    print()
    
    # 性能对比
    if cpu_result and gpu0_result and cpu_result['success'] and gpu0_result['success']:
        print("📊 性能对比结果:")
        print(f"CPU模式:  {cpu_result['time']:.2f}秒")
        print(f"GPU模式:  {gpu0_result['time']:.2f}秒")
        
        if cpu_result['time'] > gpu0_result['time']:
            speedup = cpu_result['time'] / gpu0_result['time']
            print(f"🎉 GPU加速比: {speedup:.2f}x")
        else:
            slowdown = gpu0_result['time'] / cpu_result['time']
            print(f"⚠️  GPU比CPU慢: {slowdown:.2f}x")
    else:
        print("❌ 无法进行性能对比，某个测试失败")

if __name__ == "__main__":
    main()
