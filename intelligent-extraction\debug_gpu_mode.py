#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深入调试GPU模式OCR问题
"""
import os
import sys
import json
import tempfile
from pathlib import Path

# 强制使用单GPU
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

def debug_gpu_ocr():
    """调试GPU模式OCR"""
    print("🔍 深入调试GPU模式OCR问题")
    print("=" * 60)
    
    # 测试图片路径
    test_image = Path("test_image.png")
    if not test_image.exists():
        print(f"❌ 测试图片不存在: {test_image}")
        return
    
    print(f"📷 测试图片: {test_image}")
    print(f"📏 图片大小: {test_image.stat().st_size} bytes")
    
    # 创建临时配置文件
    config = {
        'image_path': str(test_image.absolute()),
        'use_cpu': False,  # 关键：必须设置为False才能启用GPU
        'enable_gpu': True,  # 显式启用GPU
        'gpu_id': 0,
        'use_angle_cls': False,
        'lang': 'ch',
        'text_detection_model_name': 'PP-OCRv5_server_det',
        'text_recognition_model_name': 'PP-OCRv5_server_rec',
        'det_db_thresh': 0.3,
        'det_db_box_thresh': 0.6,
        'rec_batch_num': 6,
        'confidence_threshold': 0.5,
        'debug_mode': True  # 启用调试模式
    }
    
    print("\n🔧 配置信息:")
    for key, value in config.items():
        print(f"   {key}: {value}")
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', 
                                   delete=False, encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
        config_file = f.name
    
    try:
        print(f"\n📝 配置文件: {config_file}")
        
        # 直接调用OCR worker进行调试
        print("\n🚀 启动GPU模式OCR调试...")
        
        # 使用subprocess调用worker
        import subprocess
        
        cmd = [
            sys.executable, 
            "src/models/ocr_worker.py", 
            config_file
        ]
        
        print(f"📋 执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            cwd=Path.cwd()
        )
        
        print(f"\n📤 返回码: {result.returncode}")
        
        if result.stdout:
            print("\n📤 标准输出:")
            print(result.stdout)
            
            # 尝试解析JSON结果
            try:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.startswith('{') and line.endswith('}'):
                        ocr_result = json.loads(line)
                        print("\n🔍 OCR结果分析:")
                        print(f"   成功: {ocr_result.get('success', False)}")
                        print(f"   处理时间: {ocr_result.get('processing_time', 0):.2f}秒")
                        
                        if 'result' in ocr_result:
                            result_data = ocr_result['result']
                            print(f"   检测到文本块: {len(result_data)}个")
                            
                            if len(result_data) == 0:
                                print("   ⚠️  GPU模式未检测到任何文本块!")
                                
                                # 检查原始OCR数据
                                if 'debug_info' in ocr_result:
                                    debug_info = ocr_result['debug_info']
                                    print("\n🔍 调试信息:")
                                    for key, value in debug_info.items():
                                        print(f"   {key}: {value}")
                            else:
                                for i, item in enumerate(result_data):
                                    text = item.get('text', '')
                                    confidence = item.get('confidence', 0)
                                    print(f"   块{i+1}: {text} (置信度: {confidence:.3f})")
                        break
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
        
        if result.stderr:
            print("\n📤 标准错误:")
            print(result.stderr)
            
    finally:
        # 清理临时文件
        try:
            os.unlink(config_file)
        except:
            pass

if __name__ == "__main__":
    debug_gpu_ocr()
